package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.util.StrUtil

/**
 *
 * 是否能够查询到Leader节点信息
 *
 */
static def main(InspectionContext inspectionContext, ParamContext paramContext, MeterReportBo meterReportBo) {
    try {
        String leader = inspectionContext.getRegistryService().leader()
        if (StrUtil.isNotEmpty(leader)) {
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY)
            meterReportBo.setDetails("Leader正常:${leader}")
        } else {
            meterReportBo.setStatus(InspectionMeterStatus.DANGER)
            meterReportBo.setDetails("无Leader节点")
        }
    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.DANGER)
        meterReportBo.setDetails("获取Leader节点异常:${e.getMessage()}")
    }
    return meterReportBo
}