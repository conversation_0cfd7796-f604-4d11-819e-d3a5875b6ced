package cn.huolala.arch.hermes.mesh.sentinel.groovy.soa

import cn.huolala.arch.hermes.mesh.api.bos.inspection.ItemReportBo
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.StrUtil

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    try {
        Map<String, String> appOperationsMap = inspectionContext.getGovernanceInfoQueryService().listAppOperationDetailByCreateTime(context.getStartTimeOrBefore1Hour(), context.getEndTimeOrNow());

        appOperationsMap.each { appId, operation ->
            String details = StrUtil.format("AppId:{},近期存在通过SOA Admin进行以下操作: {}", appId, operation)
            meterReportBo.putItems(ItemReportBo.of(appId, InspectionMeterStatus.WARNING, details))
        }

        if (CollUtil.isEmpty(meterReportBo.getItems())){
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("近期用户没有通过soa-admin变更"))
            return meterReportBo;
        }

        //当不存在danger状态时，返回正常
        for (final def item in meterReportBo.getItems()) {
            if (item.getStatus() == InspectionMeterStatus.WARNING) {
                meterReportBo.setStatus(InspectionMeterStatus.WARNING)
                meterReportBo.setDetails("近期用户存在通过soa-admin变更")
                return meterReportBo
            }
        }
    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.WARNING)
        meterReportBo.setDetails(StrUtil.format("查询soa-admin操作信息失败，异常信息:{}", e.getMessage()));
        return meterReportBo;
    }
}
