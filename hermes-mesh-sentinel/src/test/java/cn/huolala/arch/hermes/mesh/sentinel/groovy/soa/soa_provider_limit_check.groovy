package cn.huolala.arch.hermes.mesh.sentinel.groovy.soa

import cn.huolala.arch.hermes.mesh.api.bos.inspection.ItemReportBo
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.monitor.model.LabelTimeValue
import cn.huolala.arch.hermes.mesh.monitor.model.TimeValue
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.StrUtil

import java.util.function.Predicate

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    String sql = "sum(increase(hllci_provider_ratelimit_check_result_total{result=\"0\"}[1m])) BY (hll_appid,service_key) > 0";

    try {
        List<LabelTimeValue> metric = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabel(sql, context.getBeforeNMinSeconds(3), context.getEndTimeOrNow()).stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > 0D;
                    }
                }).findFirst().isPresent();
            }
        }).toList();
        Set<String> appCdkSet = inspectionContext.getGovernanceInfoQueryService().listApiLimitLogsByCreateTime(context.getStartTimeOrBefore1Hour(), context.getEndTimeOrNow());
        for (def cm in metric) {
            def max = cm.timeValues.stream().max(new Comparator<TimeValue>() {
                @Override
                int compare(TimeValue o1, TimeValue o2) {
                    return o2.value - o1.value ;
                }
            });
            def details = StrUtil.format("AppId:{},存在限流接口:{}，最大限流值:{}, 需关注!" ,cm.labels.get("hll_appid"),cm.labels.get("service_key"), max.get().value)
            if (appCdkSet.contains(cm.labels.get("hll_appid") + "-" + cm.labels.get("service_key"))) {
                details = details + "且用户有通过SOA-ADMIN操作限流配置。"
            }
            meterReportBo.putItems(ItemReportBo.of(cm.labels.get("hll_appid"), InspectionMeterStatus.WARNING, details))
        }

        if (CollUtil.isEmpty(meterReportBo.getItems())){
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("SOA Provider限流状态无异常"))
            return meterReportBo;
        }

        //当不存在danger状态时，返回正常
        for (final def item in meterReportBo.getItems()) {
            if (item.getStatus() == InspectionMeterStatus.WARNING) {
                meterReportBo.setStatus(InspectionMeterStatus.WARNING)
                meterReportBo.setDetails("当前时间点存在SOA Provider限流")
                return meterReportBo
            }
        }

    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.WARNING)
        meterReportBo.setDetails(StrUtil.format("MonitorMetric监控指标异常，无法从Monitor上获取监控指标，异常信息:{}", e.getMessage()));
        return meterReportBo;
    }
}
