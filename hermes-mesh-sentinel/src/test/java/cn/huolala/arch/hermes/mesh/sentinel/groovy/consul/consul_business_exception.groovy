package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.dao.entity.Pair
import cn.huolala.arch.hermes.mesh.monitor.model.LabelTimeValue
import cn.huolala.arch.hermes.mesh.monitor.model.TimeValue
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.StrUtil

import java.util.function.Function
import java.util.function.Predicate
import java.util.stream.Collectors
import java.util.stream.DoubleStream

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {

    int appIdNum = 5;
    int exceptionNum = 5;
    String sql = "sum(increase(hllci_exception_total{tag=~\"consul_exception|NotRegisteredException\"}[1m])) by(hll_appid, tag)";

    try {
        List<LabelTimeValue> metrics = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabel(sql, context.getEndTimeOrNow() - 1000, context.getEndTimeOrNow());
        //指标为空时说明服务正常
        if (CollUtil.isEmpty(metrics)){
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("Consul异常指标监控正常，不存在业务异常指标信息"));
            return meterReportBo;
        }
        def appIdLabelTimeValue = metrics.stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > 0;
                    }
                }).findFirst().isPresent();
            }
        }).toList();
        println appIdLabelTimeValue;

        def exceptionLabelTimeValue = appIdLabelTimeValue.stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > exceptionNum;
                    }
                }).findFirst().isPresent();
            }
        }).toList();
        println exceptionLabelTimeValue;


        //如果存在异常数量大于输入判定数量的appId数量，则认为consul 集群有异常
        if (exceptionLabelTimeValue.size() > appIdNum){
            def appIdMap = exceptionLabelTimeValue.stream().map(new Function<LabelTimeValue, Pair>() {
                @Override
                Pair apply(LabelTimeValue labelTimeValue) {
                    def count = labelTimeValue.getTimeValues().stream().flatMapToDouble(new Function<TimeValue, DoubleStream>() {
                        @Override
                        DoubleStream apply(TimeValue timeValue) {
                            return DoubleStream.of(timeValue.value);
                        }
                    }).count();
                    return Pair.of(labelTimeValue.labels.get("hll_appid"), String.valueOf(count), labelTimeValue.labels.get("tag"))
                }
            }).toList().stream().collect(Collectors.toMap(pair -> Pair.name, v -> v, (v1, v2) -> v1));
            meterReportBo.setStatus(InspectionMeterStatus.DANGER);
            meterReportBo.setDetails(StrUtil.format("Consul业务异常指标不健康，存在以下服务异常:{}",appIdMap));
            meterReportBo.setExtra(appIdMap);
            return meterReportBo;
        }

        //不存在此异常数量大于10的情况,但是可能存在少量的异常，
        if (appIdLabelTimeValue.size() > 0){

            def appIdMap = appIdLabelTimeValue.stream().map(new Function<LabelTimeValue, Pair>() {
                @Override
                Pair apply(LabelTimeValue labelTimeValue) {
                    def count = labelTimeValue.getTimeValues().stream().flatMapToDouble(new Function<TimeValue, DoubleStream>() {
                        @Override
                        DoubleStream apply(TimeValue timeValue) {
                            return DoubleStream.of(timeValue.value);
                        }
                    }).count();
                    return Pair.of(labelTimeValue.labels.get("hll_appid"), String.valueOf(count), labelTimeValue.labels.get("tag"))
                }
            }).toList().stream().collect(Collectors.toMap(pair -> Pair.name, v -> v, (v1, v2) -> v1));

            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("Consul业务异常监控指标存在少量业务异常，请人工确认是否存在问题:{}",appIdMap.keySet()));
            meterReportBo.setExtra(appIdMap);
            return meterReportBo;
        }

        meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
        meterReportBo.setDetails(StrUtil.format("Consul业务异常监控指标正常，不存在业务服务异常指标"));
        return meterReportBo;
    }catch (Exception e){
        meterReportBo.setStatus(InspectionMeterStatus.WARNING);
        meterReportBo.setDetails(StrUtil.format("执行ConsulException自检规则失败，失败原因:{}", e.getMessage()))
    }
    return meterReportBo;
}
