package cn.huolala.arch.hermes.mesh.sentinel.groovy.soa

import cn.huolala.arch.hermes.mesh.api.bos.inspection.ItemReportBo
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.monitor.model.LabelTimeValue
import cn.huolala.arch.hermes.mesh.monitor.model.TimeValue
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.StrUtil

import java.util.function.Predicate

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    String sql = "(sum(rate(hllci_soa_call_seconds_count{error=\"1\",command_key!=\"-\"}[1m])) by (hll_appid) / (sum(rate(hllci_soa_call_seconds_count{command_key!=\"-\"}[1m])) by (hll_appid) > 0) or vector(0)) > 0.20";
    try {
        List<LabelTimeValue> metric = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabel(sql, context.getBeforeNMinSeconds(3), context.getEndTimeOrNow()).stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > 0D;
                    }
                }).findFirst().isPresent();
            }
        }).toList();

        for (def cm in metric) {
            def details = StrUtil.format("AppId:{},异常请求大于20%, 需关注!" ,cm.labels.get("hll_appid"))
            meterReportBo.putItems(ItemReportBo.of(cm.labels.get("hll_appid"), InspectionMeterStatus.DANGER, details))
        }

        if (CollUtil.isEmpty(meterReportBo.getItems())){
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("SOA-App异常请求量检查正常"))
            return meterReportBo;
        }

        //当不存在danger状态时，返回正常
        for (final def item in meterReportBo.getItems()) {
            if (item.getStatus() == InspectionMeterStatus.DANGER) {
                meterReportBo.setStatus(InspectionMeterStatus.DANGER)
                meterReportBo.setDetails("SOA-App异常请求量小于20%")
                return meterReportBo
            }
        }

    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.WARNING)
        meterReportBo.setDetails(StrUtil.format("MonitorMetric监控指标异常，无法从Monitor上获取监控指标，异常信息:{}", e.getMessage()));
        return meterReportBo;
    }
}
