package cn.huolala.arch.hermes.mesh.sentinel.groovy.soa

import cn.huolala.arch.hermes.mesh.api.bos.inspection.ItemReportBo
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.monitor.model.LabelTimeValue
import cn.huolala.arch.hermes.mesh.monitor.model.TimeValue
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.StrUtil

import java.util.function.Predicate

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    String sql = "(histogram_quantile(0.99, sum(rate(hllci_soa_call_seconds_bucket[1m])) by (le)) / (histogram_quantile(0.99, sum(rate(hllci_soa_call_seconds_bucket[1m] offset 5m)) by (le)) > 0) or 1 ) > 1.15"

    try {
        List<LabelTimeValue> metric = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabel(sql, context.getBeforeNMinSeconds(3), context.getEndTimeOrNow()).stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > 0D;
                    }
                }).findFirst().isPresent();
            }
        }).toList();

        if (CollUtil.isNotEmpty(metric)){
            meterReportBo.putItems(ItemReportBo.of("soa_request_p99_change", InspectionMeterStatus.DANGER, "大盘请求P99短时间内上升超过15%，请关注！"))
            meterReportBo.setStatus(InspectionMeterStatus.DANGER)
            meterReportBo.setDetails("SOA-存在App请求qps短时间内下降超过50%！！！")
            return meterReportBo
        }

        meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
        meterReportBo.setDetails(StrUtil.format("SOA-App请求qps短时间内无较大下降"))
        return meterReportBo;

    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.WARNING)
        meterReportBo.setDetails(StrUtil.format("MonitorMetric监控指标异常，无法从Monitor上获取监控指标，异常信息:{}", e.getMessage()));
        return meterReportBo;
    }
}
