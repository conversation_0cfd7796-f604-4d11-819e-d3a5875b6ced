package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.date.DateUtil
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics

/**
 *
 * 计算逻辑
 * 1、当前3分钟的平均值  对比 1个小时前3分钟的平均值 的波动是否在正常波动范围内
 * 2、且当前3分钟的平均值不能大于告警阈值
 *
 */
static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {

    def sql = "avg(consul_runtime_num_goroutines) BY (host)"
    def threshold = 50000;
    def thresholdPrecent = 1.5;

    try {
        //获取1个小时前3分钟的数据
        def hostToLtv1h = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabelStep(sql, DateUtil.offsetMinute(new Date(), -63).getTime(), DateUtil.offsetMinute(new Date(), -60).getTime(), "30s").groupBy { labelTimeValue -> labelTimeValue.labels.get("host") };

        //获取当前3分钟的数据
        inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabelStep(sql, DateUtil.offsetMinute(new Date(), -3).getTime(), context.getEndTimeOrNow(), "30s").stream().forEach(labelTimeValue -> {
            def host = labelTimeValue.labels.get("host")
            def ltv3m = labelTimeValue.timeValues;
            def ltv1h = hostToLtv1h.get(host).stream().findFirst().get()

            DescriptiveStatistics stats3m = new DescriptiveStatistics();
            DescriptiveStatistics stats1h = new DescriptiveStatistics();
            ltv3m.stream().forEach { tv -> {
                    stats3m.addValue(tv.value)
                }
            }
            ltv1h.timeValues.stream().forEach { tv -> {
                    stats1h.addValue(tv.value)
                }
            }
            // 取整操作
            long mean3m = Math.round(stats3m.getMean());
            long mean1h = Math.round(stats1h.getMean());

            if (mean3m > mean1h * thresholdPrecent || mean3m * thresholdPrecent < mean3m || mean3m > threshold) {
                meterReportBo.putItems(MeterReportBo.of("${host}", InspectionMeterStatus.DANGER, "goroutines数量异常,当前值:${mean3m},1小时前值:${mean1h},波动值:${Math.round(((mean3m - mean1h) / mean1h.toDouble()) * 100)}%",
                        Map.of("ltv3m", ltv3m, "ltv1h", ltv1h, "stats3m", stats3m.toString(), "stats1h", stats1h.toString())))
            } else {
                meterReportBo.putItems(MeterReportBo.of("${host}", InspectionMeterStatus.HEALTHY, "goroutines数量正常,当前值:${mean3m},1小时前值:${mean1h},波动值:${Math.round(((mean3m - mean1h) / mean1h.toDouble()) * 100)}%",
                        Map.of("ltv3m", ltv3m, "ltv1h", ltv1h, "stats3m", stats3m.toString(), "stats1h", stats1h.toString())))
            }
        })

        //当不存在danger状态时，返回正常
        for (final def item in meterReportBo.getItems()) {
            if (item.getStatus() == InspectionMeterStatus.DANGER) {
                meterReportBo.setStatus(InspectionMeterStatus.DANGER)
                meterReportBo.setDetails("节点:${item.getName()},${item.getDetails()}")
                return meterReportBo
            }
        }

        meterReportBo.setStatus(InspectionMeterStatus.HEALTHY)
        meterReportBo.setDetails("所有节点goroutines数量正常,未有泄漏或者递增现象")
    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.DANGER)
        meterReportBo.setDetails("获取指标数据异常:${e.getMessage()}")
    }
    return meterReportBo
}