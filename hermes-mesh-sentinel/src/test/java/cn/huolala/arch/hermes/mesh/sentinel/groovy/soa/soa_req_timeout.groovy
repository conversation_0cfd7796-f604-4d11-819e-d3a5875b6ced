package cn.huolala.arch.hermes.mesh.sentinel.groovy.soa

import cn.huolala.arch.hermes.mesh.api.bos.inspection.ItemReportBo
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.component.apollo.service.ApolloFacadeConfigService
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService
import cn.huolala.arch.hermes.mesh.monitor.model.LabelTimeValue
import cn.huolala.arch.hermes.mesh.monitor.model.TimeValue
import cn.huolala.arch.hermes.mesh.monitor.service.VictoriaMetricService
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.huolala.arch.hermes.mesh.sentinel.service.RegistryService
import cn.huolala.arch.hermes.mesh.sentinel.service.rpc.RpcInspectionServiceImpl
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.lang.Opt
import cn.hutool.core.util.StrUtil

import java.util.function.Predicate

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    String sql = "(sum(increase(hllci_exception_total{tag=~\"TimeoutException|ConnectTimeoutException\", hll_appid!=\"risk-avira-data-svc\"}[1m])) by (hll_appid) / (sum(increase(hllci_soa_call_seconds_count[1m])) by (hll_appid)>0 or 1)) > 0.20";

    try {
        List<LabelTimeValue> metric = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabel(sql, context.getBeforeNMinSeconds(3), context.getEndTimeOrNow()).stream().filter(new Predicate<LabelTimeValue>() {
            @Override
            boolean test(LabelTimeValue labelTimeValue) {
                return labelTimeValue.timeValues.stream().filter(new Predicate<TimeValue>() {
                    @Override
                    boolean test(TimeValue timeValue) {
                        return timeValue.value > 0D;
                    }
                }).findFirst().isPresent();
            }
        }).toList();

        Set<String> appSet = inspectionContext.getGovernanceInfoQueryService().listAppTimeoutLogsByCreateTime(context.getStartTimeOrBefore1Hour(), context.getEndTimeOrNow());

        for (def cm in metric) {
            def details = StrUtil.format("AppId:{},超时请求大于20%, 需关注!" ,cm.labels.get("hll_appid"))
            if (appSet.contains(cm.labels.get("hll_appid"))){
                details = details + "且今天用户有通过SOA-ADMIN操作超时配置。"
            }
            meterReportBo.putItems(ItemReportBo.of(cm.labels.get("hll_appid"), InspectionMeterStatus.WARNING, details))
        }

        if (CollUtil.isEmpty(meterReportBo.getItems())){
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
            meterReportBo.setDetails(StrUtil.format("SOA-AppId超时请求小于20%"))
            return meterReportBo;
        }

        //当不存在danger状态时，返回正常
        for (final def item in meterReportBo.getItems()) {
            if (item.getStatus() == InspectionMeterStatus.WARNING) {
                meterReportBo.setStatus(InspectionMeterStatus.WARNING)
                meterReportBo.setDetails("当前时间点存在SOA APP超时请求数量大于20%！！！")
                return meterReportBo
            }
        }

    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.WARNING)
        meterReportBo.setDetails(StrUtil.format("MonitorMetric监控指标异常，无法从Monitor上获取监控指标，异常信息:{}", e.getMessage()));
        return meterReportBo;
    }
}
