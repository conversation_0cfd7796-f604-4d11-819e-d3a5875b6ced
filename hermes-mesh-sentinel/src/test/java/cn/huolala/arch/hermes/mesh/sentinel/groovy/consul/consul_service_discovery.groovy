package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.util.StrUtil
import com.orbitz.consul.model.health.ServiceHealth

import java.util.stream.Collectors

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    try {
        def healths = inspectionContext.getRegistryService().serviceHealths("ci-hermes-sentinel-svc")
        meterReportBo.setExtra(healths.stream().collect(Collectors.toMap({ ServiceHealth serviceHealth -> serviceHealth.getService().getId()},v ->v)));
        meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
        meterReportBo.setDetails(StrUtil.format("Consul集群正常，验证注册查询正常:{}"));
    }catch (Exception e){
        meterReportBo.setStatus(InspectionMeterStatus.DANGER);
        meterReportBo.setDetails(StrUtil.format("Consul集群异常，无法正常查询服务:{}",e.getMessage()));
    }
    return meterReportBo;
}
