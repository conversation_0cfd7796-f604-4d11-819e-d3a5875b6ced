package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext

/**
 * 计算逻辑
 * 1、检测Consul Server 集群节点的数量是否符合预期值
 *
 */
static def main(InspectionContext inspectionContext, ParamContext paramContext, MeterReportBo meterReportBo) {
    def expect = $${consul_cluster_expect_num};
    try {
        def state = inspectionContext.getRegistryService().autopilotState()
        def servers = state.getServers()
        def serverCount = servers.size()
        meterReportBo.setExtra(servers)
        if (serverCount != expect) {
            meterReportBo.setStatus(InspectionMeterStatus.DANGER)
            meterReportBo.setDetails("Server集群异常,Server数量:${serverCount} < 预期值${expect}")
            return meterReportBo
        }
    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.DANGER)
        meterReportBo.setDetails("获取Server集群异常:${e.getMessage()}")
    }
    // 设置正常状态和成功详情
    meterReportBo.setStatus(InspectionMeterStatus.HEALTHY)
    meterReportBo.setDetails("Server集群数量正常,当前值:${expect},预期值:${expect}")
    return meterReportBo
}
