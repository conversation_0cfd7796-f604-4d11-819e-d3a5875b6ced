package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.date.DateUtil
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics

/**
 *
 * 计算逻辑
 * 1、当前3分钟的平均值  对比 1个小时前3分钟的平均值 的波动是否在正常波动范围内
 * 2、且当前3分钟的平均值不能大于告警阈值
 *
 */
static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {

    def sql = "consul_raft_commitTime{quantile=\"0.9\"}"
    def threshold = 50;
    def thresholdCondition = 10;
    def thresholdPrecent = 2;

    try {
        //获取1个小时前3分钟的数据
        def ltv1h = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabelStep(sql, DateUtil.offsetMinute(new Date(), -63).getTime(), DateUtil.offsetMinute(new Date(), -60).getTime(), "30s").stream().findFirst().get();
        //获取当前5分钟的数据
        def ltv3m = inspectionContext.getVictoriaMetricService().queryRangeMetricAndLabelStep(sql,  DateUtil.offsetMinute(new Date(), -3).getTime(), context.getEndTimeOrNow(), "30s").stream().findFirst().get();

        DescriptiveStatistics stats3m = new DescriptiveStatistics();
        ltv3m.timeValues.stream().forEach { tv -> {
            stats3m.addValue(tv.value)
        }
        }
        DescriptiveStatistics stats1h = new DescriptiveStatistics();
        ltv1h.timeValues.stream().forEach { tv -> {
            stats1h.addValue(tv.value)
        }
        }
        // 取整操作
        long mean3m = Math.round(stats3m.getMean());
        long mean1h = Math.round(stats1h.getMean());
        meterReportBo.setExtra(Map.of("ltv3m", ltv3m, "ltv1h", ltv1h, "stats3m", stats3m.toString(), "stats1h", stats1h.toString()))

        if (mean3m > thresholdCondition && (mean3m > mean1h * thresholdPrecent || mean3m * thresholdPrecent < mean3m || mean3m > threshold)) {
            meterReportBo.setStatus(InspectionMeterStatus.WARNING)
            meterReportBo.setDetails("Raft Commit Time波动异常，当前值:${mean3m}ms,1小时前值:${mean1h}ms, 波动值:${Math.round(((mean3m - mean1h) / mean1h.toDouble()) * 100)}%")
        } else {
            meterReportBo.setStatus(InspectionMeterStatus.HEALTHY)
            meterReportBo.setDetails("Raft Commit Time波动正常，当前值:${mean3m}ms,1小时前值:${mean1h}ms, 波动值:${Math.round(((mean3m - mean1h) / mean1h.toDouble()) * 100)}%")
        }
    } catch (Exception e) {
        meterReportBo.setStatus(InspectionMeterStatus.DANGER)
        meterReportBo.setDetails("获取指标数据异常:${e.getMessage()}")
    }
    return meterReportBo


}