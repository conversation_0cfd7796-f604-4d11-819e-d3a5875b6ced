package cn.huolala.arch.hermes.mesh.sentinel.groovy.consul

import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus
import cn.huolala.arch.hermes.mesh.common.model.ParamContext
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext
import cn.hutool.core.net.NetUtil
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.StrUtil
import com.orbitz.consul.Consul
import com.orbitz.consul.model.agent.ImmutableRegCheck
import com.orbitz.consul.model.agent.ImmutableRegistration

static def main(InspectionContext inspectionContext, ParamContext context, MeterReportBo meterReportBo) {
    try {
        def check = ImmutableRegCheck
                .builder()
                .ttl(String.format("%ss", 300))
                .deregisterCriticalServiceAfter(String.format("%ss", 600))
                .build();
        def registration = ImmutableRegistration.builder()
                .name("soa-admin-demo-registry")
                .id("soa-admin-demo-registry-" + RandomUtil.randomInt(1000))
                .address(NetUtil.getLocalhostStr())
                .check(check)
                .build();
        Consul client = inspectionContext.getRegistryService().consulClient();
        client.agentClient().register(registration)
        meterReportBo.setStatus(InspectionMeterStatus.HEALTHY);
        meterReportBo.setDetails(StrUtil.format("Consul集群正常，验证注册服务正常"));
    }catch (Exception e){
        meterReportBo.setStatus(InspectionMeterStatus.DANGER);
        meterReportBo.setDetails(StrUtil.format("Consul集群异常，无法正常注册服务，异常信息:{}",e.getMessage()));
    }
    return meterReportBo;
}
