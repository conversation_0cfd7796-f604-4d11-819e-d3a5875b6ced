package cn.huolala.arch.hermes.mesh.sentinel.inspection;

import cn.huolala.arch.hermes.mesh.component.apollo.service.ApolloFacadeConfigService;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.component.lone.service.LoneFacadeService;
import cn.huolala.arch.hermes.mesh.monitor.service.VictoriaMetricService;
import cn.huolala.arch.hermes.mesh.sentinel.service.GovernanceInfoQueryService;
import cn.huolala.arch.hermes.mesh.sentinel.service.RegistryService;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Component;

@Data
@Component
public class InspectionContext {

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private RegistryService registryService;

    @Resource
    private LoneFacadeService loneFacadeService;

    @Resource
    private CmdbFacadeService cmdbFacadeService;

    @Resource
    private VictoriaMetricService victoriaMetricService;

    @Resource
    private ApolloFacadeConfigService apolloFacadeConfigService;

    @Resource
    private GovernanceInfoQueryService governanceInfoQueryService;

}
