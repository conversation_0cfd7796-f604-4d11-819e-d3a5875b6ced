package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.mesh.api.bos.HttpRequestBo;
import cn.huolala.arch.hermes.mesh.api.bos.HttpResponseBo;
import cn.huolala.arch.hermes.mesh.api.enums.Method;
import cn.huolala.arch.hermes.mesh.common.el.ElCoder;
import cn.huolala.arch.hermes.mesh.common.el.ElExpression;
import cn.huolala.arch.hermes.mesh.common.model.Pair;
import cn.huolala.arch.hermes.mesh.sentinel.service.HttpService;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import cn.lalaframework.tools.convert.Convert;
import lombok.SneakyThrows;
import org.apache.hc.core5.net.URIBuilder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;


@Service
public class HttpServiceImpl implements HttpService {

    private static final HttpClient HTTP_CLIENT = HttpClient.newBuilder()
            .version(HttpClient.Version.HTTP_1_1)
            .followRedirects(HttpClient.Redirect.NORMAL)
            .connectTimeout(Duration.ofSeconds(6)).build();


    @Override
    public HttpResponseBo invoke(HttpRequestBo requestBo) {
        return doInvoke(requestBo);
    }


    private HttpResponseBo doInvoke(HttpRequestBo requestBo){

        Map<String, Object> context = requestBo.getContext().stream().collect(Collectors.toMap(Pair::getName, Pair::getValue));
        requestBo.setUrl(ElCoder.decoder(requestBo.getUrl(),context));
        requestBo.setParameters(ElCoder.decoder(requestBo.getParameters(), context));
        requestBo.setHeaders(ElCoder.decoder(requestBo.getHeaders(), context));
        requestBo.setBody(ElCoder.decoder(requestBo.getBody(),context));

        HttpRequest.Builder builder = HttpRequest.newBuilder().uri(toUri(requestBo.getUrl(),requestBo.getParameters())).header("Content-Type", requestBo.getContent().getMemo());
        requestBo.getHeaders().stream().filter(pairBo -> StrUtil.isAllNotEmpty(pairBo.getName(),pairBo.getValue())).forEach((Pair pair) -> {builder.header(pair.getName(), pair.getValue());});
        ofNullable(requestBo.getBody()).ifPresent(body -> {
            HttpRequest.BodyPublisher bodyPublisher = HttpRequest.BodyPublishers.ofString(body);
            if (requestBo.getMethod() == Method.POST){
               builder.POST(bodyPublisher);
               return;
            }
            builder.PUT(bodyPublisher);
        });
        StopWatch stopWatch = StopWatch.create("");
        stopWatch.start();
        HttpRequest httpRequest = builder.build();
        HttpResponseBo responseBo = new HttpResponseBo();
        try {
            HttpResponse<String> httpResponse = HTTP_CLIENT.send(httpRequest, HttpResponse.BodyHandlers.ofString());
            stopWatch.stop();
            int statusCode = httpResponse.statusCode();
            responseBo.setStatusCode(statusCode);
            responseBo.setRt(stopWatch.getLastTaskTimeMillis());
            responseBo.setUrl(httpResponse.uri().toString());
            responseBo.setReqBody(requestBo.getBody());
            responseBo.setRespBody(httpResponse.body());
            responseBo.setMethod(httpRequest.method());
            responseBo.setReqHeaders(httpRequest.headers().map().entrySet().stream().map(entry -> Pair.builder().name(entry.getKey()).value(StrUtil.join(";",entry.getValue())).build()).toList());
            responseBo.setRespHeaders(httpResponse.headers().map().entrySet().stream().map(entry -> Pair.builder().name(entry.getKey()).value(StrUtil.join(";",entry.getValue())).build()).toList());

            if (statusCode < 300 && statusCode >= 200){
                try {
                    JSON json = JSONUtil.parse(responseBo.getRespBody());
                    Map<String, Object> expression = ElExpression.expression(requestBo.getTakeOut(), json, responseBo.getRespHeaders());
                    responseBo.getTakeOut().addAll(expression.entrySet().stream().map(entry -> Pair.builder().name(entry.getKey()).value(Convert.toStr(entry.getValue())).build()).toList());
                    HttpResponseBo.ExpressionResult expressionResult = new HttpResponseBo.ExpressionResult();
                    if (StrUtil.isNotEmpty(requestBo.getAssertExp())){
                        String decoder = ElCoder.decoder(requestBo.getAssertExp(), expression);
                        expressionResult.setExpression(decoder);
                        expressionResult.setResult(ElCoder.decoder(decoder));
                    }else {
                        expressionResult.setResult(true);
                    }
                    responseBo.setAssertResult(expressionResult);
                    String businessCode = ElExpression.expression(requestBo.getBusinessExp(), json,  responseBo.getRespHeaders());
                    int bc = StrUtil.isBlank(businessCode) ? -1 : Integer.parseInt(businessCode);
                    responseBo.setBusinessCode(bc);
                }catch (Exception ignored){
                    // ignore if not be json
                }
            }
        } catch (IOException | InterruptedException e) {
            responseBo.setPhrase(e.getClass().getName());
            responseBo.setRespBody(e.toString());
        }
        return responseBo;
    }



    @SneakyThrows
    private static URI toUri(String i, List<Pair> parameter) {
        URIBuilder uriBuilder = new URIBuilder(i.trim());
        parameter.stream().filter(pairBo -> StrUtil.isAllNotEmpty(pairBo.getName(),pairBo.getValue())).forEach(pairBo -> uriBuilder.addParameter(pairBo.getName(),pairBo.getValue()));
        return uriBuilder.build();
    }


}
