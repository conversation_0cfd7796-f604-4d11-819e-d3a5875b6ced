package cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.AlertEventType;
import cn.huolala.arch.hermes.mesh.component.lark.notify.annotation.*;
import cn.huolala.arch.hermes.mesh.sentinel.altert.annotation.LarkNotifyData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.AppAlertDataObject;
import cn.huolala.arch.hermes.mesh.sentinel.support.InspectionConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@LarkNotifyData(type = AlertEventType.INSPECTION_WARNING, clickUrl = @LarkMsgBottomUrl(url = InspectionConstants.INSPECTION_URL_TEMPLATE))
public class InspectionWarningData extends AppAlertDataObject {

    @LarkNotifyUser
    private String owner;

    @LarkNotifyGroup
    private String notifyGroup;

    @LarkMsgContent(value = "告警详情")
    private String content;

    @LarkMsgUrlFormat(value = "reportId")
    private Long reportId;

    @LarkMsgUrlFormat(value = "panelDomain")
    private String panelDomain;
}
