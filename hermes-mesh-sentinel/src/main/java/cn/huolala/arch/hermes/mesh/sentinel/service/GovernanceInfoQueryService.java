package cn.huolala.arch.hermes.mesh.sentinel.service;

import java.util.Map;
import java.util.Set;

public interface GovernanceInfoQueryService {

    /**
     * 返回时间段内的所有修改过的app-cdk
     */
    Set<String> listApiCircuitLogsByCreateTime(Long startTime, Long endTime);

    /**
     * 返回时间段内的所有修改过的app-cdk
     */
    Set<String> listApiDegradeLogsByCreateTime(Long startTime, Long endTime);

    /**
     * 返回时间段内的所有修改过的app-cdk
     */
    Set<String> listApiLimitLogsByCreateTime(Long startTime, Long endTime);

    Set<String> listAppTimeoutLogsByCreateTime(Long startTime, Long endTime);

    Map<String, String> listAppOperationDetailByCreateTime(Long startTime, Long endTime);
}
