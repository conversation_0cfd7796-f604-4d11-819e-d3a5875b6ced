package cn.huolala.arch.hermes.mesh.sentinel.cron.consul;

import cn.huolala.arch.hermes.mesh.common.utils.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.actuate.scheduling.ScheduledTasksEndpoint;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.config.ScheduledTaskHolder;
import org.springframework.stereotype.Component;

/**
 * 定时任务生效日志打印
 */
@Slf4j
@Component
public class TaskInfoPrinter implements ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private ObjectProvider<ScheduledTaskHolder> taskHolders;


    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        ScheduledTasksEndpoint.ScheduledTasksDescriptor scheduledTasks = new ScheduledTasksEndpoint(taskHolders.stream().toList()).scheduledTasks();
    }
}
