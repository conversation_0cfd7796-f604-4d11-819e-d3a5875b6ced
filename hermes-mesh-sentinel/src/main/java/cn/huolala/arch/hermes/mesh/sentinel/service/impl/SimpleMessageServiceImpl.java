package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.mesh.component.lark.notify.annotation.LarkNotify;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.check.AppWarmUpAbnormalData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.inspection.InspectionWarningData;
import cn.huolala.arch.hermes.mesh.sentinel.service.SimpleMessageService;
import org.springframework.stereotype.Service;

@Service
public class SimpleMessageServiceImpl implements SimpleMessageService {
    @Override
    @LarkNotify(title = "Java服务预热异常通知", groups = {"JAF服务预热异常通知群"})
    public void releaseTrafficReasonableNotify(AppWarmUpAbnormalData data) {
    }
}
