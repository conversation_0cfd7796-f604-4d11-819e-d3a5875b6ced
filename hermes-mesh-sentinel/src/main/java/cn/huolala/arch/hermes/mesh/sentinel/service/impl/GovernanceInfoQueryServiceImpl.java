package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.mesh.common.enums.RegistryEventType;
import cn.huolala.arch.hermes.mesh.dao.entity.*;
import cn.huolala.arch.hermes.mesh.dao.mapper.*;
import cn.huolala.arch.hermes.mesh.sentinel.service.GovernanceInfoQueryService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GovernanceInfoQueryServiceImpl implements GovernanceInfoQueryService {
    @Resource
    private ApiCircuitLogMapper apiCircuitLogMapper;

    @Resource
    private ApiDegradeLogMapper apiDegradeLogMapper;

    @Resource
    private ApiLimitLogMapper apiLimitLogMapper;

    @Resource
    private ApiTimeoutLogMapper apiTimeoutLogMapper;

    @Resource
    private ApiAuthAppLogMapper apiAuthAppLogMapper;

    @Resource
    private AppRegistryEventMapper appRegistryEventMapper;

    @Override
    public Set<String> listApiCircuitLogsByCreateTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return Set.of();
        }
        QueryWrapper<ApiCircuitLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiCircuitLog> apiCircuitLogs = apiCircuitLogMapper.selectList(queryWrapper);
        if (apiCircuitLogs.isEmpty()) {
            return Set.of();
        }
        return apiCircuitLogs.stream()
                .map(log -> String.format("%s-%s", log.getAppId(), log.getName())).collect(Collectors.toSet());
    }

    @Override
    public Set<String> listApiDegradeLogsByCreateTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return Set.of();
        }
        QueryWrapper<ApiDegradeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiDegradeLog> apiDegradeLogs = apiDegradeLogMapper.selectList(queryWrapper);
        if (apiDegradeLogs.isEmpty()) {
            return Set.of();
        }
        return apiDegradeLogs.stream()
                .map(log -> String.format("%s-%s", log.getAppId(), log.getName())).collect(Collectors.toSet());
    }

    @Override
    public Set<String> listApiLimitLogsByCreateTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return Set.of();
        }
        QueryWrapper<ApiLimitLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiLimitLog> apiLimitLogs = apiLimitLogMapper.selectList(queryWrapper);
        if (apiLimitLogs.isEmpty()) {
            return Set.of();
        }
        return apiLimitLogs.stream()
                .map(log -> String.format("%s-%s", log.getAppId(), log.getName())).collect(Collectors.toSet());
    }

    @Override
    public Set<String> listAppTimeoutLogsByCreateTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return Set.of();
        }
        QueryWrapper<ApiTimeoutLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiTimeoutLog> apiTimeoutLogs = apiTimeoutLogMapper.selectList(queryWrapper);
        if (apiTimeoutLogs.isEmpty()) {
            return Set.of();
        }
        return apiTimeoutLogs.stream().map(ApiTimeoutLog::getAppId).collect(Collectors.toSet());
    }

    public Map<String, String> listAppOperationDetailByCreateTime(Long startTime, Long endTime) {
        if (startTime == null || endTime == null) {
            return Map.of();
        }
        Map<String, String> appOperationLogMap = new HashMap<>();
        QueryWrapper<AppRegistryEvent> registryQueryWrapper = new QueryWrapper<>();
        registryQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        registryQueryWrapper.in("event_type", RegistryEventType.OPERATION_UP, RegistryEventType.OPERATION_UP);
        List<AppRegistryEvent> appRegistryEvents = appRegistryEventMapper.selectList(registryQueryWrapper);
        CollUtil.emptyIfNull(appRegistryEvents).stream().map(AppRegistryEvent::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "存在上下线事件");
        });

        QueryWrapper<ApiLimitLog> limitQueryWrapper = new QueryWrapper<>();
        limitQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiLimitLog> apiLimitLogs = apiLimitLogMapper.selectList(limitQueryWrapper);
        CollUtil.emptyIfNull(apiLimitLogs).stream().map(ApiLimitLog::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "，存在限流配置操作");
        });

        QueryWrapper<ApiCircuitLog> circuitQueryWrapper = new QueryWrapper<>();
        circuitQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiCircuitLog> apiCircuitLogs = apiCircuitLogMapper.selectList(circuitQueryWrapper);
        CollUtil.emptyIfNull(apiCircuitLogs).stream().map(ApiCircuitLog::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "，存在熔断配置操作");
        });

        QueryWrapper<ApiDegradeLog> degradeQueryWrapper = new QueryWrapper<>();
        degradeQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiDegradeLog> apiDegradeLogs = apiDegradeLogMapper.selectList(degradeQueryWrapper);
        CollUtil.emptyIfNull(apiDegradeLogs).stream().map(ApiDegradeLog::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "，存在降级配置操作");
        });

        QueryWrapper<ApiTimeoutLog> timeoutQueryWrapper = new QueryWrapper<>();
        timeoutQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiTimeoutLog> apiTimeoutLogs = apiTimeoutLogMapper.selectList(timeoutQueryWrapper);
        CollUtil.emptyIfNull(apiTimeoutLogs).stream().map(ApiTimeoutLog::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "，存在超时配置操作");
        });

        QueryWrapper<ApiAuthAppLog> authAppQueryWrapper = new QueryWrapper<>();
        authAppQueryWrapper.between("created_at", DateUtil.date(startTime), DateUtil.date(endTime));
        List<ApiAuthAppLog> apiAuthAppLogs = apiAuthAppLogMapper.selectList(authAppQueryWrapper);
        CollUtil.emptyIfNull(apiAuthAppLogs).stream().map(ApiAuthAppLog::getAppId).distinct().forEach(appId -> {
            appOperationLogMap.putIfAbsent(appId, "");
            appOperationLogMap.put(appId, appOperationLogMap.get(appId) + "，存在鉴权配置操作");
        });

        return appOperationLogMap;
    }
}