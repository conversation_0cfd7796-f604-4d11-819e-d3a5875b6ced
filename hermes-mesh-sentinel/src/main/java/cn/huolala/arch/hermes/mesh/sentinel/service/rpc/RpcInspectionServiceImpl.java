package cn.huolala.arch.hermes.mesh.sentinel.service.rpc;


import cn.huolala.arch.hermes.mesh.api.RpcInspectionService;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.*;
import cn.huolala.arch.hermes.mesh.common.el.ElCoder;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionType;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.model.ParamContext;
import cn.huolala.arch.hermes.mesh.dao.entity.inspection.InspectionReport;
import cn.huolala.arch.hermes.mesh.dao.entity.inspection.InspectionRule;
import cn.huolala.arch.hermes.mesh.dao.entity.inspection.ScriptParam;
import cn.huolala.arch.hermes.mesh.dao.mapper.ScriptParamMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.inspection.InspectionReportMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.inspection.InspectionRuleMapper;
import cn.huolala.arch.hermes.mesh.sentinel.altert.AppAlertEvent;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.inspection.InspectionWarningData;
import cn.huolala.arch.hermes.mesh.sentinel.config.HermesPanelConfig;
import cn.huolala.arch.hermes.mesh.sentinel.inspection.InspectionContext;
import cn.huolala.arch.hermes.mesh.sentinel.prompt.PromptTemplater;
import cn.huolala.arch.hermes.mesh.sentinel.service.SimpleMessageService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.lalaframework.soa.annotation.spring.SOAServiceAutoImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.mesh.common.el.ElCoder.PATTERN$$;
import static cn.huolala.arch.hermes.mesh.sentinel.utils.PaginationUtil.graftPage;

@Slf4j
@Service
@SOAServiceAutoImpl
public class RpcInspectionServiceImpl implements RpcInspectionService {

    @Resource
    private InspectionContext inspectionContext;

    @Resource
    private OpenAiChatModel openAiChatModel;

    @Resource
    private ScriptParamMapper scriptParamMapper;

    @Resource
    private InspectionRuleMapper inspectionRuleMapper;

    @Resource
    private InspectionReportMapper inspectionReportMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private HermesPanelConfig hermesPanelConfig;

    private static final BeanOutputConverter<AiInspectionReportBo> BEAN_OUTPUT_CONVERTER = new BeanOutputConverter<>(AiInspectionReportBo.class);

    private static final ScriptEngine SCRIPT_ENGINE = new ScriptEngineManager().getEngineByName("groovy");


    @Override
    public RespMsg<InspectionReportBo> inspectionReport(InspectionScene scene, Long id) {
        InspectionReport report;
        if (id == 0) {
            //find latest
            report = inspectionReportMapper.selectLatest(scene);
        } else {
            report = inspectionReportMapper.selectById(id);
        }
        return RespMsg.respSuc(toInspectionRuleBo(report));
    }


    private InspectionReportBo toInspectionRuleBo(InspectionReport report) {
        if (report == null) return null;
        InspectionReportBo bo = new InspectionReportBo();
        bo.setId(report.getId());
        bo.setStatus(report.getStatus());
        bo.setCreatedAt(report.getCreatedAt());
        List<ComponentReportBo> componentReportBos = report.getComponents().stream().map(componentReport -> {
            ComponentReportBo componentReportBo = new ComponentReportBo();
            componentReportBo.setName(componentReport.getName());
            componentReportBo.setStatus(componentReport.getStatus());

            componentReportBo.setMeterReports(componentReport.getMeterReports().stream().map(MeterReportBo::of).toList());
            return componentReportBo;
        }).toList();
        bo.setComponents(componentReportBos);
        bo.setParamContext(ParamContext.of(report.getParamContext()));
        bo.setAiInspectionReport(BeanUtil.copyProperties(report.getAiInspectionReport(), AiInspectionReportBo.class));
        return bo;
    }


    @Override
    public RespMsg<InspectionReportBo> executeInspection(InspectionScene scene, ParamContext context) {
        InspectionReport inspectionReport = new InspectionReport();
        inspectionReport.setScene(scene);
        inspectionReport.setParamContext(context);
        List<InspectionRuleBo> inspectionRuleBos = BeanUtil.copyToList(inspectionRuleMapper.selectList(scene), InspectionRuleBo.class);
        InspectionMeterStatus status = InspectionMeterStatus.HEALTHY;


        //填充全局参数
        List<ScriptParam> scriptParams = scriptParamMapper.selectList(new QueryWrapper<>());
        scriptParams.stream().filter(scriptParam -> scriptParam.getComponent() == InspectionComponent.ALL).forEach(scriptParam -> context.putIfAbsent(scriptParam.getName(), scriptParam.getValue()));


        //执行自检
        List<MeterReportBo> meterReportBos = new ArrayList<>();
        for (InspectionRuleBo inspectionRuleBo : inspectionRuleBos) {
            ParamContext ruleContext = ParamContext.of(context);

            //填充组件参数
            scriptParams.stream().filter(scriptParam -> scriptParam.getComponent() == inspectionRuleBo.getComponent()).forEach(scriptParam -> ruleContext.put(scriptParam.getName(), scriptParam.getValue()));
            MeterReportBo meterReportBo = initMeterReport(inspectionRuleBo);

            try {
                //参数填充
                inspectionRuleBo.setScript(ElCoder.decoder(inspectionRuleBo.getScript(), ruleContext,PATTERN$$));
                meterReportBo = doExecuteInspectionRule(inspectionRuleBo,meterReportBo,ruleContext);
                if (meterReportBo.getStatus().ordinal() > status.ordinal() && meterReportBo.getType() == InspectionType.EVALUATION) {
                    status = meterReportBo.getStatus();
                }
            } catch (Exception e) {
                meterReportBo.setStatus(InspectionMeterStatus.DANGER);
                meterReportBo.setDetails(e.getMessage());
                log.error("executeInspectionRule error ,meterReport:{}", meterReportBo, e);
            }finally {
                meterReportBos.add(meterReportBo);
            }
        }

        //设置自检报告状态
        inspectionReport.setStatus(status);
        meterReportBos.stream().collect(Collectors.groupingBy(MeterReportBo::getComponent)).forEach((key, value) -> {

            InspectionReport.ComponentReport componentReport = new InspectionReport.ComponentReport();
            componentReport.setName(key);
            InspectionMeterStatus meterStatus = InspectionMeterStatus.HEALTHY;

            for (MeterReportBo meterReportBo : value) {
                if (meterReportBo.getStatus().ordinal() > meterStatus.ordinal()) {
                    meterStatus = meterReportBo.getStatus();
                }
                componentReport.getMeterReports().add(meterReportBo);
            }
            componentReport.setStatus(meterStatus);
            inspectionReport.getComponents().add(componentReport);
        });

        //保存报告
        Prompt prompt = PromptTemplate.builder().template(PromptTemplater.InspectionReportPrompt.INSPECTION_REPORT_PROMPT).variables(Map.of("data", JSONUtil.toJsonStr(inspectionReport), "format", BEAN_OUTPUT_CONVERTER.getFormat())).build().create();
        Generation generation = openAiChatModel.call(prompt).getResult();
        AiInspectionReportBo aiInspectionReportBo = null;
        if (generation.getOutput().getText() != null) {
            aiInspectionReportBo = BEAN_OUTPUT_CONVERTER.convert(generation.getOutput().getText());
        }
        inspectionReport.setAiInspectionReport(BeanUtil.copyProperties(aiInspectionReportBo, InspectionReport.AiInspectionReport.class));
        inspectionReportMapper.insert(inspectionReport);

        // 按组件发送告警通知
        inspectionReport.getComponents().forEach(componentReport -> {
            if (!InspectionMeterStatus.HEALTHY.equals(componentReport.getStatus())) {
                try {
                    InspectionWarningData warningData = InspectionWarningData.builder()
                            .owner("boris.huang")// todo 后面再细化组件和负责人的关系
                            .notifyGroup("SOA-ADMIN智检告警")
                            .content(String.format("组件:{%s} 智能检查存在异常, 请关注", componentReport.getName()))
                            .areaEnvId(hermesPanelConfig.getAreaEnvId())
                            .reportId(inspectionReport.getId())
                            .appId(componentReport.getName().name())
                            .panelDomain(hermesPanelConfig.getAddress())
                            .build();
                    eventPublisher.publishEvent(new AppAlertEvent(warningData));
                } catch (Exception e) {
                    log.error("send lark inspectionWarning error,componentName:{} , report:{}, ", componentReport.getName(), inspectionReport.getId(), e);
                }
            }
        });

        return RespMsg.respSuc(toInspectionRuleBo(inspectionReport));
    }

    @Override
    public RespMsg<List<InspectionRuleBo>> inspectionRules(InspectionScene scene) {
        return RespMsg.respSuc(BeanUtil.copyToList(inspectionRuleMapper.selectAllList(scene), InspectionRuleBo.class));
    }


    @Override
    public RespMsg<InspectionRuleBo> updateInspectionRule(InspectionRuleBo ruleBo) {
        if (ruleBo.getId() == null) {
            InspectionRule inspectionRule = BeanUtil.copyProperties(ruleBo, InspectionRule.class);
            inspectionRuleMapper.insert(inspectionRule);
        } else {
            InspectionRule inspectionRule = inspectionRuleMapper.selectById(ruleBo.getId());
            if (inspectionRule == null) {
                throw new IllegalArgumentException(StrUtil.format("未找到脚本信息根据脚本ID:{},请刷新页面后重试", ruleBo.getId()));
            }
            BeanUtil.copyProperties(ruleBo, inspectionRule);
            inspectionRuleMapper.updateById(inspectionRule);
        }
        return RespMsg.respSuc(ruleBo);
    }


    @Override
    public RespMsg<MeterReportBo> executeInspectionRule(InspectionRuleBo ruleBo, ParamContext context) {
        //填充全局参数
        List<ScriptParam> scriptParams = scriptParamMapper.selectByComponent(ruleBo.getComponent());
        scriptParams.stream().filter(scriptParam -> scriptParam.getComponent() == InspectionComponent.ALL).forEach(scriptParam -> context.putIfAbsent(scriptParam.getName(), scriptParam.getValue()));
        scriptParams.stream().filter(scriptParam -> scriptParam.getComponent() == ruleBo.getComponent()).forEach(scriptParam -> context.put(scriptParam.getName(), scriptParam.getValue()));
        try {
            MeterReportBo meterReportBo = initMeterReport(ruleBo);
            ruleBo.setScript(ElCoder.decoder(ruleBo.getScript(), context,PATTERN$$));
            return RespMsg.respSuc(doExecuteInspectionRule(ruleBo,meterReportBo,context));
        } catch (Exception e) {
            log.error("executeInspectionRule error ,script:{}", ruleBo.getScript(), e);
            return RespMsg.respErr(e.getMessage());
        }
    }

    @Override
    public RespMsg<Page<InspectionReportBo>> inspectionReportHistory(int num, int size,InspectionScene scene) {
        return RespMsg.respSuc(graftPage(inspectionReportMapper.selectPage(Page.of(num, size), scene), this::toInspectionRuleBo));
    }

    @Override
    public void deleteInspectionRule(Long id) {
        inspectionRuleMapper.deleteById(id);
    }

    private MeterReportBo doExecuteInspectionRule(InspectionRuleBo ruleBo, MeterReportBo meterReportBo, ParamContext context) throws Exception{
        String script = ruleBo.getScript();
        SCRIPT_ENGINE.eval(script);
        return (MeterReportBo) ((Invocable) SCRIPT_ENGINE).invokeFunction("main", inspectionContext, context, meterReportBo);
    }

    @NotNull
    private MeterReportBo initMeterReport(InspectionRuleBo ruleBo) {
        MeterReportBo meterReportBo = new MeterReportBo();
        meterReportBo.setId(ruleBo.getId());
        meterReportBo.setName(ruleBo.getName());
        meterReportBo.setComponent(ruleBo.getComponent());
        meterReportBo.setType(ruleBo.getType());
        meterReportBo.setMemo(ruleBo.getMemo());
        meterReportBo.setAction(ruleBo.getAction());
        return meterReportBo;
    }


    @Override
    public List<ScriptParamBo> scriptParams() {
        return BeanUtil.copyToList(scriptParamMapper.selectList(new QueryWrapper<>()), ScriptParamBo.class);
    }

    @Override
    public void postScriptParam(ScriptParamBo paramBo) {
        ScriptParam scriptParam = BeanUtil.copyProperties(paramBo, ScriptParam.class);
        scriptParamMapper.insert(scriptParam);
    }

    @Override
    public void putScriptParam(ScriptParamBo paramBo) {
        if (paramBo.getId() == null){
            throw new RuntimeException("id cannot be empty");
        }
        ScriptParam scriptParam = scriptParamMapper.selectById(paramBo.getId());
        scriptParam.setName(paramBo.getName());
        scriptParam.setValue(paramBo.getValue());
        scriptParam.setComponent(paramBo.getComponent());
        scriptParam.setMemo(paramBo.getMemo());
        scriptParamMapper.updateById(scriptParam);
    }

    @Override
    public void deleteScriptParam(Long id) {
        scriptParamMapper.deleteById(id);
    }
}
