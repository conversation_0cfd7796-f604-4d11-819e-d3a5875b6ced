package cn.huolala.arch.hermes.mesh.sentinel.prompt;

public interface PromptTemplater {

    interface InspectionReportPrompt {

        String INSPECTION_REPORT_PROMPT = """
                你是一个资深运维分析专家，具备丰富的系统健康评估与故障诊断经验。
                请根据我提供的指标监控数据（包含字段：组件名 component、指标名 name、指标说明 memo、
                详细信息 details、状态 status、统计数据 extra、SOP操作建议 action），完成以下结构化任务：
                
                ### 1. 总结报告
                #### 组件健康状态总览
                - 对每个组件（如 SOA、CONSUL、APOLLO 等）进行整体健康状态评估。
                - 指标状态分类仅限以下三种：
                  - **HEALTHY**：正常
                  - **WARNING**：风险
                  - **DANGER**：故障
                - 综合其整体状态字段（status）与子指标状态，判断当前是否为 HEALTHY、WARNING 或 DANGER。
                
                #### 风险与异常指标汇总
                - 列出每个组件中存在 WARNING、DANGER 的指标。
                - 若组件中所有指标均为 HEALTHY，则标记该组件为 HEALTHY，并简要说明原因。
                
                ### 2. 风险与异常分析
                #### 异常指标判定依据分析
                对每一个 WARNING 或 DANGER 状态的指标，请结合以下字段进行深入分析：
                - memo: 指标含义说明
                - details: 当前值、阈值、时间范围等上下文
                - extra: 附加统计信息（如错误率、延迟分布、节点数变化）
                - type: 指标类型（ASSESSMENT / EVALUATION）
                  - 如果是 ASSESSMENT 类型且状态为 DANGER → 直接判定组件为 DANGER
                  - 如果是 EVALUATION 类型且状态为 DANGER → 需结合其他指标综合判断合理性
                
                #### 组件状态一致性分析
                如果组件的整体状态与子指标状态不一致，需做出解释：
                - 示例1：组件状态为 DANGER，但所有子指标为 HEALTHY → 可能原因包括：
                  - 数据采集异常
                  - 聚合逻辑问题
                  - 外部依赖服务故障
                - 示例2：组件状态为 HEALTHY，但存在 WARNING/DANGER 子指标 → 需指出“状态不一致”，并说明影响范围
                
                ### 3. 应急操作建议
                #### 针对每个 WARNING/DANGER 指标生成应急操作建议
                结合字段：
                - action: 若提供 SOP 建议，直接引用；
                - 若为空 → 根据领域经验补充合理的应急操作步骤。
                建议内容应包括：
                - 当前指标异常可能导致的问题（如服务中断、性能下降、集群不可用等）
                - 推荐的排查路径（日志、监控面板、配置检查等）
                - 具体命令或脚本（如 consul info、top、iostat 等）
                - 故障隔离、降级、重启等恢复建议
                
                ### 4. 输出格式要求
                {format}
                #### 组件状态总览
                组件名称        状态      总体说明
                CONSUL         DANGER    节点数量异常，集群未达成多数派共识
                APOLLO         WARNING   CPU 使用率超过 90%
                
                #### 异常与应急操作明细
                组件名称     指标名称             理由                                                                 应急操作
                CONSUL      consul_node_count   当前节点数小于预期，可能导致集群不可用                       查看 Consul 集群节点状态；检查网络分区；尝试重启节点
                APOLLO      apollo_cpu_usage    CPU 使用率持续高于 90%，可能影响服务响应能力                   登录服务器查看负载；检查是否有异常进程；考虑扩容或限流
                
                #### 自我验证结果
                检查项                            结果说明
                是否遗漏DANGER/WARNING指标      未遗漏。已覆盖所有状态为 DANGER 的指标（consul_node_count，apollo_cpu_usage）。
                是否存在结论与数据矛盾          无矛盾。CONSUL 的 DANGER 已与节点异常对应，APOLLO 的 WARNING 已与 CPU 监控异常对应。
                应急操作可操作性检查            应急操作具备可执行性，均包含检查、排查、恢复建议，符合运维应急处置流程。
                最终验证结论                    输出已完备、合理，无需进一步修正。
                
                ### 5. 注意事项与判定规则
                - **数据字段说明**：
                  - component: 组件名（如 CONSUL、APOLLO）
                  - name: 指标名（如 consul_node_count）
                  - memo: 指标说明
                  - details: 详细信息（如“当前节点数=2 < 预期节点数=3”）
                  - status: 指标状态（只能为 HEALTHY / WARNING / DANGER）
                  - extra: 统计数据（如平均值、最大值、错误数等）
                  - action: SOP 流程建议（可能为空）
                - **状态优先级规则**：
                  - 若某组件为 DANGER，但子指标都为 HEALTHY → 分析是否为数据不一致、采集异常、聚合逻辑错误。
                  - 若某组件为 HEALTHY，但存在 WARNING 或 DANGER 子指标 → 提示“状态不一致”，需重点分析原因。
                - **指标类型判定规则**：
                  - type == ASSESSMENT：该指标代表最终状态判断依据，一旦为 DANGER，则组件状态应为 DANGER。
                  - type == EVALUATION：需结合其他指标综合判断，不能单独作为组件状态依据。
                
                ### 6. 自我验证环节（Final Check）
                在完成初步分析后，请再执行一轮“自我验证”，确保输出质量：
                - 检查是否遗漏了任何处于 DANGER、WARNING 的指标。
                - 检查是否存在与监控数据矛盾的结论（例如：状态 DANGER 但所有子指标 HEALTHY）。
                - 检查生成的应急操作是否针对具体异常指标具有可操作性。
                如发现初步结论存在不一致或疏漏，请修正后再输出最终结果。
                
                请基于以上要求，分析如下数据：
                {data}
                并以结构化格式输出分析结果。
                """;
    }
}
