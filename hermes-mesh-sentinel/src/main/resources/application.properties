server.port=8070
spring.profiles.active=dao,victoria,compatible,stream,apollo,llm
apollo.config-service=http://apollo-stg.myhll.cn:8080
management.health.defaults.enabled=true
spring.main.allow-bean-definition-overriding=true
#soa
lala.soa.config-center.protocol=apollo
lala.soa.config-center.address=apollo-stg.myhll.cn:8080
hll.app.id=ci-hermes-sentinel-svc
lala.soa.consumer.scan-base-packages=cn.huolala.arch.hermes.mesh.sentinel.service.rpc
#apollo
apollo.portalUrl=http://apollo.huolala.work/lalaapi/
apollo.env=FAT
#lone
lone.address=http://lone-api-stg.huolala.work
#lone.address=http://lone-api.myhll.cn
#lone.app-key=lone_knjuzxd5rw2l5yjh
lone.app-key=lone_hsxen6z1mcua0tcs
#lone.app-secret=e8e07a7448a90aca609d23b860b9cf0e
lone.app-secret=2100561ca5adcf5649cfe6d4db95c04f
#soa
lala.soa.hostPort.ci-ldeps-svc.host=ldeps-stg.myhll.cn
lala.soa.hostPort.ci-ldeps-svc.port=80
lala.soa.hostPorts.ci-hermes-panel-web.host=127.0.0.1
lala.soa.hostPorts.ci-hermes-panel-web.port=8080
lala.soa.consumer.scan-package-classes=cn.huolala.ldeps.facade.service.LdepsDependencyFacade,cn.huolala.arch.hermes.mesh.api.RpcEventService,cn.huolala.arch.hermes.mesh.api.RpcPanelService
#lark
lark.address=https://open.feishu.cn
lark.file-address=https://huolala.feishu.cn
lark.app-id=********************
lark.app-secret=COHqq2ABqzNDZ1yX3Tt7TeYxAB1ec7ma
lark.file-dir-token=fldcnjLdbJ3Cqll0FZUtX1BMhye
lark.file-name-suffix=HLL
lark.help-desk.id=6851384208410017793
lark.help-desk.token=ht-c86d223d-ddad-8e1d-493b-86a0e54b4747
#cmdb
cmdb.address=https://cmdb.huolala.work
cmdb.key=app-ci-soa-admin-monitor-svr
cmdb.secret=ED7148A5BEE440AAE93FC756AFF5A1D1
cmdb.app-info-expire=120
cmdb.user-app-info-expire=60
# kali
kali.address=http://kali-api-prd.huolala.work/api/v1/
kali.token=a4f72af9-594b-42fb-8522-4b975664f989
# victoria
victoria.address=http://gateway-svr-stg.myhll.cn
victoria.app-id=ci-soa-admin-monitor-svr
victoria.app-key=97B32D3119BE4B00558CC5D558E0400D

#hermes.specification
hermes.specification.config-url.command-key-api-generic-count=https://huolala.feishu.cn/wiki/V2JZwZAKRinyZ2kMgzyco5QXnEe
hermes.specification.config-url.lala-soa-discovery-register=https://huolala.feishu.cn/wiki/FnKfw5jEpiMJWqkfd32c6rRnnWe#YzIxdgI4LoDr4IxJRS9cUiLDn8d
hermes.specification.config-url.lala-soa-discovery-tags=https://huolala.feishu.cn/wiki/FnKfw5jEpiMJWqkfd32c6rRnnWe#RULHdsI0sosoC8xRjBAcjrpCnSc
hermes.specification.config-url.no-exist-cross-zone-call=https://huolala.feishu.cn/docx/BbNbdxASUoyD8IxadKzcC9Xgndc
hermes.specification.config-url.rpc-interface-suffix=https://huolala.feishu.cn/docx/M4X4dOjhxoJQKYx8zmVc7yxFnQd#O8K2doqQ2oUCMUxUTpHcTZjSnVb
hermes.specification.config-url.provider-interface-type=https://huolala.feishu.cn/docx/M4X4dOjhxoJQKYx8zmVc7yxFnQd#N0YUdWYSMo8MOexgvP6cVFtfn8g
hermes.specification.config-url.minimum-version=https://huolala.feishu.cn/wiki/wikcn9RrlDfDwG4beISlGtOvxTe#jOesPM
hermes.specification.config-url.kv-in-registration-center=https://huolala.feishu.cn/docx/M4X4dOjhxoJQKYx8zmVc7yxFnQd#TqokdgW0UoyM4oxu6iAcxCwQnZf
hermes.specification.config-url.lala-soa-discovery-address=https://huolala.feishu.cn/docx/M4X4dOjhxoJQKYx8zmVc7yxFnQd#YYWids4kqoS0sSx8hOxcm6Runbf
hermes.specification.config-url.lala-soa-consumer-timout=https://huolala.feishu.cn/wiki/wikcnHkCICcQcQ9D4PR5wb9Lcge#N7hivc
hermes.specification.config-url.node-in-registration-center=https://huolala.feishu.cn/wiki/wikcniN3EoL0TPCSwJbp9eoPiBb
hermes.specification.minimum-version=2.0.6.RELEASE
#hermes.panel
hermes.panel.area-env-id=1
hermes.panel.address=http://127.0.0.1:8080
hermes.panel.inner-address=http://127.0.0.1:8080

#apollo
apollo.open.api.url=http://apollo.huolala.work
apollo.open.api.token=1cdb471e6c2ae51c46db47e6aa0f502e8a53adc1

endpoint.auth.token=2cdb471e6c2ae51c46db47e6aa0f502e8a53adc2
endpoint.auth.sign.expire.seconds=3600

# ?? gzip
server.compression.enabled = true
server.compression.mime-types = application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=/home/<USER>/logs/ci-hermes-sentinel-svc
#server.tomcat.accesslog.pattern=%t %r %s %B %D %a %A %{x_forwarded_proto}i %{x_forwarded_for}i %{User-Agent}i %{X-Real-IP}i %{Referer}i

#logging.level.root=INFO
#retrofit.global-log.log-strategy=body
spring.ai.openai.api-key=sk-a0a37c6763054d2aaa18e02fd8ec9cd1
spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.chat.options.model=qwen-plus
#spring.ai.openai.chat.options.model=deepseek-reasoner
spring.ai.openai.chat.options.temperature=0.7