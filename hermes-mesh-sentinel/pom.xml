<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hll-hermes-mesh</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hermes-mesh-sentinel</artifactId>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-metric</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-rpc</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-consul</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-apollo</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-dao</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-monitor</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-kms</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus-starter}</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.huolala.ldeps</groupId>
            <artifactId>ci-ldeps-svc-facade</artifactId>
            <version>${ldeps.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-math3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-lark</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-cmdb</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-compatible</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-stream-application</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-lone</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-kali</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dns-cache-manipulator</artifactId>
            <version>${dns-cache.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-mesh-autopilot</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-annotations-api</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-apollo-db</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-accesslog</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>ci-hermes-mesh-sentinel</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
