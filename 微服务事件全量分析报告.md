# 微服务事件全量分析报告

## 分析说明
本报告基于2024年7月10日至2025年7月1日期间的稳定性事件数据和SOA admin操作记录，重新定义微服务问题为：可以通过SOA admin控制面治理手段（TIMEOUT、DEGRADE、LIMIT）影响的事件。

## 稳定性事件分类统计

- **微服务问题**: 85个事件 (62.0%)
- **容器问题**: 6个事件 (4.4%)
- **网络问题**: 5个事件 (3.6%)
- **基础设施问题**: 12个事件 (8.8%)
- **第三方问题**: 0个事件 (0.0%)
- **配置问题**: 18个事件 (13.1%)
- **代码问题**: 5个事件 (3.6%)
- **运营问题**: 1个事件 (0.7%)
- **其他问题**: 5个事件 (3.6%)

总计: 137个事件

## 微服务问题事件治理统计

- **微服务问题事件总数**: 85个
- **使用了SOA admin治理**: 7个 (8.2%)
- **未使用治理**: 78个 (91.8%)

## 微服务问题事件详细分析

### 使用了SOA admin治理的事件

#### 1. 事件ID 2176: 梅林机房宕机

事件描述: 梅林机房宕机
发生时间: 2025-05-27 18:10:00
恢复时间: 2025-05-27 18:40:00
影响业务线: 货运
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - TIMEOUT操作: 2025-05-27 20:04:05 by hanyou.zhu on ops-msgplat-api-svc
    目标: ops-aurora-toolbox-svc@*/*
  - TIMEOUT操作: 2025-05-27 20:03:43 by hanyou.zhu on ops-msgplat-api-svc
    目标: ops-aurora-toolbox-svc@*/*

治理操作统计:
  - TIMEOUT: 2次

---

#### 2. 事件ID 2160: xl-lbs-dloc3-api  4个节点CPU 100%

事件描述: xl-lbs-dloc3-api  4个节点CPU 100%
发生时间: 2025-04-16 22:10:00
恢复时间: 2025-04-16 22:30:00
影响业务线: 小拉
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - DEGRADE操作: 2025-04-16 21:18:48 by jettyk.kuang on bme-trade-driverinfo-svc
    目标: bme-ticket-micro-svc@ticket-micro/ticket/query/getMultipleTypeOrderTicketsNew
  - DEGRADE操作: 2025-04-16 21:14:18 by jettyk.kuang on bme-trade-driverinfo-svc
    目标: bme-ticket-micro-svc@ticket-micro/ticket/query/getMultipleTypeOrderTicketsNew
  - DEGRADE操作: 2025-04-16 20:44:52 by ying.zheng on bfe-orchestrator-api
    目标: ops-ops3-svc@/ops-ops3-svc@_m=api&_a=get_other_expenses_tips
  - DEGRADE操作: 2025-04-16 20:39:05 by ying.zheng on bfe-orchestrator-api
    目标: ops-ops3-svc@/ops-ops3-svc@_m=api&_a=get_other_expenses_tips

治理操作统计:
  - DEGRADE: 4次

---

#### 3. 事件ID 2158: 小拉支付渠道异常

事件描述: 小拉支付渠道异常
发生时间: 2025-04-15 18:45:30
恢复时间: 2025-04-15 18:48:00
影响业务线: 小拉
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - TIMEOUT操作: 2025-04-15 20:23:49 by wendy.liu on bfe-user-open-platform-svc
    目标: bme-hpay-financelegal-svc@financeLegalDecisionFacade/decision
  - TIMEOUT操作: 2025-04-15 20:07:36 by pengcheng.xie on bfe-dapi-hall-svc
    目标: ops-driver-task-svc@/api/task/list/page/ab_test
  - TIMEOUT操作: 2025-04-15 20:07:03 by pengcheng.xie on bfe-dapi-hall-svc
    目标: bme-component-biz-svc@/component/queryOneByUserId
  - TIMEOUT操作: 2025-04-15 20:06:32 by pengcheng.xie on bfe-dapi-hall-svc
    目标: risk-gateway-api@/gateway/?api=risk.face.order.bid&apiVersion=1.0.0
  - TIMEOUT操作: 2025-04-15 20:04:10 by pengcheng.xie on bfe-dmanage-svc
    目标: bme-intracity-svc@/memberReach/queryMemberDisPlayNameBO
  - TIMEOUT操作: 2025-04-15 20:03:44 by pengcheng.xie on bfe-dmanage-svc
    目标: bme-whale-serviceflow-svc@/api/sendLoginSmsCode
  - TIMEOUT操作: 2025-04-15 20:02:53 by pengcheng.xie on bfe-dmanage-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/queryValidAccount
  - DEGRADE操作: 2025-04-15 20:02:16 by gordon.yuan on bfe-customer-application-command-svc
    目标: bme-hpay-financelegal-svc@financeLegalBatchDecisionFacade/batchDecision
  - TIMEOUT操作: 2025-04-15 20:00:37 by yuchao.yu on ops-hestia-api
    目标: css-crm-web-api@salesLeadsFacade/addSales

治理操作统计:
  - TIMEOUT: 8次
  - DEGRADE: 1次

---

#### 4. 事件ID 2122: bme-trade-orderinfo-api 异常升高

事件描述: bme-trade-orderinfo-api 异常升高
发生时间: 2025-01-09 20:24:00
恢复时间: 2025-01-09 20:35:00
影响业务线: 货运
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - TIMEOUT操作: 2025-01-09 22:14:19 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/sendPlatformModelMsg
  - TIMEOUT操作: 2025-01-09 22:14:19 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/sendPlatformModelMsg
  - TIMEOUT操作: 2025-01-09 22:14:03 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryUrlScheme
  - TIMEOUT操作: 2025-01-09 22:14:03 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryUrlScheme
  - TIMEOUT操作: 2025-01-09 22:13:55 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/quickCheckStudentIdentity
  - TIMEOUT操作: 2025-01-09 22:13:55 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/quickCheckStudentIdentity
  - TIMEOUT操作: 2025-01-09 22:13:48 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/getPlatformUserInfo
  - TIMEOUT操作: 2025-01-09 22:13:48 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/getPlatformUserInfo
  - TIMEOUT操作: 2025-01-09 22:13:40 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/saveUserPlatformTag
  - TIMEOUT操作: 2025-01-09 22:13:40 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/saveUserPlatformTag
  - TIMEOUT操作: 2025-01-09 22:13:30 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryJsSdkTicket
  - TIMEOUT操作: 2025-01-09 22:13:30 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryJsSdkTicket
  - TIMEOUT操作: 2025-01-09 22:13:25 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryJsSdkTicket
  - TIMEOUT操作: 2025-01-09 22:13:25 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/queryJsSdkTicket
  - TIMEOUT操作: 2025-01-09 22:13:12 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/weChatAccessToken
  - TIMEOUT操作: 2025-01-09 22:13:12 by david.zhu.xl on xl-bfe-user-svc
    目标: wechat-svc@/weChatAccessToken

治理操作统计:
  - TIMEOUT: 16次

---

#### 5. 事件ID 2098: 微信支付高频超时

事件描述: 微信支付高频超时
发生时间: 2024-11-12 16:15:19
恢复时间: 2024-11-12 16:15:19
影响业务线: 货运
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - DEGRADE操作: 2024-11-12 18:05:56 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryCompleteResourceByOrderNo
  - DEGRADE操作: 2024-11-12 18:05:42 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/overrideByTag
  - DEGRADE操作: 2024-11-12 18:05:28 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryByBatchSubBusinessNo
  - DEGRADE操作: 2024-11-12 18:05:13 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryCompleteResourceByBatchSubBusinessNo
  - DEGRADE操作: 2024-11-12 18:04:56 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/saveResource
  - DEGRADE操作: 2024-11-12 18:04:42 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/saveDifferentGroupResource
  - DEGRADE操作: 2024-11-12 18:03:29 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryCompleteResourceByOrderNo
  - DEGRADE操作: 2024-11-12 18:03:28 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/overrideByTag
  - DEGRADE操作: 2024-11-12 18:03:28 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryByBatchSubBusinessNo
  - DEGRADE操作: 2024-11-12 18:03:27 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/query/v2/queryCompleteResourceByBatchSubBusinessNo
  - DEGRADE操作: 2024-11-12 18:03:27 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/saveResource
  - DEGRADE操作: 2024-11-12 18:03:27 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-trade-resource-svc@service/trade/resource/v2/saveDifferentGroupResource
  - DEGRADE操作: 2024-11-12 17:56:54 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/bme-bizp-user-svc@_m=user_register
  - DEGRADE操作: 2024-11-12 17:56:38 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/bme-bizp-user-svc@_m=get_user_by_phone
  - DEGRADE操作: 2024-11-12 17:56:23 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/_m=get_user_by_id
  - DEGRADE操作: 2024-11-12 17:56:12 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/_m=get_user_by_id
  - DEGRADE操作: 2024-11-12 17:54:57 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/bme-bizp-user-svc@_m=user_register
  - DEGRADE操作: 2024-11-12 17:54:57 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/bme-bizp-user-svc@_m=get_user_by_phone
  - DEGRADE操作: 2024-11-12 17:54:56 by jackson.chen on bme-trade-freight-core-svc
    目标: bme-bizp-user-svc@/index/_m=get_user_by_id

治理操作统计:
  - DEGRADE: 19次

---

#### 6. 事件ID 2083: 小B用户企业认证异常

事件描述: 小B用户企业认证异常
发生时间: 2024-10-22 19:10:00
恢复时间: 2024-10-22 20:14:00
影响业务线: 货运
关联appid: 

✅ 使用了SOA admin控制面进行服务治理:
  - TIMEOUT操作: 2024-10-22 21:09:56 by jack59.zhou on bme-account-query-svc
    目标: bme-relation-platform-svc@tagsQueryFacade/batchQueryTags
  - TIMEOUT操作: 2024-10-22 21:09:46 by jack59.zhou on bme-account-query-svc
    目标: bme-relation-platform-svc@tagsQueryFacade/queryByTagCode
  - TIMEOUT操作: 2024-10-22 21:09:32 by jack59.zhou on bme-account-query-svc
    目标: bme-relation-platform-svc@tagsQueryFacade/queryByBizId
  - LIMIT操作: 2024-10-22 21:06:56 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.TokenFacade/*@*
  - LIMIT操作: 2024-10-22 21:06:13 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.TokenFacade/*@*
  - LIMIT操作: 2024-10-22 21:05:35 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountSmsFacade/sendLoginSmsCode@*
  - LIMIT操作: 2024-10-22 21:05:03 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountVerifyCodeFacade/sendLoginVerifyCode@*
  - LIMIT操作: 2024-10-22 21:04:41 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountSmsFacade/*@*
  - LIMIT操作: 2024-10-22 21:04:23 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountSmsFacade/sendLoginSmsCode@*
  - LIMIT操作: 2024-10-22 21:04:19 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountSmsFacade/sendLoginSmsCode@*
  - LIMIT操作: 2024-10-22 21:04:00 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountVerifyCodeFacade/*@*
  - LIMIT操作: 2024-10-22 21:03:18 by jack59.zhou on bme-account-login-svc
    目标: /cn.huolala.account.login.facade.service.AccountLoginFacade/*@*
  - TIMEOUT操作: 2024-10-22 21:00:14 by jack59.zhou on bme-account-login-svc
    目标: risk-gateway-api@*/*
  - TIMEOUT操作: 2024-10-22 21:00:02 by jack59.zhou on bme-account-login-svc
    目标: risk-gateway-api@*/*
  - TIMEOUT操作: 2024-10-22 20:59:54 by jack59.zhou on bme-account-login-svc
    目标: risk-gateway-api@//*
  - TIMEOUT操作: 2024-10-22 20:59:44 by jack59.zhou on bme-account-login-svc
    目标: risk-gateway-api@/loginRisk
  - TIMEOUT操作: 2024-10-22 20:59:32 by jack59.zhou on bme-account-login-svc
    目标: risk-gateway-api@/extendTokenRisk
  - TIMEOUT操作: 2024-10-22 20:59:15 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:58:55 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountBanFacade/*
  - TIMEOUT操作: 2024-10-22 20:58:41 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/*
  - TIMEOUT操作: 2024-10-22 20:58:24 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountOpenFacade/*
  - TIMEOUT操作: 2024-10-22 20:58:02 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/*
  - TIMEOUT操作: 2024-10-22 20:57:46 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/checkValidAccountType
  - TIMEOUT操作: 2024-10-22 20:57:37 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/queryValidAccount
  - TIMEOUT操作: 2024-10-22 20:57:25 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/batchQueryValidAccount
  - TIMEOUT操作: 2024-10-22 20:57:14 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/queryValidAccountByIdCard
  - TIMEOUT操作: 2024-10-22 20:56:59 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountInfoQueryFacade/queryAllValidRoleAccount
  - TIMEOUT操作: 2024-10-22 20:56:30 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/modifyByPwd
  - TIMEOUT操作: 2024-10-22 20:56:17 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/modifyByUserId
  - TIMEOUT操作: 2024-10-22 20:56:05 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/modifyBySms
  - TIMEOUT操作: 2024-10-22 20:55:46 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountOpenFacade/queryOpenBind
  - TIMEOUT操作: 2024-10-22 20:55:33 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountOpenFacade/unBindOpenRel
  - TIMEOUT操作: 2024-10-22 20:55:14 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountBanFacade/checkAccountBan
  - TIMEOUT操作: 2024-10-22 20:54:58 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/verifyPassword
  - TIMEOUT操作: 2024-10-22 20:54:40 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountPasswordFacade/modifyPwd
  - TIMEOUT操作: 2024-10-22 20:53:41 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountBanFacade/accountBanByUserId
  - TIMEOUT操作: 2024-10-22 20:52:45 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/callVoice
  - TIMEOUT操作: 2024-10-22 20:52:33 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/sendSmsCode
  - TIMEOUT操作: 2024-10-22 20:51:54 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@verifyCodeFacade/sendVerifyCode
  - TIMEOUT操作: 2024-10-22 20:50:33 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@verifyCodeFacade/*
  - TIMEOUT操作: 2024-10-22 20:49:35 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountOpenFacade/bindOpenRel
  - TIMEOUT操作: 2024-10-22 20:49:16 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@verifyCodeFacade/checkSendCodeNumIsLimit
  - TIMEOUT操作: 2024-10-22 20:49:04 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@verifyCodeFacade/sendVerifyCode
  - TIMEOUT操作: 2024-10-22 20:48:48 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@verifyCodeFacade/verifyVerifyCode
  - TIMEOUT操作: 2024-10-22 20:48:23 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@tokenFacade/*
  - TIMEOUT操作: 2024-10-22 20:48:08 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@tokenFacade/expireToken
  - TIMEOUT操作: 2024-10-22 20:47:58 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@tokenFacade/create
  - TIMEOUT操作: 2024-10-22 20:47:46 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@tokenFacade/queryAllChannelValidToken
  - TIMEOUT操作: 2024-10-22 20:47:17 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/*
  - TIMEOUT操作: 2024-10-22 20:46:52 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/queryAuthCode
  - TIMEOUT操作: 2024-10-22 20:46:43 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/delDeviceKeyInfo
  - TIMEOUT操作: 2024-10-22 20:46:33 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/getAllLoginType
  - TIMEOUT操作: 2024-10-22 20:46:23 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/queryDeviceKeyBindInfo
  - TIMEOUT操作: 2024-10-22 20:46:10 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/verifyDeviceKey
  - TIMEOUT操作: 2024-10-22 20:45:59 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/queryAuthAlgo
  - TIMEOUT操作: 2024-10-22 20:45:43 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@deviceKeyFacade/bindDeviceKey
  - TIMEOUT操作: 2024-10-22 20:45:11 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountRegisterFacade/*
  - TIMEOUT操作: 2024-10-22 20:44:54 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/*
  - TIMEOUT操作: 2024-10-22 20:44:43 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/callVoice
  - TIMEOUT操作: 2024-10-22 20:44:26 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/sendSmsCode
  - TIMEOUT操作: 2024-10-22 20:44:23 by dc.tong on bme-hpay-transfercenter-svc
    目标: bme-hpay-innertrans-svc@/innertrans/querybaseaccount
  - TIMEOUT操作: 2024-10-22 20:44:16 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/verifySmsCode
  - TIMEOUT操作: 2024-10-22 20:44:07 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@smsFacade/checkVoiceSendNum
  - TIMEOUT操作: 2024-10-22 20:44:07 by dc.tong on bme-hpay-transfercenter-svc
    目标: bme-hpay-instpay-svc@instRouterConfigFacade/queryInstRouterConfig
  - TIMEOUT操作: 2024-10-22 20:43:46 by jack59.zhou on bme-account-login-svc
    目标: bme-account-platform-svc@accountRegisterFacade/register
  - TIMEOUT操作: 2024-10-22 20:43:42 by dc.tong on bme-hpay-transfercenter-svc
    目标: bme-hpay-payment-svc@//payment/freeze
  - TIMEOUT操作: 2024-10-22 20:43:29 by dc.tong on bme-hpay-transfercenter-svc
    目标: bme-hpay-payment-svc@/payment/freeze
  - TIMEOUT操作: 2024-10-22 20:43:21 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:43:16 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/*
  - TIMEOUT操作: 2024-10-22 20:42:57 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/queryAccountExtInfo
  - TIMEOUT操作: 2024-10-22 20:42:52 by dc.tong on bme-walletprod-api
    目标: bme-hpay-transfercenter-svc@/transfer/queryTransferTotalAmount
  - TIMEOUT操作: 2024-10-22 20:42:36 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/queryAccountExtInfo
  - TIMEOUT操作: 2024-10-22 20:42:26 by dc.tong on bme-walletprod-api
    目标: bme-hpay-transfercenter-svc@/transfer/queryDetailCount
  - TIMEOUT操作: 2024-10-22 20:42:25 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/createToken
  - TIMEOUT操作: 2024-10-22 20:42:16 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/queryOpenUser
  - TIMEOUT操作: 2024-10-22 20:42:09 by dc.tong on bme-walletprod-api
    目标: bme-hpay-transfercenter-svc@/transfer/v2/accept
  - TIMEOUT操作: 2024-10-22 20:42:05 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/registerBindWeChatByPhone
  - TIMEOUT操作: 2024-10-22 20:42:01 by dc.tong on bme-walletprod-api
    目标: bme-hpay-transfercenter-svc@//transfer/v2/accept
  - TIMEOUT操作: 2024-10-22 20:41:56 by jack59.zhou on bme-account-login-svc
    目标: bme-account-svc@accountDomainFacade/smsValidate
  - TIMEOUT操作: 2024-10-22 20:41:44 by dc.tong on bme-walletprod-api
    目标: bme-account-svc@/api/?_m=account.real.info.user.id
  - TIMEOUT操作: 2024-10-22 20:41:23 by dc.tong on bme-walletprod-api
    目标: risk-front-dsc-php@/outsite/punish/get_driver_all_punish
  - TIMEOUT操作: 2024-10-22 20:41:09 by dc.tong on bme-walletprod-api
    目标: risk-front-dsc-php@//outsite/punish/get_driver_all_punish
  - TIMEOUT操作: 2024-10-22 20:40:57 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/*
  - TIMEOUT操作: 2024-10-22 20:40:46 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/extendToken
  - TIMEOUT操作: 2024-10-22 20:40:34 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/logout
  - TIMEOUT操作: 2024-10-22 20:40:26 by dc.tong on bme-walletprod-api
    目标: bfe-sdp-settle-api@/payment/queryRestAmountByDriverId
  - TIMEOUT操作: 2024-10-22 20:40:25 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/verifyToken
  - TIMEOUT操作: 2024-10-22 20:40:16 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/verifyTokenV2
  - TIMEOUT操作: 2024-10-22 20:40:06 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/token/createToken
  - TIMEOUT操作: 2024-10-22 20:39:57 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:39:50 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/mpToken/*
  - TIMEOUT操作: 2024-10-22 20:39:32 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/mpToken/createToken
  - TIMEOUT操作: 2024-10-22 20:39:21 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/mpToken/logout
  - TIMEOUT操作: 2024-10-22 20:39:11 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/mpToken/extendToken
  - TIMEOUT操作: 2024-10-22 20:39:00 by jack59.zhou on bme-account-login-svc
    目标: bme-token-svc@/mpToken/verifyMpToken
  - TIMEOUT操作: 2024-10-22 20:38:59 by dc.tong on bme-walletprod-api
    目标: bme-transport-info-svc@/driverFacade/getDriverInfo
  - TIMEOUT操作: 2024-10-22 20:38:39 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:38:33 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/*
  - TIMEOUT操作: 2024-10-22 20:38:28 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/*
  - TIMEOUT操作: 2024-10-22 20:38:17 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/createToken
  - TIMEOUT操作: 2024-10-22 20:38:11 by dc.tong on bme-walletprod-api
    目标: hll-bme-payboss-svc@/api/?name=bind.personal.bankcard.available.list
  - TIMEOUT操作: 2024-10-22 20:38:06 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/verifyToken
  - TIMEOUT操作: 2024-10-22 20:37:57 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/verifyTokenV2
  - TIMEOUT操作: 2024-10-22 20:37:48 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/logout
  - TIMEOUT操作: 2024-10-22 20:37:38 by jack59.zhou on bme-account-login-svc
    目标: bme-token-user-svc@/userToken/extendToken
  - TIMEOUT操作: 2024-10-22 20:37:37 by dc.tong on bme-walletprod-api
    目标: bme-hpay-acctrans-svc@/acctrans/fundaccount/accountInfo
  - TIMEOUT操作: 2024-10-22 20:37:28 by dc.tong on bme-walletprod-api
    目标: bme-hpay-acctrans-svc@//acctrans/fundaccount/accountInfo
  - TIMEOUT操作: 2024-10-22 20:37:20 by jack59.zhou on bme-account-login-svc
    目标: *@*/*
  - TIMEOUT操作: 2024-10-22 20:37:13 by jack59.zhou on bme-account-login-svc
    目标: bme-account-query-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:37:06 by jack59.zhou on bme-account-login-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/*
  - TIMEOUT操作: 2024-10-22 20:36:50 by jack59.zhou on bme-account-login-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/queryValidAccount
  - TIMEOUT操作: 2024-10-22 20:36:38 by jack59.zhou on bme-account-login-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/queryAccountExtInfo
  - TIMEOUT操作: 2024-10-22 20:36:28 by jack59.zhou on bme-account-login-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/checkValidAccountType
  - TIMEOUT操作: 2024-10-22 20:35:29 by dc.tong on bme-walletprod-api
    目标: bme-whale-serviceflow-svc@/api/?name=getDriverInfoApp
  - LIMIT操作: 2024-10-22 20:32:35 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.register.AccountRegisterFacade/*@*
  - LIMIT操作: 2024-10-22 20:31:56 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/*@*
  - LIMIT操作: 2024-10-22 20:31:30 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/queryValidAccountByIdCard@*
  - LIMIT操作: 2024-10-22 20:30:42 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/queryValidAccount@*
  - LIMIT操作: 2024-10-22 20:30:13 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/checkValidAccountType@*
  - LIMIT操作: 2024-10-22 20:29:54 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/batchQueryValidAccount@*
  - LIMIT操作: 2024-10-22 20:28:39 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.accountinfo.AccountInfoQueryFacade/queryAllValidRoleAccount@*
  - TIMEOUT操作: 2024-10-22 20:28:00 by tim.chen on bfe-delivery-order-svc
    目标: bme-trade-freight-info-svc@service/trade/freight/customize/query/queryOrderNoByFreightNo
  - LIMIT操作: 2024-10-22 20:26:36 by jack59.zhou on bme-account-platform-svc
    目标: /cn.huolala.account.platform.facade.service.register.AccountRegisterFacade/register@*
  - TIMEOUT操作: 2024-10-22 20:23:14 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:23:06 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/*
  - TIMEOUT操作: 2024-10-22 20:21:53 by jack59.zhou on bme-account-platform-svc
    目标: risk-eye-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:21:47 by jack59.zhou on bme-account-platform-svc
    目标: risk-eye-svc@//*
  - TIMEOUT操作: 2024-10-22 20:21:33 by jack59.zhou on bme-account-platform-svc
    目标: risk-eye-svc@/idCardQuery
  - TIMEOUT操作: 2024-10-22 20:20:25 by jack59.zhou on bme-account-platform-svc
    目标: *@*/*
  - TIMEOUT操作: 2024-10-22 20:20:11 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-transfercenter-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:20:06 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-transfercenter-svc@//*
  - TIMEOUT操作: 2024-10-22 20:19:58 by zhipeng.li on bfe-ubase-svr
    目标: bfe-commodity-core-svc@commodityInfoService/getSubCommodityPriceInfo
  - TIMEOUT操作: 2024-10-22 20:19:52 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-transfercenter-svc@/transfer/v2/principal/queryPage
  - TIMEOUT操作: 2024-10-22 20:19:36 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:19:30 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/*
  - TIMEOUT操作: 2024-10-22 20:19:22 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/getId
  - TIMEOUT操作: 2024-10-22 20:19:11 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/getIds
  - TIMEOUT操作: 2024-10-22 20:19:02 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/getIdWithReset
  - TIMEOUT操作: 2024-10-22 20:18:57 by alex.long on bfe-ubase-svr
    目标: bfe-commodity-core-svc@commodityInfoService/getSubCommodityPriceInfo
  - TIMEOUT操作: 2024-10-22 20:18:52 by jack59.zhou on bme-account-platform-svc
    目标: bme-uuid-svc@redisIdService/getIdsWithReset
  - TIMEOUT操作: 2024-10-22 20:18:25 by jack59.zhou on bme-account-platform-svc
    目标: bme-call-api@*/*
  - TIMEOUT操作: 2024-10-22 20:18:18 by jack59.zhou on bme-account-platform-svc
    目标: bme-call-api@//*
  - TIMEOUT操作: 2024-10-22 20:18:11 by jack59.zhou on bme-account-platform-svc
    目标: bme-call-api@/callVoice
  - TIMEOUT操作: 2024-10-22 20:17:50 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-acctrans-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:17:44 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-acctrans-svc@//*
  - TIMEOUT操作: 2024-10-22 20:17:34 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-acctrans-svc@/closeWallet
  - TIMEOUT操作: 2024-10-22 20:17:22 by jack59.zhou on bme-account-platform-svc
    目标: bme-hpay-acctrans-svc@/acctrans/fundaccount/v1/modify
  - TIMEOUT操作: 2024-10-22 20:16:00 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/mpToken/*
  - TIMEOUT操作: 2024-10-22 20:15:38 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:15:31 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/*
  - TIMEOUT操作: 2024-10-22 20:15:16 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/extendToken
  - TIMEOUT操作: 2024-10-22 20:15:07 by benise.li on bme-finance-factoring-svc
    目标: bme-lalafinance-repayment-api@/cash/seal/seal4Bill
  - TIMEOUT操作: 2024-10-22 20:15:05 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/verifyTokenV2
  - TIMEOUT操作: 2024-10-22 20:14:56 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/createToken
  - TIMEOUT操作: 2024-10-22 20:14:47 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/logout
  - TIMEOUT操作: 2024-10-22 20:14:42 by benise.li on bme-finance-factoring-svc
    目标: bme-lalafinance-repayment-api@/cash/seal/seal4Bill
  - TIMEOUT操作: 2024-10-22 20:14:30 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/token/verifyToken
  - TIMEOUT操作: 2024-10-22 20:14:10 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/mpToken/createToken
  - TIMEOUT操作: 2024-10-22 20:14:01 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/mpToken/logout
  - TIMEOUT操作: 2024-10-22 20:13:51 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/mpToken/extendToken
  - TIMEOUT操作: 2024-10-22 20:13:40 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-svc@/mpToken/verifyMpToken
  - TIMEOUT操作: 2024-10-22 20:12:21 by jack59.zhou on bme-account-platform-svc
    目标: bme-sms-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:12:14 by jack59.zhou on bme-account-platform-svc
    目标: bme-sms-svc@/sms/api/*
  - TIMEOUT操作: 2024-10-22 20:12:02 by jack59.zhou on bme-account-platform-svc
    目标: bme-sms-svc@/sms/api/templateSendV3
  - TIMEOUT操作: 2024-10-22 20:11:31 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:11:24 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/*
  - TIMEOUT操作: 2024-10-22 20:11:10 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/createToken
  - TIMEOUT操作: 2024-10-22 20:10:59 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/logout
  - TIMEOUT操作: 2024-10-22 20:10:50 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/verifyToken
  - TIMEOUT操作: 2024-10-22 20:10:41 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/verifyTokenV2
  - TIMEOUT操作: 2024-10-22 20:09:59 by jack59.zhou on bme-account-platform-svc
    目标: bme-token-user-svc@/userToken/extendToken
  - TIMEOUT操作: 2024-10-22 20:07:19 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@/userToken/extendToken
  - TIMEOUT操作: 2024-10-22 20:07:10 by jiarong.yu on bme-hpay-tradeaccept-svc
    目标: bme-hpay-acctrans-svc@/acctrans/outFundAccount/query
  - TIMEOUT操作: 2024-10-22 20:07:01 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@/userToken/*
  - TIMEOUT操作: 2024-10-22 20:06:45 by jiarong.yu on bme-hpay-tradeaccept-svc
    目标: bme-hpay-cashiercore-svc@/payToken/generate
  - TIMEOUT操作: 2024-10-22 20:06:43 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@/api/name=account.login.verify&version=1.0
  - TIMEOUT操作: 2024-10-22 20:06:33 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@/api/*
  - TIMEOUT操作: 2024-10-22 20:06:13 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:06:07 by jack59.zhou on bme-userinfo-svc
    目标: bme-token-user-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:05:36 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@*/*
  - TIMEOUT操作: 2024-10-22 20:05:17 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/queryValidAccount
  - TIMEOUT操作: 2024-10-22 20:04:29 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/queryAccountExtInfo
  - TIMEOUT操作: 2024-10-22 20:04:08 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/checkValidAccountType
  - TIMEOUT操作: 2024-10-22 20:04:02 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/checkValidAccountType
  - TIMEOUT操作: 2024-10-22 20:00:54 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/*
  - TIMEOUT操作: 2024-10-22 20:00:32 by jack59.zhou on bme-userinfo-svc
    目标: bme-account-query-svc@accountInfoQueryFacade/*
  - TIMEOUT操作: 2024-10-22 19:48:55 by weiyuan.huang on bme-finance-bizadmin-svc
    目标: bme-lalafinance-repayment-api@billCmsFacadeService/getOrderRecordingList
  - TIMEOUT操作: 2024-10-22 19:47:54 by weiyuan.huang on bme-finance-bizadmin-svc
    目标: bme-lalafinance-repayment-api@billCmsFacadeService/getOrderRepayList

治理操作统计:
  - TIMEOUT: 171次
  - LIMIT: 17次

---

#### 7. 事件ID 2053: 风控risk-themis-service-svc多个pod cpu异常

事件描述: 风控risk-themis-service-svc多个pod cpu异常
发生时间: 2024-08-19 09:19:17
恢复时间: 2024-08-19 09:31:49
影响业务线: 货运
关联appid: bfe-dapi-order-svc

✅ 使用了SOA admin控制面进行服务治理:
  - DEGRADE操作: 2024-08-19 10:14:37 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/common_reminder/getCommonReminderAreaResult
  - DEGRADE操作: 2024-08-19 10:06:00 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/common_reminder/getCommonReminderResult
  - DEGRADE操作: 2024-08-19 10:04:39 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/bill_judge/getActJudgementResult
  - DEGRADE操作: 2024-08-19 10:01:12 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/api/_m=app.judge_order.list&version=1.0
  - DEGRADE操作: 2024-08-19 09:57:46 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/api/_m=app.judge_order.info&version=1.0
  - DEGRADE操作: 2024-08-19 09:54:21 by jenny.huang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/api/_m=app.judge_order.info&version=1.0
  - DEGRADE操作: 2024-08-19 09:42:15 by freeze.wang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/common_reminder/getCommonReminderResult
  - DEGRADE操作: 2024-08-19 09:33:24 by freeze.wang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/bill_judge/getActJudgementResult
  - DEGRADE操作: 2024-08-19 09:32:56 by freeze.wang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/api/_m=app.judge_order.list&version=1.0
  - DEGRADE操作: 2024-08-19 09:31:10 by freeze.wang on bfe-dapi-order-svc
    目标: risk-themis-service-svc@/common_reminder/getCommonReminderAreaResult

治理操作统计:
  - DEGRADE: 10次

🔗 关联性分析: 找到10个与事件appid(bfe-dapi-order-svc)直接相关的操作

---

### 未使用治理的事件

#### 1. 事件ID 2192: 用户app下单超时未支付

事件描述: 用户app下单超时未支付
发生时间: 2025-06-30 13:24:00
恢复时间: 2025-07-01 15:04:31
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 2. 事件ID 2190: lbs-driving-svc 异常上涨

事件描述: lbs-driving-svc 异常上涨
发生时间: 2025-06-25 09:51:00
恢复时间: 2025-06-25 10:02:00
影响业务线: 货运
关联appid: lbs-driving-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 3. 事件ID 2187: 小拉大盘支付下跌

事件描述: 小拉大盘支付下跌
发生时间: 2025-06-20 20:04:00
恢复时间: 2025-06-20 20:30:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 4. 事件ID 2186: 小拉服务xl-lbs-driving-public-api异常上升

事件描述: 小拉服务xl-lbs-driving-public-api异常上升
发生时间: 2025-06-20 15:02:30
恢复时间: 2025-06-20 15:05:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 5. 事件ID 2183: bme-hpay-payment-svc服务异常上涨

事件描述: bme-hpay-payment-svc服务异常上涨
发生时间: 2025-06-07 13:47:11
恢复时间: 2025-06-07 14:07:11
影响业务线: LLM
关联appid: bme-hpay-payment-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 6. 事件ID 2181: 货运司机收不到e签宝验证码

事件描述: 货运司机收不到e签宝验证码
发生时间: 2025-06-04 11:58:32
恢复时间: 2025-06-04 12:12:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 7. 事件ID 2180: Cloudflare无法解析源主机名

事件描述: Cloudflare无法解析源主机名
发生时间: 2025-05-31 11:09:00
恢复时间: 2025-05-31 11:52:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 8. 事件ID 2173: 小拉虚拟号跌底

事件描述: 小拉虚拟号跌底
发生时间: 2025-05-15 10:04:00
恢复时间: 2025-05-15 10:28:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 9. 事件ID 2171: 货运大盘下单数同比上涨15%左右

事件描述: 货运大盘下单数同比上涨15%左右
发生时间: 2025-05-14 11:24:40
恢复时间: 2025-05-14 11:32:45
影响业务线: 货运
关联appid: bme-trade-payment-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bme-trade-payment-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 10. 事件ID 2170: 地图xl-map-open-api服务异常上涨

事件描述: 地图xl-map-open-api服务异常上涨
发生时间: 2025-05-14 10:34:34
恢复时间: 2025-05-14 10:41:34
影响业务线: 小拉
关联appid: xl-map-open-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 11. 事件ID 2166: ai-orderhall-api 抢单大厅pod流量不均衡

事件描述: ai-orderhall-api 抢单大厅pod流量不均衡
发生时间: 2025-05-09 06:02:00
恢复时间: 2025-05-09 09:37:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 12. 事件ID 2164: 司机反馈专送版更新之后打不开了

事件描述: 司机反馈专送版更新之后打不开了
发生时间: 2025-05-01 08:33:19
恢复时间: 2025-05-01 09:40:08
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 13. 事件ID 2163: 抢单大厅抖动

事件描述: 抢单大厅抖动
发生时间: 2025-04-30 15:25:09
恢复时间: 2025-04-30 15:26:09
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 14. 事件ID 2161: 大盘目的地检索指标跌底

事件描述: 大盘目的地检索指标跌底
发生时间: 2025-04-18 17:49:00
恢复时间: 2025-04-18 18:10:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 15. 事件ID 2157: 检索引擎服务异常导致地址检索有结果率下降

事件描述: 检索引擎服务异常导致地址检索有结果率下降
发生时间: 2025-04-14 09:31:00
恢复时间: 2025-04-14 10:07:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 16. 事件ID 2155: 多个核心服务出现异常抖动

事件描述: 多个核心服务出现异常抖动
发生时间: 2025-04-03 12:01:00
恢复时间: 2025-04-03 12:13:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 17. 事件ID 2153: 支付渠道异常

事件描述: 支付渠道异常
发生时间: 2025-04-02 11:02:00
恢复时间: 2025-04-02 11:08:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 18. 事件ID 2152:  ops-ops2-ucore-api异常

事件描述:  ops-ops2-ucore-api异常
发生时间: 2025-04-01 13:46:12
恢复时间: 2025-04-01 13:56:30
影响业务线: LLM
关联appid: ops-ops2-ucore-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 19. 事件ID 2151: 小拉大盘接单数 四轮小件抖动

事件描述: 小拉大盘接单数 四轮小件抖动
发生时间: 2025-04-01 12:12:44
恢复时间: 2025-04-01 12:32:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 20. 事件ID 2149: bfe-dapi-api RT增长较高

事件描述: bfe-dapi-api RT增长较高
发生时间: 2025-03-31 13:29:00
恢复时间: 2025-03-31 18:00:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 21. 事件ID 2148: ai-push-api服务异常上升

事件描述: ai-push-api服务异常上升
发生时间: 2025-03-28 14:42:00
恢复时间: 2025-03-28 15:00:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 22. 事件ID 2146: 小拉部分司机无法绑定银行卡提现

事件描述: 小拉部分司机无法绑定银行卡提现
发生时间: 2025-03-24 12:44:54
恢复时间: 2025-03-24 13:33:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 23. 事件ID 2144: 货运核心服务抖动

事件描述: 货运核心服务抖动
发生时间: 2025-03-21 09:42:00
恢复时间: 2025-03-21 09:45:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 24. 事件ID 2145: 机器磁盘满导致IDP bq task任务失败

事件描述: 机器磁盘满导致IDP bq task任务失败
发生时间: 2025-03-21 00:15:00
恢复时间: 2025-03-21 01:57:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 25. 事件ID 2143: 交易服务抖动

事件描述: 交易服务抖动
发生时间: 2025-03-14 18:47:00
恢复时间: 2025-03-14 18:53:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 26. 事件ID 2142: AWS新加坡机房的专线异常

事件描述: AWS新加坡机房的专线异常
发生时间: 2025-03-13 15:25:00
恢复时间: 2025-03-13 17:06:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 27. 事件ID 2141: 客服上报新司机合同无法签署，退出协议白屏

事件描述: 客服上报新司机合同无法签署，退出协议白屏
发生时间: 2025-03-13 15:21:01
恢复时间: 2025-03-13 15:48:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 28. 事件ID 2138: 货运服务lbs-map-svc异常升高

事件描述: 货运服务lbs-map-svc异常升高
发生时间: 2025-03-10 16:45:00
恢复时间: 2025-03-10 16:50:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 29. 事件ID 2137: 香港司机购买会员没有减免佣金

事件描述: 香港司机购买会员没有减免佣金
发生时间: 2025-03-05 14:40:00
恢复时间: 2025-03-05 15:37:25
影响业务线: LLM
关联appid: ai-pk-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 30. 事件ID 2136: 小拉支付成功数抖动

事件描述: 小拉支付成功数抖动
发生时间: 2025-03-03 19:43:00
恢复时间: 2025-03-03 19:46:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 31. 事件ID 2135: ai-orderhall-api等多个核心服务exception有一个尖刺

事件描述: ai-orderhall-api等多个核心服务exception有一个尖刺
发生时间: 2025-02-25 14:01:00
恢复时间: 2025-02-25 14:03:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 32. 事件ID 2133: bme-ucore-svc异常报错增多

事件描述: bme-ucore-svc异常报错增多
发生时间: 2025-02-21 09:05:00
恢复时间: 2025-02-21 09:39:00
影响业务线: LLM
关联appid: bme-ucore-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 33. 事件ID 2134: 货运司机准出服务RT上涨

事件描述: 货运司机准出服务RT上涨
发生时间: 2025-02-20 21:30:00
恢复时间: 2025-02-21 09:49:00
影响业务线: 货运
关联appid: risk-driver-exit-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(risk-driver-exit-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 34. 事件ID 2131: 计价算路调用百度大车RT超时

事件描述: 计价算路调用百度大车RT超时
发生时间: 2025-02-13 17:57:00
恢复时间: 2025-02-13 18:05:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内有1个其他操作，但非治理操作
  - AZ_ROUTER: 1次

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 35. 事件ID 2130: 小拉人脸识别下跌

事件描述: 小拉人脸识别下跌
发生时间: 2025-02-13 16:55:00
恢复时间: 2025-02-13 17:04:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内有1个其他操作，但非治理操作
  - AZ_ROUTER: 1次

---

#### 36. 事件ID 2129: 支付成功数下跌

事件描述: 支付成功数下跌
发生时间: 2025-02-11 14:18:00
恢复时间: 2025-02-11 14:22:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 37. 事件ID 2127: XL ai外呼无法呼出

事件描述: XL ai外呼无法呼出
发生时间: 2025-01-17 08:10:00
恢复时间: 2025-01-17 10:58:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 38. 事件ID 2123: APP一月费用金额统计不一致

事件描述: APP一月费用金额统计不一致
发生时间: 2025-01-10 10:03:00
恢复时间: 2025-01-10 13:31:00
影响业务线: 货运
关联appid: bme-trade-billinfo-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bme-trade-billinfo-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 39. 事件ID 2118: 外呼IVR身份识别接口QPS掉底

事件描述: 外呼IVR身份识别接口QPS掉底
发生时间: 2024-12-27 16:00:00
恢复时间: 2024-12-27 16:54:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 40. 事件ID 2116: bfe-uapi-api异常升高

事件描述: bfe-uapi-api异常升高
发生时间: 2024-12-23 15:40:00
恢复时间: 2024-12-23 15:44:30
影响业务线: LLM
关联appid: bfe-uapi-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 41. 事件ID 2110: dataMesh新版发布引起多个服务redis的RT上涨

事件描述: dataMesh新版发布引起多个服务redis的RT上涨
发生时间: 2024-12-10 06:06:00
恢复时间: 2024-12-10 09:35:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 42. 事件ID 2109: 多个核心服务异常报错

事件描述: 多个核心服务异常报错
发生时间: 2024-12-07 07:13:00
恢复时间: 2024-12-07 07:48:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 43. 事件ID 2107: 容器资源内存满导致多个服务抖动

事件描述: 容器资源内存满导致多个服务抖动
发生时间: 2024-11-29 14:16:00
恢复时间: 2024-11-29 14:17:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 44. 事件ID 2106: bme-information-fee-svc zone2 rt上涨

事件描述: bme-information-fee-svc zone2 rt上涨
发生时间: 2024-11-29 06:05:00
恢复时间: 2024-11-29 08:50:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 45. 事件ID 2101: TOC服务异常上涨RMQ内存水位上涨

事件描述: TOC服务异常上涨RMQ内存水位上涨
发生时间: 2024-11-15 01:12:10
恢复时间: 2024-11-15 10:09:00
影响业务线: 货运
关联appid: bme-trade-timeout-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 46. 事件ID 2100: 部分核心服务异常增长

事件描述: 部分核心服务异常增长
发生时间: 2024-11-14 15:30:46
恢复时间: 2024-11-14 15:32:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 47. 事件ID 2099: 客服进线掉底

事件描述: 客服进线掉底
发生时间: 2024-11-13 16:07:00
恢复时间: 2024-11-13 16:19:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 48. 事件ID 2096: 支付宝故障导致支付回调状态异常

事件描述: 支付宝故障导致支付回调状态异常
发生时间: 2024-11-11 09:17:00
恢复时间: 2024-11-11 13:06:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 49. 事件ID 2092: ai-orderhall-api Exception 达到   123.45 

事件描述: ai-orderhall-api Exception 达到   123.45 
发生时间: 2024-11-05 23:15:00
恢复时间: 2024-11-05 23:25:00
影响业务线: LLM
关联appid: ai-orderhall-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 50. 事件ID 2091: 画像服务DB failover

事件描述: 画像服务DB failover
发生时间: 2024-11-05 16:09:41
恢复时间: 2024-11-05 16:11:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 51. 事件ID 2090: 小拉大盘异常增长

事件描述: 小拉大盘异常增长
发生时间: 2024-11-03 02:43:00
恢复时间: 2024-11-03 02:43:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 52. 事件ID 2089: map-open-api调用bme-token-user-svc超时

事件描述: map-open-api调用bme-token-user-svc超时
发生时间: 2024-10-30 15:15:00
恢复时间: 2024-10-30 16:27:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 53. 事件ID 2087: bfe-global-aggr-wallet-svc RT抖动

事件描述: bfe-global-aggr-wallet-svc RT抖动
发生时间: 2024-10-24 20:09:35
恢复时间: 2024-10-24 20:16:04
影响业务线: LLM
关联appid: bfe-global-aggr-wallet-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 54. 事件ID 2086: 高峰期故障演练导致大盘波动

事件描述: 高峰期故障演练导致大盘波动
发生时间: 2024-10-24 17:59:11
恢复时间: 2024-10-24 18:03:00
影响业务线: 货运
关联appid: bme-trade-order-core-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bme-trade-order-core-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 55. 事件ID 2085: LApi - ECS丢包数量升高

事件描述: LApi - ECS丢包数量升高
发生时间: 2024-10-24 08:04:04
恢复时间: 2024-10-24 10:50:50
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 56. 事件ID 2081: 司机无法人脸识别登陆

事件描述: 司机无法人脸识别登陆
发生时间: 2024-10-01 00:00:00
恢复时间: 2024-10-01 10:11:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 57. 事件ID 2080: xl-ai-orderhall-api Exception Count 异常 > 100

事件描述: xl-ai-orderhall-api Exception Count 异常 > 100
发生时间: 2024-09-25 15:02:07
恢复时间: 2024-09-25 15:15:00
影响业务线: 小拉
关联appid: xl-ai-orderhall-api

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 58. 事件ID 2079: 新加坡马尼拉下单抖动（已恢复）

事件描述: 新加坡马尼拉下单抖动（已恢复）
发生时间: 2024-09-25 13:42:23
恢复时间: 2024-09-25 13:46:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 59. 事件ID 2076: 小拉服务抖动

事件描述: 小拉服务抖动
发生时间: 2024-09-20 13:43:37
恢复时间: 2024-09-20 13:45:00
影响业务线: 小拉
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 60. 事件ID 2074: 企业认证页面显示系统故障

事件描述: 企业认证页面显示系统故障
发生时间: 2024-09-12 09:30:00
恢复时间: 2024-09-12 09:34:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 61. 事件ID 2070: SG大盘下单&支付指标下跌

事件描述: SG大盘下单&支付指标下跌
发生时间: 2024-09-05 21:48:00
恢复时间: 2024-09-05 21:50:00
影响业务线: LLM
关联appid: bfe-global-pricing-user-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bfe-global-pricing-user-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 62. 事件ID 2069: 反馈司机无法收到短信验证码

事件描述: 反馈司机无法收到短信验证码
发生时间: 2024-09-03 10:16:04
恢复时间: 2024-09-03 10:22:16
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 63. 事件ID 2067: 中信银行故障导致托管银行记账失败

事件描述: 中信银行故障导致托管银行记账失败
发生时间: 2024-08-29 10:36:00
恢复时间: 2024-08-29 10:52:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 64. 事件ID 2062: br bme-account-svc Exception上涨

事件描述: br bme-account-svc Exception上涨
发生时间: 2024-08-23 14:13:00
恢复时间: 2024-08-23 14:59:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 65. 事件ID 2058: sg TOC回调异常

事件描述: sg TOC回调异常
发生时间: 2024-08-22 18:31:00
恢复时间: 2024-08-22 18:45:00
影响业务线: LLM
关联appid: bme-timeout-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bme-timeout-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 66. 事件ID 2055: BR 用户服务bfe-uapi-api 等核心服务 rt上涨

事件描述: BR 用户服务bfe-uapi-api 等核心服务 rt上涨
发生时间: 2024-08-20 10:50:20
恢复时间: 2024-08-20 11:08:00
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 67. 事件ID 2054: 抽佣开城放量计价服务异常

事件描述: 抽佣开城放量计价服务异常
发生时间: 2024-08-20 08:51:27
恢复时间: 2024-08-20 09:14:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 68. 事件ID 2049: rh完单调ci-lgw-inner-core-map-infra服务rt超时

事件描述: rh完单调ci-lgw-inner-core-map-infra服务rt超时
发生时间: 2024-08-16 16:44:15
恢复时间: 2024-08-16 15:45:38
影响业务线: LLM
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 69. 事件ID 2047: 南昌移动号码故障

事件描述: 南昌移动号码故障
发生时间: 2024-08-12 15:45:00
恢复时间: 2024-08-12 15:45:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 70. 事件ID 2046: 百度地图不可用

事件描述: 百度地图不可用
发生时间: 2024-08-07 13:00:00
恢复时间: 2024-08-07 13:07:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 71. 事件ID 2045: 兆维机房宕机

事件描述: 兆维机房宕机
发生时间: 2024-08-06 09:07:00
恢复时间: 2024-08-06 09:18:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 72. 事件ID 2043: 客服反馈河北省部分地区司机查看不到费用明细

事件描述: 客服反馈河北省部分地区司机查看不到费用明细
发生时间: 2024-08-04 17:13:00
恢复时间: 2024-08-04 17:15:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 73. 事件ID 2040: 货运部分服务exception突刺上涨

事件描述: 货运部分服务exception突刺上涨
发生时间: 2024-07-30 14:33:00
恢复时间: 2024-07-30 14:35:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

#### 74. 事件ID 2036: 个推服务异常上涨

事件描述: 个推服务异常上涨
发生时间: 2024-07-23 17:16:01
恢复时间: 2024-07-23 17:26:59
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 75. 事件ID 2034: 司机反馈接到订单进行中的订单都看不到显示连接失败

事件描述: 司机反馈接到订单进行中的订单都看不到显示连接失败
发生时间: 2024-07-22 11:12:00
恢复时间: 2024-07-22 11:33:00
影响业务线: 货运
关联appid: bfe-dapi-order-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bfe-dapi-order-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 76. 事件ID 2032: 司机退保垫付失败

事件描述: 司机退保垫付失败
发生时间: 2024-07-18 20:15:00
恢复时间: 2024-07-19 09:50:51
影响业务线: 货运
关联appid: bme-ticket-micro-svc

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件有明确的微服务appid(bme-ticket-micro-svc)，理论上可以使用控制面治理手段，但实际未使用

---

#### 77. 事件ID 2031: 天眼接口调用失败

事件描述: 天眼接口调用失败
发生时间: 2024-07-15 14:28:00
恢复时间: 2024-07-15 14:31:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用

---

#### 78. 事件ID 2030: 阿里云到人社部电信专线丢包率 大于10%

事件描述: 阿里云到人社部电信专线丢包率 大于10%
发生时间: 2024-07-13 09:23:00
恢复时间: 2024-07-13 10:27:00
影响业务线: 货运
关联appid: 

❌ 未发现使用SOA admin控制面进行服务治理
时间窗口内无任何SOA admin操作记录

---

## 总结

本报告提供了所有微服务问题事件的详细分析，包括治理措施的使用情况和具体效果。通过重新定义微服务问题的标准，更准确地识别了可以通过控制面治理的事件类型。
