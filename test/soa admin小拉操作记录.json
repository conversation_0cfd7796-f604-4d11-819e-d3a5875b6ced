[{"id": "411", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 10:26:29", "created_at": "2025-06-05 10:26:29"}, {"id": "410", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 10:26:29", "created_at": "2025-06-05 10:26:29"}, {"id": "409", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 10:26:10", "created_at": "2025-06-05 10:26:10"}, {"id": "408", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 10:26:10", "created_at": "2025-06-05 10:26:10"}, {"id": "407", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:28:01", "created_at": "2025-06-05 00:28:01"}, {"id": "406", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:28:01", "created_at": "2025-06-05 00:28:01"}, {"id": "405", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:57", "created_at": "2025-06-05 00:27:57"}, {"id": "404", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:57", "created_at": "2025-06-05 00:27:57"}, {"id": "403", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:54", "created_at": "2025-06-05 00:27:54"}, {"id": "402", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:54", "created_at": "2025-06-05 00:27:54"}, {"id": "401", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:49", "created_at": "2025-06-05 00:27:49"}, {"id": "400", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:49", "created_at": "2025-06-05 00:27:49"}, {"id": "399", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:37", "created_at": "2025-06-05 00:27:37"}, {"id": "398", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-05 00:27:37", "created_at": "2025-06-05 00:27:37"}, {"id": "397", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:40:38", "created_at": "2025-06-04 15:40:38"}, {"id": "396", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:40:38", "created_at": "2025-06-04 15:40:38"}, {"id": "395", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:38:14", "created_at": "2025-06-04 15:38:14"}, {"id": "394", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:38:14", "created_at": "2025-06-04 15:38:14"}, {"id": "393", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:26:06", "created_at": "2025-06-04 15:26:06"}, {"id": "392", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:26:06", "created_at": "2025-06-04 15:26:06"}, {"id": "391", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:25:54", "created_at": "2025-06-04 15:25:54"}, {"id": "390", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:25:54", "created_at": "2025-06-04 15:25:54"}, {"id": "389", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"id\": null, \"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"appId\": null, \"cluster\": null, \"deleted\": false, \"authAppId\": \"xl-bfe-hong-qi-svc\", \"createdAt\": 1749021928666, \"expiredAt\": 0, \"updatedAt\": 1749021928666, \"effectiveAt\": 1749021900672, \"operationType\": 1}", "deleted": 0, "updated_at": "2025-06-04 15:25:29", "created_at": "2025-06-04 15:25:29"}, {"id": "388", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"id\": null, \"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"appId\": null, \"cluster\": null, \"deleted\": false, \"authAppId\": \"xl-bfe-hong-qi-svc\", \"createdAt\": 1749021928666, \"expiredAt\": 0, \"updatedAt\": 1749021928666, \"effectiveAt\": 1749021900672, \"operationType\": 1}", "deleted": 0, "updated_at": "2025-06-04 15:25:29", "created_at": "2025-06-04 15:25:29"}, {"id": "387", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:24:40", "created_at": "2025-06-04 15:24:40"}, {"id": "386", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:24:40", "created_at": "2025-06-04 15:24:40"}, {"id": "385", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:24:31", "created_at": "2025-06-04 15:24:31"}, {"id": "384", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 15:24:31", "created_at": "2025-06-04 15:24:31"}, {"id": "383", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 2537711748000, \"effectiveAt\": 1748793344000, \"operationType\": 0}", "deleted": 0, "updated_at": "2025-06-04 14:40:28", "created_at": "2025-06-04 14:40:28"}, {"id": "382", "user_id": "cruise.xu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 2537711748000, \"effectiveAt\": 1748793344000, \"operationType\": 0}", "deleted": 0, "updated_at": "2025-06-04 14:40:28", "created_at": "2025-06-04 14:40:28"}, {"id": "381", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 00:00:13", "created_at": "2025-06-04 00:00:13"}, {"id": "380", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-04 00:00:13", "created_at": "2025-06-04 00:00:13"}, {"id": "379", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 2537711748589, \"effectiveAt\": 1748793344370, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:56:00", "created_at": "2025-06-03 23:56:00"}, {"id": "378", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 2537711748589, \"effectiveAt\": 1748793344370, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:56:00", "created_at": "2025-06-03 23:56:00"}, {"id": "377", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-03 23:35:32", "created_at": "2025-06-03 23:35:32"}, {"id": "376", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-03 23:35:32", "created_at": "2025-06-03 23:35:32"}, {"id": "375", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748792113544, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:35:26", "created_at": "2025-06-03 23:35:26"}, {"id": "374", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748792113544, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:35:26", "created_at": "2025-06-03 23:35:26"}, {"id": "373", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748792104575, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:35:10", "created_at": "2025-06-03 23:35:10"}, {"id": "372", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748792104575, \"operationType\": 2}", "deleted": 0, "updated_at": "2025-06-03 23:35:10", "created_at": "2025-06-03 23:35:10"}, {"id": "371", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-03 23:28:01", "created_at": "2025-06-03 23:28:01"}, {"id": "370", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-06-03 23:28:01", "created_at": "2025-06-03 23:28:01"}, {"id": "369", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-03 22:15:57", "created_at": "2025-06-03 22:15:57"}, {"id": "368", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\":\"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-06-03 22:15:57", "created_at": "2025-06-03 22:15:57"}, {"id": "367", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748960145681, \"operationType\": 1}", "deleted": 0, "updated_at": "2025-06-03 22:15:49", "created_at": "2025-06-03 22:15:49"}, {"id": "366", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "AUTH", "data": "{\"name\": \"cn.xiaola.hongqi.api.api.soa.ShenZhenNorthStationGatewaySoaService\", \"authAppId\": \"xl-bfe-hong-qi-svc\", \"expiredAt\": 0, \"effectiveAt\": 1748960145681, \"operationType\": 1}", "deleted": 0, "updated_at": "2025-06-03 22:15:49", "created_at": "2025-06-03 22:15:49"}, {"id": "365", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:34", "created_at": "2025-05-22 22:49:34"}, {"id": "364", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:34", "created_at": "2025-05-22 22:49:34"}, {"id": "363", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:27", "created_at": "2025-05-22 22:49:27"}, {"id": "362", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:27", "created_at": "2025-05-22 22:49:27"}, {"id": "361", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:23", "created_at": "2025-05-22 22:49:23"}, {"id": "360", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:49:23", "created_at": "2025-05-22 22:49:23"}, {"id": "359", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:39:07", "created_at": "2025-05-22 22:39:07"}, {"id": "358", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:39:07", "created_at": "2025-05-22 22:39:07"}, {"id": "357", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:38:59", "created_at": "2025-05-22 22:38:59"}, {"id": "356", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:38:59", "created_at": "2025-05-22 22:38:59"}, {"id": "355", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:29:48", "created_at": "2025-05-22 22:29:48"}, {"id": "354", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-22 22:29:48", "created_at": "2025-05-22 22:29:48"}, {"id": "353", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-ops-luna-read-svc@/luna/api/name=getPrivateReadToken\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:05:10", "created_at": "2025-05-20 22:05:10"}, {"id": "352", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-ops-luna-read-svc@/luna/api/name=getPrivateReadToken\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:05:10", "created_at": "2025-05-20 22:05:10"}, {"id": "351", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:05:01", "created_at": "2025-05-20 22:05:01"}, {"id": "350", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getId\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:05:01", "created_at": "2025-05-20 22:05:01"}, {"id": "349", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:04:56", "created_at": "2025-05-20 22:04:56"}, {"id": "348", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-uuid-svc@snowflakeIdService/getIds\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:04:56", "created_at": "2025-05-20 22:04:56"}, {"id": "347", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-jeye-svc@/getFaceImg\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:04:49", "created_at": "2025-05-20 22:04:49"}, {"id": "346", "user_id": "carter.zhang.xl", "app_id": "xl-bme-vtn-task", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-jeye-svc@/getFaceImg\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-20 22:04:49", "created_at": "2025-05-20 22:04:49"}, {"id": "345", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:19:07", "created_at": "2025-05-19 22:19:07"}, {"id": "344", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:19:07", "created_at": "2025-05-19 22:19:07"}, {"id": "343", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "CIRCUIT", "data": "{\"name\": \"xl-ops-fulfillment-api@/fulfillment/queryByPage\", \"enabled\": true, \"forceClose\": false, \"sleepWindowMS\": 5000, \"requestVolumeThreshold\": 50, \"errorThresholdPercentage\": 50.0}", "deleted": 0, "updated_at": "2025-05-19 22:18:44", "created_at": "2025-05-19 22:18:44"}, {"id": "342", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "CIRCUIT", "data": "{\"name\": \"xl-ops-fulfillment-api@/fulfillment/queryByPage\", \"enabled\": true, \"forceClose\": false, \"sleepWindowMS\": 5000, \"requestVolumeThreshold\": 50, \"errorThresholdPercentage\": 50.0}", "deleted": 0, "updated_at": "2025-05-19 22:18:44", "created_at": "2025-05-19 22:18:44"}, {"id": "341", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "LIMIT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/getOrderDetailV2\", \"appId\": \"xl-bme-trade-fund-svc\", \"status\": 0, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 10}", "deleted": 0, "updated_at": "2025-05-19 22:18:25", "created_at": "2025-05-19 22:18:25"}, {"id": "340", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "LIMIT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/getOrderDetailV2\", \"appId\": \"xl-bme-trade-fund-svc\", \"status\": 0, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 10}", "deleted": 0, "updated_at": "2025-05-19 22:18:25", "created_at": "2025-05-19 22:18:25"}, {"id": "339", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "LIMIT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/getOrderDetailV2\", \"appId\": \"xl-bme-trade-fund-svc\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 100}", "deleted": 0, "updated_at": "2025-05-19 22:17:28", "created_at": "2025-05-19 22:17:28"}, {"id": "338", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "LIMIT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/getOrderDetailV2\", \"appId\": \"xl-bme-trade-fund-svc\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 100}", "deleted": 0, "updated_at": "2025-05-19 22:17:28", "created_at": "2025-05-19 22:17:28"}, {"id": "337", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:11:11", "created_at": "2025-05-19 22:11:11"}, {"id": "336", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:11:11", "created_at": "2025-05-19 22:11:11"}, {"id": "335", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:04:07", "created_at": "2025-05-19 22:04:07"}, {"id": "334", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": false, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:04:07", "created_at": "2025-05-19 22:04:07"}, {"id": "333", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:03:49", "created_at": "2025-05-19 22:03:49"}, {"id": "332", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-fund-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-risk-gateway-svc@/gateway/?api=xl.risk.invoice.advancepay&apiVersion=1.0.0\", \"enabled\": true, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-19 22:03:49", "created_at": "2025-05-19 22:03:49"}, {"id": "331", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/batch/routes\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-15 16:08:55", "created_at": "2025-05-15 16:08:55"}, {"id": "330", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/batch/routes\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-15 16:08:55", "created_at": "2025-05-15 16:08:55"}, {"id": "329", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/driver/driverOrderList\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-12 22:05:33", "created_at": "2025-05-12 22:05:33"}, {"id": "328", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-trade-orderinfo-api@service/order/driver/driverOrderList\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-05-12 22:05:33", "created_at": "2025-05-12 22:05:33"}, {"id": "327", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-hong-qi-svc@/driver/black/deleteBatch\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:20:40", "created_at": "2025-05-07 11:20:40"}, {"id": "326", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-hong-qi-svc@/driver/black/deleteBatch\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:20:40", "created_at": "2025-05-07 11:20:40"}, {"id": "325", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:20:17", "created_at": "2025-05-07 11:20:17"}, {"id": "324", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:20:17", "created_at": "2025-05-07 11:20:17"}, {"id": "323", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:19:52", "created_at": "2025-05-07 11:19:52"}, {"id": "322", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:19:52", "created_at": "2025-05-07 11:19:52"}, {"id": "321", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:19:43", "created_at": "2025-05-07 11:19:43"}, {"id": "320", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:19:43", "created_at": "2025-05-07 11:19:43"}, {"id": "319", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/queryOrderListByScroll\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:17:58", "created_at": "2025-05-07 11:17:58"}, {"id": "318", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/queryOrderListByScroll\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-05-07 11:17:58", "created_at": "2025-05-07 11:17:58"}, {"id": "317", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-report-svc@/driver/white/deleteCache\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-04-28 10:22:05", "created_at": "2025-04-28 10:22:05"}, {"id": "316", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-report-svc@/driver/white/deleteCache\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-04-28 10:22:05", "created_at": "2025-04-28 10:22:05"}, {"id": "315", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"/cn.xiaola.report.svc.api.soa.service.WhiteDriverFacade/deleteCache\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-04-28 10:21:57", "created_at": "2025-04-28 10:21:57"}, {"id": "314", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"/cn.xiaola.report.svc.api.soa.service.WhiteDriverFacade/deleteCache\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-04-28 10:21:57", "created_at": "2025-04-28 10:21:57"}, {"id": "313", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/getRoutesForphp\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:40:25", "created_at": "2025-04-25 10:40:25"}, {"id": "312", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/getRoutesForphp\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:40:25", "created_at": "2025-04-25 10:40:25"}, {"id": "311", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/getRoutesForphp\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:40:11", "created_at": "2025-04-25 10:40:11"}, {"id": "310", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/position-query/getRoutesForphp\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:40:11", "created_at": "2025-04-25 10:40:11"}, {"id": "309", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:39:59", "created_at": "2025-04-25 10:39:59"}, {"id": "308", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-web", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:39:59", "created_at": "2025-04-25 10:39:59"}, {"id": "307", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:39:43", "created_at": "2025-04-25 10:39:43"}, {"id": "306", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-25 10:39:43", "created_at": "2025-04-25 10:39:43"}, {"id": "305", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 400, \"connectionTimeout\": 400}", "deleted": 0, "updated_at": "2025-04-25 10:38:45", "created_at": "2025-04-25 10:38:45"}, {"id": "304", "user_id": "jettyk.kuang.xl", "app_id": "xl-bme-trade-admin-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-lbs-dloc3-query-svc@/navigation/logs\", \"enabled\": true, \"readTimeout\": 400, \"connectionTimeout\": 400}", "deleted": 0, "updated_at": "2025-04-25 10:38:45", "created_at": "2025-04-25 10:38:45"}, {"id": "303", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-04-23 22:10:43", "created_at": "2025-04-23 22:10:43"}, {"id": "302", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-04-23 22:10:43", "created_at": "2025-04-23 22:10:43"}, {"id": "301", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-04-23 22:10:29", "created_at": "2025-04-23 22:10:29"}, {"id": "300", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-04-23 22:10:29", "created_at": "2025-04-23 22:10:29"}, {"id": "299", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eus-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/getEpPageOrderList\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-21 22:33:34", "created_at": "2025-04-21 22:33:34"}, {"id": "298", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eus-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/getEpPageOrderList\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-21 22:33:34", "created_at": "2025-04-21 22:33:34"}, {"id": "297", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-10 22:20:16", "created_at": "2025-04-10 22:20:16"}, {"id": "296", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-10 22:20:16", "created_at": "2025-04-10 22:20:16"}, {"id": "295", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-10 22:20:10", "created_at": "2025-04-10 22:20:10"}, {"id": "294", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-10 22:20:10", "created_at": "2025-04-10 22:20:10"}, {"id": "293", "user_id": "mason938.li.xl", "app_id": "xl-bfe-data-collect-svc", "type": "LIMIT", "data": "{\"name\": \"/cn.xiaola.data.collect.svc.api.soa.service.NeuronPlatformTriggerSoaService/sendCustomBehavior@*\", \"appId\": \"xl-bfe-data-collect-svc\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 50}", "deleted": 0, "updated_at": "2025-04-10 22:03:56", "created_at": "2025-04-10 22:03:56"}, {"id": "292", "user_id": "mason938.li.xl", "app_id": "xl-bfe-data-collect-svc", "type": "LIMIT", "data": "{\"name\": \"/cn.xiaola.data.collect.svc.api.soa.service.NeuronPlatformTriggerSoaService/sendCustomBehavior@*\", \"appId\": \"xl-bfe-data-collect-svc\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 50}", "deleted": 0, "updated_at": "2025-04-10 22:03:56", "created_at": "2025-04-10 22:03:56"}, {"id": "291", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-08 22:11:30", "created_at": "2025-04-08 22:11:30"}, {"id": "290", "user_id": "chengwei.wang.xl", "app_id": "xl-bfe-driver-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-ai-pk-api@/driver/pk/sign\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-04-08 22:11:30", "created_at": "2025-04-08 22:11:30"}, {"id": "289", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/export\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-24 22:34:03", "created_at": "2025-03-24 22:34:03"}, {"id": "288", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/pageConfig\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-24 22:33:53", "created_at": "2025-03-24 22:33:53"}, {"id": "287", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/export\", \"enabled\": true, \"readTimeout\": 10000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-24 22:30:46", "created_at": "2025-03-24 22:30:46"}, {"id": "286", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-aggregation-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-ops-csc-backend-svc@extTicketFacade/save\", \"enabled\": true, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-19 22:46:02", "created_at": "2025-03-19 22:46:02"}, {"id": "285", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-aggregation-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-ops-csc-backend-svc@extTicketFacade/save\", \"enabled\": true, \"readTimeout\": 2000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-19 22:46:02", "created_at": "2025-03-19 22:46:02"}, {"id": "284", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/confirmReceived\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:22:58", "created_at": "2025-03-13 23:22:58"}, {"id": "283", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/confirmReceived\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:22:58", "created_at": "2025-03-13 23:22:58"}, {"id": "282", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/cashSettle\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:40", "created_at": "2025-03-13 23:19:40"}, {"id": "281", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/cashSettle\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:40", "created_at": "2025-03-13 23:19:40"}, {"id": "280", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/getCashPayState\", \"enabled\": false, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:30", "created_at": "2025-03-13 23:19:30"}, {"id": "279", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/getCashPayState\", \"enabled\": false, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:30", "created_at": "2025-03-13 23:19:30"}, {"id": "278", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/getCashPayState\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:00", "created_at": "2025-03-13 23:19:00"}, {"id": "277", "user_id": "david.zhu.xl", "app_id": "xl-bfe-dfinance-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-convey-svc@cashPayFacade/getCashPayState\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-13 23:19:00", "created_at": "2025-03-13 23:19:00"}, {"id": "276", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/offLineExpiredConfig\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:40:38", "created_at": "2025-03-06 22:40:38"}, {"id": "275", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/offLineExpiredConfig\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:40:38", "created_at": "2025-03-06 22:40:38"}, {"id": "274", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-strategy-svc@strategyModifySoaService/batchModifyEffectStatus\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:40:18", "created_at": "2025-03-06 22:40:18"}, {"id": "273", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-strategy-svc@strategyModifySoaService/batchModifyEffectStatus\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:40:18", "created_at": "2025-03-06 22:40:18"}, {"id": "272", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/batchOperateRule\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:39:58", "created_at": "2025-03-06 22:39:58"}, {"id": "271", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/batchOperateRule\", \"enabled\": true, \"readTimeout\": 20000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-03-06 22:39:58", "created_at": "2025-03-06 22:39:58"}, {"id": "270", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-strategy-svc@/orderDriverSoaService/listConveyOrders\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-17 10:02:20", "created_at": "2025-02-17 10:02:20"}, {"id": "269", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bfe-strategy-svc@/orderDriverSoaService/listConveyOrders\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-17 10:02:20", "created_at": "2025-02-17 10:02:20"}, {"id": "268", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-transport-svc@/api/listByVehiclePlate\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-17 10:02:17", "created_at": "2025-02-17 10:02:17"}, {"id": "267", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-transport-svc@/api/listByVehiclePlate\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-17 10:02:17", "created_at": "2025-02-17 10:02:17"}, {"id": "266", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-14 17:35:28", "created_at": "2025-02-14 17:35:28"}, {"id": "265", "user_id": "david.zhu.xl", "app_id": "xl-bfe-hong-qi-api", "type": "DYNAMIC_LOG", "data": "{\"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2025-02-14 17:35:28", "created_at": "2025-02-14 17:35:28"}, {"id": "264", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-13 21:18:13", "created_at": "2025-02-13 21:18:13"}, {"id": "263", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-13 21:18:13", "created_at": "2025-02-13 21:18:13"}, {"id": "262", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-13 21:17:56", "created_at": "2025-02-13 21:17:56"}, {"id": "261", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-13 21:17:56", "created_at": "2025-02-13 21:17:56"}, {"id": "260", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:16:06", "created_at": "2025-02-11 10:16:06"}, {"id": "259", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:16:06", "created_at": "2025-02-11 10:16:06"}, {"id": "258", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:15:46", "created_at": "2025-02-11 10:15:46"}, {"id": "257", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"*@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:15:46", "created_at": "2025-02-11 10:15:46"}, {"id": "256", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:15:15", "created_at": "2025-02-11 10:15:15"}, {"id": "255", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@*/*\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:15:15", "created_at": "2025-02-11 10:15:15"}, {"id": "254", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:14:54", "created_at": "2025-02-11 10:14:54"}, {"id": "253", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@*/*\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:14:54", "created_at": "2025-02-11 10:14:54"}, {"id": "252", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:14:03", "created_at": "2025-02-11 10:14:03"}, {"id": "251", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-11 10:14:03", "created_at": "2025-02-11 10:14:03"}, {"id": "250", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:13:31", "created_at": "2025-02-11 10:13:31"}, {"id": "249", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-11 10:13:31", "created_at": "2025-02-11 10:13:31"}, {"id": "248", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-10 22:10:06", "created_at": "2025-02-10 22:10:06"}, {"id": "247", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"0\"}", "deleted": 0, "updated_at": "2025-02-10 22:10:06", "created_at": "2025-02-10 22:10:06"}, {"id": "246", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-10 22:09:46", "created_at": "2025-02-10 22:09:46"}, {"id": "245", "user_id": "damon.zhou.xl", "app_id": "xl-ci-jsonrpc-demo1-svr", "type": "DEGRADE", "data": "{\"name\":\"ci-jsonrpc-demo2-svr@/std/aBInnerHandle\",\"status\":\"1\"}", "deleted": 0, "updated_at": "2025-02-10 22:09:46", "created_at": "2025-02-10 22:09:46"}, {"id": "244", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eu-admin-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/queryOrderListByScroll\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-02-05 22:41:12", "created_at": "2025-02-05 22:41:12"}, {"id": "243", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eu-admin-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-order-es-api@service/es/order/queryOrderListByScroll\", \"enabled\": true, \"readTimeout\": 3000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-02-05 22:41:12", "created_at": "2025-02-05 22:41:12"}, {"id": "242", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/sendPlatformModelMsg\", \"enabled\": true, \"readTimeout\": 1300, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:14:19", "created_at": "2025-01-09 22:14:19"}, {"id": "241", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/sendPlatformModelMsg\", \"enabled\": true, \"readTimeout\": 1300, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:14:19", "created_at": "2025-01-09 22:14:19"}, {"id": "240", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryUrlScheme\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:14:03", "created_at": "2025-01-09 22:14:03"}, {"id": "239", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryUrlScheme\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:14:03", "created_at": "2025-01-09 22:14:03"}, {"id": "238", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/quickCheckStudentIdentity\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:55", "created_at": "2025-01-09 22:13:55"}, {"id": "237", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/quickCheckStudentIdentity\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:55", "created_at": "2025-01-09 22:13:55"}, {"id": "236", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/getPlatformUserInfo\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:48", "created_at": "2025-01-09 22:13:48"}, {"id": "235", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/getPlatformUserInfo\", \"enabled\": true, \"readTimeout\": 1000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:48", "created_at": "2025-01-09 22:13:48"}, {"id": "234", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/saveUserPlatformTag\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:40", "created_at": "2025-01-09 22:13:40"}, {"id": "233", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/saveUserPlatformTag\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:40", "created_at": "2025-01-09 22:13:40"}, {"id": "232", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryJsSdkTicket\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:30", "created_at": "2025-01-09 22:13:30"}, {"id": "231", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryJsSdkTicket\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:30", "created_at": "2025-01-09 22:13:30"}, {"id": "230", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryJsSdkTicket\", \"enabled\": false, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:25", "created_at": "2025-01-09 22:13:25"}, {"id": "229", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/queryJsSdkTicket\", \"enabled\": false, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:25", "created_at": "2025-01-09 22:13:25"}, {"id": "228", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/weChatAccessToken\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:12", "created_at": "2025-01-09 22:13:12"}, {"id": "227", "user_id": "david.zhu.xl", "app_id": "xl-bfe-user-svc", "type": "TIMEOUT", "data": "{\"name\": \"wechat-svc@/weChatAccessToken\", \"enabled\": true, \"readTimeout\": 800, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2025-01-09 22:13:12", "created_at": "2025-01-09 22:13:12"}, {"id": "226", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eu-admin-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-trade-admin-svr@/api/open/order/ep/list\", \"enabled\": true, \"readTimeout\": 6000, \"connectionTimeout\": 3000}", "deleted": 0, "updated_at": "2025-01-02 22:11:13", "created_at": "2025-01-02 22:11:13"}, {"id": "225", "user_id": "david.zhu.xl", "app_id": "xl-bfe-eu-admin-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bme-trade-admin-svr@/api/open/order/ep/list\", \"enabled\": true, \"readTimeout\": 6000, \"connectionTimeout\": 3000}", "deleted": 0, "updated_at": "2025-01-02 22:11:13", "created_at": "2025-01-02 22:11:13"}, {"id": "224", "user_id": "frank.wu.xl", "app_id": "xl-bfe-safety-business-svr", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-safety-svr@/recognitionUrlAsync\", \"enabled\": false, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2024-12-30 22:08:06", "created_at": "2024-12-30 22:08:06"}, {"id": "223", "user_id": "frank.wu.xl", "app_id": "xl-bfe-safety-business-svr", "type": "LIMIT", "data": "{\"name\": \"xl-bfe-safety-svr@/recognitionUrlAsync\", \"appId\": \"xl-bfe-safety-business-svr\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 6}", "deleted": 0, "updated_at": "2024-12-25 22:05:45", "created_at": "2024-12-25 22:05:45"}, {"id": "222", "user_id": "frank.wu.xl", "app_id": "xl-bfe-safety-business-svr", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-safety-svr@/recognitionUrlAsync\", \"enabled\": true, \"readTimeout\": 6000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2024-12-25 22:01:27", "created_at": "2024-12-25 22:01:27"}, {"id": "221", "user_id": "david.zhu.xl", "app_id": "xl-bfe-backend-api", "type": "TIMEOUT", "data": "{\"name\": \"xl-pricing-rule-svc@/intelligenceAdjustConfigV2/batchImport\", \"enabled\": true, \"readTimeout\": 5000, \"connectionTimeout\": 1000}", "deleted": 0, "updated_at": "2024-12-05 22:07:52", "created_at": "2024-12-05 22:07:52"}, {"id": "220", "user_id": "toretto.huang.xl", "app_id": "xl-bfe-intelligent-simulate-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bdi-dip-simulate-svc@/dip/multiFactor\", \"enabled\": true, \"readTimeout\": 8000, \"connectionTimeout\": 2000}", "deleted": 0, "updated_at": "2024-09-11 14:51:14", "created_at": "2024-09-11 14:51:14"}, {"id": "219", "user_id": "xiangfan.lu.xl", "app_id": "xl-ops-survey-gateway-api", "type": "LIMIT", "data": "{\"name\": \"/*/*@*\", \"appId\": \"xl-ops-survey-gateway-api\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 500}", "deleted": 0, "updated_at": "2024-09-09 22:34:52", "created_at": "2024-09-09 22:34:52"}, {"id": "218", "user_id": "xiangfan.lu.xl", "app_id": "xl-ops-survey-gateway-api", "type": "LIMIT", "data": "{\"name\": \"/*/*@*\", \"appId\": \"xl-ops-survey-gateway-api\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 100}", "deleted": 0, "updated_at": "2024-09-09 22:33:54", "created_at": "2024-09-09 22:33:54"}, {"id": "217", "user_id": "xiangfan.lu.xl", "app_id": "xl-ops-survey-gateway-api", "type": "LIMIT", "data": "{\"name\": \"/*/*@*\", \"appId\": \"xl-ops-survey-gateway-api\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 100}", "deleted": 0, "updated_at": "2024-09-09 22:33:46", "created_at": "2024-09-09 22:33:46"}, {"id": "216", "user_id": "xiangfan.lu.xl", "app_id": "xl-ops-survey-gateway-api", "type": "LIMIT", "data": "{\"name\": \"/*/*@*\", \"appId\": \"xl-ops-survey-gateway-api\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 10}", "deleted": 0, "updated_at": "2024-09-09 22:29:01", "created_at": "2024-09-09 22:29:01"}, {"id": "215", "user_id": "garry.zhang.xl", "app_id": "xl-ai-router-api", "type": "LIMIT", "data": "{\"name\": \"/cn.huolala.router.api.facade.IOrderPushFacade/aroundOrderCount@*\", \"appId\": \"xl-ai-router-api\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"provider\", \"tokenBucketCount\": 100}", "deleted": 0, "updated_at": "2024-09-09 22:06:02", "created_at": "2024-09-09 22:06:02"}, {"id": "214", "user_id": "toretto.huang.xl", "app_id": "xl-bfe-pricing-simulate-svc", "type": "TIMEOUT", "data": "{\"name\": \"xl-bfe-intelligent-simulate-svc@/intelligenceAdjust/calculateAdjustPrice\", \"enabled\": true, \"readTimeout\": 12000, \"connectionTimeout\": 5000}", "deleted": 0, "updated_at": "2024-09-04 18:47:45", "created_at": "2024-09-04 18:47:45"}, {"id": "213", "user_id": "mason938.li.xl", "app_id": "xl-bfe-ds-collect-svc", "type": "LIMIT", "data": "{\"name\": \"xl-ops-edu-svc@/getStudyLog\", \"appId\": \"xl-bfe-ds-collect-svc\", \"status\": 1, \"version\": \"\", \"limitMsg\": null, \"limitRet\": null, \"limitType\": \"TOKEN_BUCKET\", \"limitCount\": null, \"limitSource\": \"consumer\", \"tokenBucketCount\": 15}", "deleted": 0, "updated_at": "2024-09-02 22:00:38", "created_at": "2024-09-02 22:00:38"}, {"id": "212", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-bill-micro-svc@/service/bill/queryOrderBillBatch\", \"level\": \"DEBUG\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2024-07-22 15:29:41", "created_at": "2024-07-22 15:29:41"}, {"id": "211", "user_id": "david.zhu.xl", "app_id": "xl-bfe-report-svc", "type": "DYNAMIC_LOG", "data": "{\"name\": \"xl-bme-bill-micro-svc@/service/bill/queryOrderBillBatch\", \"level\": \"INFO\", \"enabled\": true, \"pattern\": \"*\"}", "deleted": 0, "updated_at": "2024-07-22 15:29:33", "created_at": "2024-07-22 15:29:33"}]