package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.kms.model.CipherSignature;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class CipherSignatureHandler extends JacksonTypeHandler {

    public CipherSignatureHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<CipherSignature>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
