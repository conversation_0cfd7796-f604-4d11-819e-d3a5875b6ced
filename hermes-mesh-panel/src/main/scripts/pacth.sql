#20220610
ALTER TABLE area_env ADD  `href` varchar(256) not null comment 'href';

#20220620
ALTER TABLE area_env ADD  `alert`  boolean default false comment '是否启用告警';
ALTER TABLE area_env ADD  `self`  boolean default false comment '是否同域';

#20220906
ALTER TABLE area_env  ADD  `kibana` text not null comment 'kibana跳转连接';

#20230302
ALTER TABLE issues_function  ADD  `tags` json not null comment '标签俩表';




#web端新增表字段调整
ALTER TABLE soa_request_log  ADD  `name` varchar(128) null comment '名称';
ALTER TABLE soa_request_log  ADD  `app_id` varchar(128) null  comment 'appId';
ALTER TABLE soa_request_log  ADD  `instance_id` varchar(128) null comment '实例';

#203230816
ALTER TABLE issues_tag ADD `links` json null comment '标签链接';


#20230302
ALTER TABLE open_accredit  ADD  `app_ids` json DEFAULT NULL COMMENT '授权的appIds';

#20240306
ALTER TABLE open_accredit  ADD  `config_app_id` varchar(128)  NULL COMMENT '配置关联appId';

#20240306
ALTER TABLE inspection_rule ADD `action` TEXT  NULL COMMENT '应急操作';
