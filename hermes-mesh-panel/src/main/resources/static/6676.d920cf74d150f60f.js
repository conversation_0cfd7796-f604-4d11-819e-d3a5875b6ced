"use strict";(self["webpackJsonp_hermes-mesh-ui"]=self["webpackJsonp_hermes-mesh-ui"]||[]).push([[6676],{55528:(O,R,o)=>{o.d(R,{J:()=>e});var r=o(54438),E=o(38520);let e=(()=>{class n{static#e=this.\u0275fac=function(v){return new(v||n)};static#n=this.\u0275cmp=r.VBU({type:n,selectors:[["app-block-quote"]],inputs:{content:"content"},decls:4,vars:1,consts:[[1,"block-quote"],["nz-typography",""]],template:function(v,f){1&v&&(r.j41(0,"blockquote",0)(1,"span",1)(2,"strong"),r.<PERSON>(3),r.k0s()()()),2&v&&(r.R7$(3),r.SpI(" ",f.content,""))},dependencies:[E.Di],styles:[".block-quote[_ngcontent-%COMP%]{line-height:1.6;font-size:14px;color:#666;margin-bottom:12px;padding:16px;border-left:3px solid #f16622;border-radius:0 2px 2px 0;background-color:#fafafa}"]})}return n})()},88058:(O,R,o)=>{o.d(R,{n:()=>_});var r=o(50906),E=o(67376),n=o(54438),j=o(77885),I=o(57094),v=o(2854),f=o(60177),T=o(23359),h=o(63523),m=o(97529),M=o(75699);function p(g,u){if(1&g&&(n.j41(0,"tr")(1,"td")(2,"nz-tag",7),n.EFF(3,"\u65b0\u589e"),n.k0s()(),n.j41(4,"td"),n.EFF(5),n.k0s(),n.j41(6,"td",8),n.EFF(7),n.k0s(),n.nrm(8,"td"),n.j41(9,"td"),n.EFF(10),n.k0s(),n.j41(11,"td"),n.EFF(12),n.nI1(13,"date"),n.k0s()()),2&g){const l=u.$implicit;n.R7$(5),n.JRh(l.key),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(l.value),n.R7$(3),n.JRh(l.dataChangeLastModifiedBy),n.R7$(2),n.JRh(n.i5U(13,5,l.dataChangeLastModifiedTime,"yyy-MM-dd HH:mm:ss"))}}function y(g,u){if(1&g&&(n.j41(0,"tr")(1,"td")(2,"nz-tag",9),n.EFF(3,"\u5220\u9664"),n.k0s()(),n.j41(4,"td"),n.EFF(5),n.k0s(),n.j41(6,"td",8),n.EFF(7),n.k0s(),n.nrm(8,"td"),n.j41(9,"td"),n.EFF(10),n.k0s(),n.j41(11,"td"),n.EFF(12),n.nI1(13,"date"),n.k0s()()),2&g){const l=u.$implicit;n.R7$(5),n.JRh(l.key),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(l.value),n.R7$(3),n.JRh(l.dataChangeLastModifiedBy),n.R7$(2),n.JRh(n.i5U(13,5,l.dataChangeLastModifiedTime,"yyy-MM-dd HH:mm:ss"))}}function b(g,u){if(1&g&&(n.j41(0,"tr")(1,"td")(2,"nz-tag",10),n.EFF(3,"\u66f4\u65b0"),n.k0s()(),n.j41(4,"td"),n.EFF(5),n.k0s(),n.j41(6,"td",8),n.EFF(7),n.k0s(),n.j41(8,"td",8),n.EFF(9),n.k0s(),n.j41(10,"td"),n.EFF(11),n.k0s(),n.j41(12,"td"),n.EFF(13),n.nI1(14,"date"),n.k0s()()),2&g){const l=u.$implicit;n.R7$(5),n.JRh(null==l.newItem?null:l.newItem.key),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(null==l.newItem?null:l.newItem.value),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(null==l.oldItem?null:l.oldItem.value),n.R7$(2),n.JRh(null==l.newItem?null:l.newItem.dataChangeLastModifiedBy),n.R7$(2),n.JRh(n.i5U(14,7,null==l.newItem?null:l.newItem.dataChangeLastModifiedTime,"yyy-MM-dd HH:mm:ss"))}}function k(g,u){if(1&g&&(n.qex(0),n.DNE(1,p,14,8,"tr",4)(2,y,14,8,"tr",4)(3,b,15,10,"tr",4),n.bVm()),2&g){const l=u.$implicit;n.R7$(),n.Y8G("ngForOf",l.item.createItems),n.R7$(),n.Y8G("ngForOf",l.item.deleteItems),n.R7$(),n.Y8G("ngForOf",l.item.updateItems)}}function S(g,u){if(1&g){const l=n.RV6();n.qex(0),n.j41(1,"nz-table",3,0)(3,"thead")(4,"tr")(5,"th"),n.EFF(6,"\u7c7b\u578b"),n.k0s(),n.j41(7,"th"),n.EFF(8,"key"),n.k0s(),n.j41(9,"th"),n.EFF(10,"new_value"),n.k0s(),n.j41(11,"th"),n.EFF(12,"old_value"),n.k0s(),n.j41(13,"th"),n.EFF(14,"\u4fee\u6539\u7528\u6237"),n.k0s(),n.j41(15,"th"),n.EFF(16,"\u4fee\u6539\u65f6\u95f4"),n.k0s()()(),n.j41(17,"tbody"),n.DNE(18,k,4,3,"ng-container",4),n.k0s()(),n.j41(19,"nz-row",5)(20,"nz-col")(21,"button",6),n.bIt("click",function(){n.eBV(l);const F=n.XpG();return n.Njj(F.next())}),n.EFF(22,"\u4e0b\u4e00\u9875"),n.k0s()()(),n.bVm()}if(2&g){const l=n.sdS(2),d=n.XpG();n.R7$(),n.Y8G("nzData",d.itemCommitModel)("nzPageSize",1e5),n.R7$(17),n.Y8G("ngForOf",l.data)}}let _=(()=>{class g extends r.m{constructor(l,d,F){super(F),this.configService=l,this.messageService=d,this.num=0,this.visible=!1,this.itemCommitModel=[]}open(l,d,F){this.appId=l,this.cluster=d,this.namespace=F,this.itemCommit(),this.visible=!0}itemCommit(){this.configService.itemCommit(this.appId,this.cluster,this.namespace,this.num,this.getPageSize()).subscribe(l=>{if(l.success){if((0,E.isEmpty)(l.data))return void this.messageService.info("\u65e0\u66f4\u591a\u6570\u636e");let d=l.data;(0,E.forEach)(d,F=>{F.item=JSON.parse(F.changeSets)}),this.itemCommitModel=(0,E.concat)(this.itemCommitModel,d)}})}close(){this.num=0,this.visible=!1,this.itemCommitModel=[]}next(){this.num++,this.itemCommit()}static#e=this.\u0275fac=function(d){return new(d||g)(n.rXU(j.w),n.rXU(I.xh),n.rXU(v.sj))};static#n=this.\u0275cmp=n.VBU({type:g,selectors:[["component-item-log"]],features:[n.Vt3],decls:2,vars:3,consts:[["basicTable",""],["nzTitle","\u4fee\u6539\u8bb0\u5f55","nzWidth","80%",3,"nzVisibleChange","nzOnCancel","nzVisible","nzFooter","nzMaskClosable"],[4,"nzModalContent"],["nzShowPagination","false",3,"nzData","nzPageSize"],[4,"ngFor","ngForOf"],["nzJustify","center",1,"margin-top-12"],["type","button",1,"ant-btn","ant-btn-primary",3,"click"],["nzColor","success"],[3,"nzBreakWord"],["nzColor","error"],["nzColor","warning"]],template:function(d,F){1&d&&(n.j41(0,"nz-modal",1),n.mxI("nzVisibleChange",function(P){return n.DH7(F.visible,P)||(F.visible=P),P}),n.bIt("nzOnCancel",function(){return F.close()}),n.DNE(1,S,23,3,"ng-container",2),n.k0s()),2&d&&(n.R50("nzVisible",F.visible),n.Y8G("nzFooter",null)("nzMaskClosable",!1))},dependencies:[f.Sq,T.CP,T.SO,T._4,T.IL,T.aj,T.kt,T.BC,h.F2,h.A9,m.Uq,m.e,M.s,f.vh]})}return g})()},46728:(O,R,o)=>{o.d(R,{q:()=>S});var r=o(50906),E=o(67376),n=o(54438),j=o(77885),I=o(57094),v=o(2854),f=o(60177),T=o(23359),h=o(63523),m=o(97529),M=o(75699);function p(_,g){if(1&_){const u=n.RV6();n.j41(0,"tr")(1,"td",10),n.EFF(2),n.k0s(),n.j41(3,"td"),n.EFF(4),n.k0s(),n.j41(5,"td"),n.EFF(6),n.k0s(),n.j41(7,"td"),n.EFF(8),n.nI1(9,"date"),n.k0s(),n.j41(10,"td")(11,"a",11),n.bIt("click",function(){const d=n.eBV(u).$implicit,F=n.XpG(2);return n.Njj(F.releaseDetail(d.releaseId,d.previousReleaseId))}),n.EFF(12,"\u67e5\u770b\u8be6\u60c5"),n.k0s()()()}if(2&_){const u=g.$implicit;n.R7$(2),n.JRh(u.releaseTitle),n.R7$(2),n.JRh(u.operator),n.R7$(2),n.JRh(u.releaseComment),n.R7$(2),n.JRh(n.i5U(9,4,u.releaseTime,"yyy-MM-dd HH:mm:ss"))}}function y(_,g){if(1&_){const u=n.RV6();n.qex(0),n.j41(1,"nz-table",5,0)(3,"thead")(4,"tr")(5,"th"),n.EFF(6,"\u53d1\u5e03\u6807\u9898"),n.k0s(),n.j41(7,"th"),n.EFF(8,"\u53d1\u5e03\u4eba"),n.k0s(),n.j41(9,"th"),n.EFF(10,"\u53d1\u5e03\u63cf\u8ff0"),n.k0s(),n.j41(11,"th"),n.EFF(12,"\u53d1\u5e03\u4e8b\u4ef6"),n.k0s(),n.j41(13,"th",6),n.EFF(14,"\u64cd\u4f5c"),n.k0s()()(),n.j41(15,"tbody"),n.DNE(16,p,13,7,"tr",7),n.k0s()(),n.j41(17,"nz-row",8)(18,"nz-col")(19,"button",9),n.bIt("click",function(){n.eBV(u);const d=n.XpG();return n.Njj(d.next())}),n.EFF(20,"\u4e0b\u4e00\u9875"),n.k0s()()(),n.bVm()}if(2&_){const u=n.sdS(2),l=n.XpG();n.R7$(),n.Y8G("nzData",l.releaseNamespaceModel),n.R7$(15),n.Y8G("ngForOf",u.data)}}function b(_,g){if(1&_&&(n.j41(0,"tr")(1,"td")(2,"nz-tag",12),n.EFF(3),n.k0s()(),n.j41(4,"td"),n.EFF(5),n.k0s(),n.j41(6,"td",13),n.EFF(7),n.k0s(),n.j41(8,"td",13),n.EFF(9),n.k0s()()),2&_){const u=g.$implicit;n.R7$(2),n.Y8G("nzColor","DELETED"==u.type?"error":"ADDED"==u.type?"success":"warning"),n.R7$(),n.SpI(" ","DELETED"==u.type?"\u5220\u9664":"ADDED"==u.type?"\u65b0\u589e":"\u4fee\u6539"," "),n.R7$(2),n.JRh(null==u.entity?null:u.entity.firstEntity.name),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(null==u.entity?null:u.entity.firstEntity.value),n.R7$(),n.Y8G("nzBreakWord",!0),n.R7$(),n.JRh(null==u.entity?null:u.entity.secondEntity.value)}}function k(_,g){if(1&_&&(n.qex(0),n.j41(1,"nz-table",5,1)(3,"thead")(4,"tr")(5,"th"),n.EFF(6,"\u7c7b\u578b"),n.k0s(),n.j41(7,"th"),n.EFF(8,"key"),n.k0s(),n.j41(9,"th"),n.EFF(10,"new_value"),n.k0s(),n.j41(11,"th"),n.EFF(12,"old_value"),n.k0s()()(),n.j41(13,"tbody"),n.DNE(14,b,10,7,"tr",7),n.k0s()(),n.bVm()),2&_){const u=n.sdS(2),l=n.XpG();n.R7$(),n.Y8G("nzData",l.releaseNamespaceDetailModel),n.R7$(13),n.Y8G("ngForOf",u.data)}}let S=(()=>{class _ extends r.m{constructor(u,l,d){super(d),this.configService=u,this.messageService=l,this.num=0,this.visible=!1,this.detailVisible=!1,this.releaseNamespaceModel=[],this.releaseNamespaceDetailModel=[]}open(u,l,d){this.appId=u,this.cluster=l,this.namespace=d,this.namespaceReleaseLog(),this.visible=!0}namespaceReleaseLog(){this.configService.namespaceReleaseLog(this.appId,this.cluster,this.namespace,this.num,this.getPageSize()).subscribe(u=>{u.success&&(this.releaseNamespaceModel=(0,E.concat)(this.releaseNamespaceModel,u.data),(0,E.isEmpty)(u.data)&&this.messageService.info("\u65e0\u66f4\u591a\u6570\u636e"))})}close(){this.num=0,this.visible=!1,this.releaseNamespaceModel=[],this.releaseNamespaceDetailModel=[]}next(){this.num++,this.namespaceReleaseLog()}closeDetail(){this.detailVisible=!1}releaseDetail(u,l){this.configService.namespaceReleaseDetail(u,l).subscribe(d=>{d.success&&(this.releaseNamespaceDetailModel=d.data?.changes,this.detailVisible=!0)})}static#e=this.\u0275fac=function(l){return new(l||_)(n.rXU(j.w),n.rXU(I.xh),n.rXU(v.sj))};static#n=this.\u0275cmp=n.VBU({type:_,selectors:[["component-namespace-release"]],features:[n.Vt3],decls:4,vars:6,consts:[["basicTable",""],["detailTable",""],["nzTitle","\u53d1\u5e03\u8bb0\u5f55","nzWidth","80%",3,"nzVisibleChange","nzOnCancel","nzVisible","nzFooter","nzMaskClosable"],[4,"nzModalContent"],["nzTitle","\u53d1\u5e03\u8be6\u60c5","nzWidth","60%",3,"nzVisibleChange","nzOnCancel","nzVisible","nzFooter","nzMaskClosable"],["nzShowPagination","false",3,"nzData"],["nzWidth","90px"],[4,"ngFor","ngForOf"],["nzJustify","center",1,"margin-top-12"],["type","button",1,"ant-btn","ant-btn-primary",3,"click"],["nzLeft",""],[3,"click"],[3,"nzColor"],[3,"nzBreakWord"]],template:function(l,d){1&l&&(n.j41(0,"nz-modal",2),n.mxI("nzVisibleChange",function(B){return n.DH7(d.visible,B)||(d.visible=B),B}),n.bIt("nzOnCancel",function(){return d.close()}),n.DNE(1,y,21,2,"ng-container",3),n.k0s(),n.j41(2,"nz-modal",4),n.mxI("nzVisibleChange",function(B){return n.DH7(d.detailVisible,B)||(d.detailVisible=B),B}),n.bIt("nzOnCancel",function(){return d.closeDetail()}),n.DNE(3,k,15,2,"ng-container",3),n.k0s()),2&l&&(n.R50("nzVisible",d.visible),n.Y8G("nzFooter",null)("nzMaskClosable",!1),n.R7$(2),n.R50("nzVisible",d.detailVisible),n.Y8G("nzFooter",null)("nzMaskClosable",!1))},dependencies:[f.Sq,T.CP,T.SO,T._4,T.IL,T.aj,T.kt,T.OL,T.BC,h.F2,h.A9,m.Uq,m.e,M.s,f.vh]})}return _})()},75120:(O,R,o)=>{o.d(R,{L:()=>h});var r=o(54438),E=o(9541),e=o(63523),n=o(70776),j=o(62e3),I=o(60177),v=o(96309);const f=()=>({height:"calc(100% - 55px)"});function T(m,M){if(1&m&&(r.qex(0),r.j41(1,"nz-tabset")(2,"nz-tab",2)(3,"nz-descriptions",3)(4,"nz-descriptions-item",4),r.EFF(5),r.k0s(),r.j41(6,"nz-descriptions-item",5),r.EFF(7),r.k0s(),r.j41(8,"nz-descriptions-item",6),r.EFF(9),r.k0s(),r.j41(10,"nz-descriptions-item",7),r.EFF(11),r.nI1(12,"actionType"),r.k0s(),r.j41(13,"nz-descriptions-item",8),r.EFF(14),r.k0s(),r.j41(15,"nz-descriptions-item",9),r.EFF(16),r.k0s(),r.j41(17,"nz-descriptions-item",10),r.EFF(18),r.nI1(19,"date"),r.k0s(),r.j41(20,"nz-descriptions-item",11),r.nrm(21,"ngx-json-viewer",12),r.k0s()()()(),r.bVm()),2&m){const p=r.XpG();r.R7$(4),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",p.logModel.appId," "),r.R7$(),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",p.logModel.cluster," "),r.R7$(),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",p.logModel.namespace," "),r.R7$(),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",r.bMT(12,17,p.logModel.actionType)," "),r.R7$(2),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",p.logModel.userId," "),r.R7$(),r.Y8G("nzSpan",1),r.R7$(),r.SpI(" ",p.logModel.success," "),r.R7$(),r.Y8G("nzSpan",3),r.R7$(),r.SpI(" ",r.i5U(19,19,p.logModel.createdAt,"yyy-MM-dd HH:mm:ss")," "),r.R7$(2),r.Y8G("nzSpan",3),r.R7$(),r.Y8G("json",p.logModel.memo)("expanded",!0)}}let h=(()=>{class m{constructor(){this.logModel={},this.configKeyModel=[],this.modalVisible=!1}open(p){this.logModel=p,this.configKeyModel=Object.keys(this.logModel),this.modalVisible=!0}static#e=this.\u0275fac=function(y){return new(y||m)};static#n=this.\u0275cmp=r.VBU({type:m,selectors:[["cipher-log-detail"]],decls:2,vars:4,consts:[["nzWidth","calc(80% - 118px)",3,"nzOnCancel","nzBodyStyle","nzVisible","nzFooter"],[4,"nzModalContent"],["nzTitle","\u4e8b\u4ef6\u8be6\u60c5"],["nzBordered",""],["nzTitle","AppId",3,"nzSpan"],["nzTitle","Cluster",3,"nzSpan"],["nzTitle","Namespace",3,"nzSpan"],["nzTitle","\u4e8b\u4ef6\u7c7b\u578b",3,"nzSpan"],["nzTitle","\u7528\u6237Id",3,"nzSpan"],["nzTitle","\u64cd\u4f5c\u72b6\u6001",3,"nzSpan"],["nzTitle","\u4e8b\u4ef6\u65f6\u95f4",3,"nzSpan"],["nzTitle","\u5907\u6ce8\u4fe1\u606f",3,"nzSpan"],[3,"json","expanded"]],template:function(y,b){1&y&&(r.j41(0,"nz-modal",0),r.bIt("nzOnCancel",function(){return b.modalVisible=!1}),r.DNE(1,T,22,22,"ng-container",1),r.k0s()),2&y&&r.Y8G("nzBodyStyle",r.lJ4(3,f))("nzVisible",b.modalVisible)("nzFooter",null)},dependencies:[E.JZ,E.rE,e.F2,e.A9,n.J,j.xA,j.gr,I.vh,v.j]})}return m})()},66676:(O,R,o)=>{o.r(R),o.d(R,{PlatformModule:()=>Ke});var r=o(60177),E=o(46530),e=o(54438);let n=(()=>{class a{static#e=this.\u0275fac=function(s){return new(s||a)};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["app-platform"]],decls:1,vars:0,template:function(s,i){1&s&&e.nrm(0,"router-outlet")},dependencies:[E.n3]})}return a})();var j=o(94101),I=o(50906),v=o(24609),f=o(67376),T=o(67562),h=o(2854),m=o(97529),M=o(25703),p=o(84341),y=o(18643),b=o(19448),k=o(5103),S=o(35154),_=o(23359),g=o(9541),u=o(50513),l=o(16389),d=o(25930),F=o(21011),B=o(38520),P=o(27120),L=o(86505),A=o(51813),N=o(77885),V=o(63523);const W=()=>({height:"calc(100% - 55px)"}),J=()=>({height:"calc(80% - 55px)"});function X(a,z){if(1&a){const t=e.RV6();e.j41(0,"button",13),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.doSearch())}),e.nrm(1,"span",14),e.k0s()}}function H(a,z){if(1&a){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"span",15)(3,"strong"),e.EFF(4),e.k0s()()(),e.j41(5,"td"),e.EFF(6),e.k0s(),e.j41(7,"td")(8,"a",16),e.bIt("click",function(){const i=e.eBV(t).$implicit,c=e.XpG(2);return e.Njj(c.appCluster(i.appId))}),e.EFF(9,"\u96c6\u7fa4"),e.k0s()()()}if(2&a){const t=z.$implicit;e.R7$(4),e.JRh(t.appId),e.R7$(2),e.SpI(" ",t.orgName," ")}}function Q(a,z){if(1&a){const t=e.RV6();e.qex(0),e.j41(1,"nz-input-group",6)(2,"input",7),e.mxI("ngModelChange",function(i){e.eBV(t);const c=e.XpG();return e.DH7(c.searchKey,i)||(c.searchKey=i),e.Njj(i)}),e.bIt("keyup",function(i){e.eBV(t);const c=e.XpG();return e.Njj(13===i.which?c.doSearch():null)}),e.k0s(),e.DNE(3,X,2,0,"ng-template",null,0,e.C5r),e.k0s(),e.j41(5,"nz-table",8,1)(7,"thead")(8,"tr")(9,"th",9),e.EFF(10,"APPID"),e.k0s(),e.j41(11,"th",10),e.EFF(12,"\u90e8\u95e8"),e.k0s(),e.j41(13,"th",11),e.EFF(14,"\u64cd\u4f5c"),e.k0s()()(),e.j41(15,"tbody"),e.DNE(16,H,10,2,"tr",12),e.k0s()(),e.bVm()}if(2&a){const t=e.sdS(4),s=e.sdS(6),i=e.XpG();e.R7$(),e.Y8G("nzAddOnAfter",t),e.R7$(),e.R50("ngModel",i.searchKey),e.R7$(3),e.Y8G("nzData",i.appModel),e.R7$(11),e.Y8G("ngForOf",s.data)}}function Z(a,z){if(1&a){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"span",15)(3,"strong"),e.EFF(4),e.k0s()()(),e.j41(5,"td")(6,"a",18),e.bIt("click",function(){const i=e.eBV(t).$implicit,c=e.XpG(2);return e.Njj(c.cipherInit(i.appId,i.name))}),e.EFF(7,"\u9009\u62e9"),e.k0s()()()}if(2&a){const t=z.$implicit;e.R7$(4),e.JRh(t.name)}}function w(a,z){if(1&a&&(e.qex(0),e.j41(1,"nz-table",8,2)(3,"thead")(4,"tr")(5,"th",17),e.EFF(6,"Cluster"),e.k0s(),e.j41(7,"th",11),e.EFF(8,"\u64cd\u4f5c"),e.k0s()()(),e.j41(9,"tbody"),e.DNE(10,Z,8,1,"tr",12),e.k0s()(),e.bVm()),2&a){const t=e.sdS(2),s=e.XpG();e.R7$(),e.Y8G("nzData",s.appClusterModel),e.R7$(9),e.Y8G("ngForOf",t.data)}}let q=(()=>{class a{constructor(t,s){this.kmsService=t,this.configService=s,this.appModel=[],this.searchKey="",this.appStoreModel=[],this.appClusterModel=[],this.modalVisible=!1,this.clusterModelVisible=!1,this.refreshEvent=new e.bkB}open(){this.apps(),this.modalVisible=!0}close(){this.modalVisible=!1,this.clusterModelVisible=!1}apps(){this.configService.apps().subscribe(t=>{t.success&&((0,f.forEach)(t.data,s=>{s.name=s.appId}),this.appModel=t.data,this.appStoreModel=t.data)})}appCluster(t){this.configService.appNav(t).subscribe(s=>{s.success&&(this.appClusterModel=s.data.clusters,this.clusterModelVisible=!0)})}cipherInit(t,s){this.kmsService.cipherInit(t,s).subscribe(i=>{i.success&&(this.refreshEvent.emit(),this.close())})}doSearch(){this.appModel=A.G.search(this.searchKey,this.appStoreModel)}static#e=this.\u0275fac=function(s){return new(s||a)(e.rXU(T.p),e.rXU(N.w))};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["cipher-cipher-init"]],outputs:{refreshEvent:"refreshEvent"},decls:4,vars:8,consts:[["suffixButtonTemplate",""],["appModelTable",""],["appClusterModelTable",""],["nzWidth","calc(80% - 118px)","nzTitle","AppId\u5217\u8868",3,"nzOnCancel","nzBodyStyle","nzVisible","nzFooter"],[4,"nzModalContent"],["nzWidth","calc(40% - 118px)","nzTitle","\u96c6\u7fa4\u5217\u8868",3,"nzOnCancel","nzBodyStyle","nzVisible","nzFooter"],["nzSearch","",3,"nzAddOnAfter"],["type","text","placeholder","\u8bf7\u8f93\u5165\u6240\u9700\u68c0\u7d22\u7684AppId","nz-input","","autofocus","",3,"ngModelChange","keyup","ngModel"],["nzShowSizeChanger","","nzSimple","",3,"nzData"],["nz-tooltip","","nzTooltipTitle","\u670d\u52a1\u6240\u914d\u7f6e\u7684APPID"],["nz-tooltip","","nzTooltipTitle","\u670d\u52a1\u6240\u914d\u7f6e\u7684AppId\u90e8\u95e8"],["nzWidth","60px"],[4,"ngFor","ngForOf"],["nz-button","","nzType","primary","nzSearch","","nz-tooltip","","nzTooltipTitle","\u521d\u59cb\u5316AppId\u52a0\u5bc6\u529f\u80fd",3,"click"],["nz-icon","","nzType","search"],["nz-typography",""],["nz-tooltip","","nzTooltipTitle","\u8bf7\u9009\u62e9\u96c6\u7fa4",3,"click"],["nz-tooltip","","nzTooltipTitle","\u914d\u7f6e\u6240\u5728\u7684\u96c6\u7fa4\u5217\u8868"],["nz-tooltip","","nzTooltipTitle","\u521d\u59cb\u5316\u6b64\u96c6\u7fa4\u7684\u52a0\u5bc6\u529f\u80fd",3,"click"]],template:function(s,i){1&s&&(e.j41(0,"nz-modal",3),e.bIt("nzOnCancel",function(){return i.modalVisible=!1}),e.DNE(1,Q,17,4,"ng-container",4),e.k0s(),e.j41(2,"nz-modal",5),e.bIt("nzOnCancel",function(){return i.clusterModelVisible=!1}),e.DNE(3,w,11,2,"ng-container",4),e.k0s()),2&s&&(e.Y8G("nzBodyStyle",e.lJ4(6,W))("nzVisible",i.modalVisible)("nzFooter",null),e.R7$(2),e.Y8G("nzBodyStyle",e.lJ4(7,J))("nzVisible",i.clusterModelVisible)("nzFooter",null))},dependencies:[r.Sq,p.me,p.BC,p.vS,k.Dn,S.LH,_.CP,_.SO,_._4,_.IL,_.aj,_.kt,u.aO,l.c,d.p,F.Sy,F.tg,V.F2,V.A9,B.Di]})}return a})();var ee=o(75120),U=o(70776),G=o(62e3);const ne=()=>({height:"calc(100% - 55px)"});function te(a,z){if(1&a&&(e.j41(0,"nz-descriptions",4)(1,"nz-descriptions-item",5),e.EFF(2),e.k0s(),e.j41(3,"nz-descriptions-item",6),e.EFF(4),e.k0s(),e.j41(5,"nz-descriptions-item",7),e.nrm(6,"ngx-json-viewer",8),e.k0s(),e.j41(7,"nz-descriptions-item",9),e.nrm(8,"ngx-json-viewer",8),e.k0s()()),2&a){const t=z.$implicit;e.R7$(),e.Y8G("nzSpan",2),e.R7$(),e.SpI(" ",t.name," "),e.R7$(),e.Y8G("nzSpan",2),e.R7$(),e.SpI(" ",t.version," "),e.R7$(),e.Y8G("nzSpan",4),e.R7$(),e.Y8G("json",t.privateSecret)("expanded",!0),e.R7$(),e.Y8G("nzSpan",4),e.R7$(),e.Y8G("json",t.publicSecret)("expanded",!0)}}function ie(a,z){if(1&a&&(e.qex(0),e.j41(1,"nz-tabset")(2,"nz-tab",2),e.DNE(3,te,9,10,"nz-descriptions",3),e.k0s()(),e.bVm()),2&a){const t=e.XpG();e.R7$(3),e.Y8G("ngForOf",t.signatureModel)}}let ae=(()=>{class a extends I.m{constructor(){super(...arguments),this.signatureModel=[],this.modalVisible=!1}open(t){this.signatureModel=t,this.modalVisible=!0}close(){this.signatureModel=[],this.modalVisible=!1}static#e=this.\u0275fac=(()=>{let t;return function(i){return(t||(t=e.xGo(a)))(i||a)}})();static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["cipher-signature-detail"]],features:[e.Vt3],decls:2,vars:4,consts:[["nzWidth","calc(80% - 118px)",3,"nzOnCancel","nzBodyStyle","nzVisible","nzFooter"],[4,"nzModalContent"],["nzTitle","\u79d8\u94a5\u8be6\u60c5"],["nzBordered","",4,"ngFor","ngForOf"],["nzBordered",""],["nzTitle","\u540d\u79f0",3,"nzSpan"],["nzTitle","\u7248\u672c",3,"nzSpan"],["nzTitle","\u79c1\u94a5",3,"nzSpan"],[3,"json","expanded"],["nzTitle","\u516c\u94a5",3,"nzSpan"]],template:function(s,i){1&s&&(e.j41(0,"nz-modal",0),e.bIt("nzOnCancel",function(){return i.close()}),e.DNE(1,ie,4,1,"ng-container",1),e.k0s()),2&s&&e.Y8G("nzBodyStyle",e.lJ4(3,ne))("nzVisible",i.modalVisible)("nzFooter",null)},dependencies:[r.Sq,g.JZ,g.rE,V.F2,V.A9,U.J,G.xA,G.gr]})}return a})();var oe=o(25784),se=o(96309);const re=["signatureDetailComponent"];function le(a,z){if(1&a){const t=e.RV6();e.j41(0,"div",33)(1,"nz-range-picker",34),e.mxI("ngModelChange",function(i){e.eBV(t);const c=e.XpG();return e.DH7(c.metricDate,i)||(c.metricDate=i),e.Njj(i)}),e.bIt("ngModelChange",function(i){e.eBV(t);const c=e.XpG();return e.Njj(c.onPickerChange(i))}),e.k0s()()}if(2&a){const t=e.XpG();e.R7$(),e.Y8G("nzRanges",t.ranges),e.R50("ngModel",t.metricDate)}}function ce(a,z){1&a&&(e.j41(0,"button",35),e.nrm(1,"span",36),e.k0s())}function pe(a,z){if(1&a){const t=e.RV6();e.j41(0,"tr")(1,"td")(2,"span",37)(3,"strong"),e.EFF(4),e.k0s()()(),e.j41(5,"td"),e.EFF(6),e.k0s(),e.j41(7,"td"),e.EFF(8),e.k0s(),e.j41(9,"td"),e.EFF(10),e.k0s(),e.j41(11,"td"),e.EFF(12),e.nI1(13,"date"),e.k0s(),e.j41(14,"td"),e.EFF(15),e.nI1(16,"date"),e.k0s(),e.j41(17,"td")(18,"a",38),e.bIt("click",function(){const i=e.eBV(t).$implicit,c=e.XpG();return e.Njj(c.signature(i.id))}),e.EFF(19,"\u79d8\u94a5"),e.k0s(),e.nrm(20,"nz-divider",39),e.j41(21,"a",40),e.bIt("click",function(){const i=e.eBV(t).$implicit,c=e.XpG();return e.Njj(c.repairCipher(i.appId,i.cluster))}),e.EFF(22,"\u4fee\u590d"),e.k0s(),e.nrm(23,"nz-divider",39),e.j41(24,"a",41),e.bIt("click",function(){const i=e.eBV(t).$implicit,c=e.XpG();return e.Njj(c.deleteCipher(i.id))}),e.EFF(25,"\u5220\u9664"),e.k0s()()()}if(2&a){const t=z.$implicit;e.R7$(4),e.JRh(t.appId),e.R7$(2),e.JRh(t.cluster),e.R7$(2),e.JRh(t.namespace),e.R7$(2),e.JRh(t.version),e.R7$(2),e.JRh(e.i5U(13,6,t.updatedAt,"yyy-MM-dd HH:mm:ss")),e.R7$(3),e.JRh(e.i5U(16,9,t.createdAt,"yyy-MM-dd HH:mm:ss"))}}function ue(a,z){1&a&&e.EFF(0),2&a&&e.SpI(" \u603b\u5171",z.$implicit," \u6761 ")}function me(a,z){if(1&a){const t=e.RV6();e.j41(0,"a",42),e.bIt("click",function(){e.eBV(t),e.XpG();const i=e.sdS(54);return e.Njj(i.open())}),e.nrm(1,"span",43),e.k0s()}}function de(a,z){if(1&a){const t=e.RV6();e.j41(0,"button",44),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.cipherCredentialsLog())}),e.nrm(1,"span",36),e.k0s()}}function _e(a,z){if(1&a&&(e.qex(0),e.j41(1,"p")(2,"span",48),e.EFF(3),e.nI1(4,"ellipsis"),e.k0s()(),e.bVm()),2&a){const t=e.XpG().$implicit;e.R7$(3),e.SpI("Memo : ",e.i5U(4,1,t.memo,40),"")}}function he(a,z){if(1&a){const t=e.RV6();e.j41(0,"nz-timeline-item",45)(1,"p"),e.EFF(2),e.nI1(3,"date"),e.j41(4,"a",46),e.bIt("click",function(){const i=e.eBV(t).$implicit;e.XpG();const c=e.sdS(56);return e.Njj(c.open(i))}),e.nrm(5,"span",47),e.k0s()(),e.j41(6,"p"),e.EFF(7,"AppId : "),e.j41(8,"span",37)(9,"strong"),e.EFF(10),e.k0s()(),e.j41(11,"span",48),e.EFF(12),e.k0s(),e.j41(13,"span",48),e.EFF(14),e.k0s()(),e.j41(15,"p"),e.EFF(16,"\u7c7b\u578b : "),e.j41(17,"span",37)(18,"strong"),e.EFF(19),e.nI1(20,"actionType"),e.k0s()()(),e.j41(21,"p"),e.EFF(22,"\u7528\u6237 : "),e.j41(23,"span",37)(24,"strong"),e.EFF(25),e.k0s()()(),e.DNE(26,_e,5,4,"ng-container",49),e.k0s()}if(2&a){const t=z.$implicit;e.Y8G("nzColor",t.success?"green":"red"),e.R7$(2),e.SpI(" ",e.i5U(3,8,t.createdAt,"yyy-MM-dd HH:mm:ss")," "),e.R7$(8),e.JRh(t.appId),e.R7$(2),e.JRh(t.cluster),e.R7$(2),e.JRh(t.namespace),e.R7$(5),e.JRh(e.bMT(20,11,t.actionType)),e.R7$(6),e.JRh(t.userId),e.R7$(),e.Y8G("ngIf",t.memo)}}function ze(a,z){if(1&a&&(e.qex(0),e.EFF(1),e.bVm()),2&a){const t=e.XpG(2);e.R7$(),e.SpI(" ",t.searchEventPointKey," ")}}function ge(a,z){1&a&&e.EFF(0," \u65e5\u5fd7\u5217\u8868 ")}function Ce(a,z){if(1&a&&e.DNE(0,ze,2,1,"ng-container",50)(1,ge,1,0,"ng-template",null,11,e.C5r),2&a){const t=e.sdS(2),s=e.XpG();e.Y8G("ngIf",""!=s.searchEventPointKey)("ngIfElse",t)}}function Fe(a,z){if(1&a){const t=e.RV6();e.qex(0),e.j41(1,"a",51),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.searchEventPoint({extra:{ids:[]},name:""}))}),e.nrm(2,"span",52),e.k0s(),e.bVm()}}function Ee(a,z){if(1&a&&e.DNE(0,Fe,3,0,"ng-container",49),2&a){const t=e.XpG();e.Y8G("ngIf",""!=t.searchEventPointKey)}}let fe=(()=>{class a extends I.m{constructor(t,s){super(s),this.cipherService=t,this.searchKey="",this.searchLogKey="",this.searchEventPointIds=[],this.searchEventPointKey="",this.cipherCredentialsModel={},this.cipherCredentialsLogModel={},this.cipherCredentialsLogMetricModel=[],this.cipherCredentialsLogStoreMetricModel=[],this.LegendPosition=j.PA,this.metricDate=[(0,v.kO)(new Date,"d",-1),new Date]}ngOnInit(){this.metrics(),this.cipherCredentials(),this.cipherCredentialsLog()}metrics(){this.cipherService.cipherCredentialsLogMetrics(this.getStartTime(),this.getEndTime()).subscribe(t=>{t.success&&(this.cipherCredentialsLogMetricModel=t.data,this.cipherCredentialsLogStoreMetricModel=t.data)})}cipherCredentials(t=0){this.cipherService.cipherCredentials(this.searchKey,t,this.getPageSize()).subscribe(s=>{s.success&&(this.cipherCredentialsModel=s.data)})}cipherCredentialsLog(t=0){this.cipherService.cipherCredentialsLog(this.searchLogKey,this.searchEventPointIds,t,6).subscribe(s=>{s.success&&(this.cipherCredentialsLogModel=s.data)})}searchEventPoint(t){if((0,f.isString)(t))if("\u6240\u6709\u7c7b\u578b"==t)this.cipherCredentialsLogMetricModel=this.cipherCredentialsLogStoreMetricModel;else{let s=[];(0,f.forEach)(this.cipherCredentialsLogStoreMetricModel,i=>{s.push(i.name==t?i:{name:i.name,series:[]})}),this.cipherCredentialsLogMetricModel=s}else this.searchEventPointKey=t.name,this.searchEventPointIds=t.extra.ids,this.cipherCredentialsLog(0)}onPickerChange(t){super.putPickerChange(t),this.metrics()}onPageSizeChange(t){super.putPageSize(t),this.cipherCredentials()}onLogPageSizeChange(t){super.putPageSize(t),this.cipherCredentialsLog()}deleteCipher(t){this.cipherService.deleteCipher(t).subscribe(s=>{s.success&&this.ngOnInit()})}signature(t){this.cipherService.cipherSignature(t).subscribe(s=>{s.success&&this.signatureDetailComponent.open(s.data)})}repairCipher(t,s){this.cipherService.cipherInit(t,s).subscribe(i=>{i.success&&this.ngOnInit()})}static#e=this.\u0275fac=function(s){return new(s||a)(e.rXU(T.p),e.rXU(h.sj))};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["app-cipher"]],viewQuery:function(s,i){if(1&s&&e.GBs(re,5),2&s){let c;e.mGM(c=e.lsd())&&(i.signatureDetailComponent=c.first)}},features:[e.Vt3],decls:59,vars:28,consts:[["eventMetricTemplate",""],["suffixButtonTemplate",""],["basicTable",""],["totalTemplate",""],["extraTemplate",""],["eventSuffixIconButton",""],["eventCardListTemplate",""],["eventCardListExtraTemplate",""],["cipherInitComponent",""],["logDetailComponent",""],["signatureDetailComponent",""],["defaultTitleTemplate",""],["nzGutter","12"],["nzSpan","18"],["nzTitle","\u64cd\u4f5c\u6982\u89c8",1,"shadow-card",3,"nzBorderless","nzExtra"],[1,"below-chart","chart-height-300"],["scheme","nightLights","legendTitle","",3,"select","results","yAxis","xAxis","roundDomains","autoScale","legend","legendPosition"],[3,"nzTabBarExtraContent"],["nzTitle","\u5f00\u901a\u5217\u8868"],["nzSearch","",3,"nzAddOnAfter"],["type","text","placeholder","\u8bf7\u8f93\u5165\u6240\u9700\u68c0\u7d22\u7684AppId","nz-input","","autofocus","",3,"ngModelChange","keyup","ngModel"],["nzShowSizeChanger","",3,"nzPageIndexChange","nzPageSizeChange","nzData","nzShowTotal","nzTotal","nzPageSize","nzPageIndex","nzFrontPagination"],["nzWidth","160px"],[4,"ngFor","ngForOf"],["nzSpan","6"],[1,"shadow-card",3,"nzBorderless","nzTitle","nzExtra"],["nzSearch","",1,"margin-bottom-12",3,"nzAddOnAfter"],["type","text","placeholder","\u8bf7\u8f93\u5165\u6240\u9700\u68c0\u7d22\u7684\u5b9e\u4f8b\u4fe1\u606f","nz-input","",3,"ngModelChange","keyup","ngModel"],[1,"margin-top-12"],[3,"nzColor",4,"ngFor","ngForOf"],["nzJustify","end"],["nzSimple","",3,"nzPageIndexChange","nzPageIndex","nzPageSize","nzTotal"],[3,"refreshEvent"],[1,"margin-right-12"],["nzShowTime","","nzFormat","yyyy/MM/dd HH:mm:ss","ngModel","",3,"ngModelChange","nzRanges","ngModel"],["nz-button","","nzType","primary","nzSearch",""],["nz-icon","","nzType","search"],["nz-typography",""],["nz-tooltip","","nzTooltipTitle","\u67e5\u770b\u6b64AppId\u7684\u516c\u79c1\u94a5\u4fe1\u606f,\u516c\u79c1\u94a5\u4ee5Base64\u7f16\u7801\u5f62\u5f0f\u5448\u73b0",3,"click"],["nzType","vertical"],["nz-tooltip","","nzTooltipTitle","\u91cd\u65b0\u521d\u59cb\u5316\u6b64\u52a0\u5bc6\u529f\u80fd\uff0c\u6b64\u64cd\u4f5c\u4fee\u590d\u73b0\u6709\u521d\u59cb\u5316\u6d41\u7a0b\u5931\u8d25\u7684\u573a\u666f",3,"click"],["nz-tooltip","","nzTooltipTitle","\u5c06\u7981\u7528\u52a0\u5bc6\u529f\u80fd\uff0c\u5e76\u5220\u9664\u516c\u79c1\u94a5\u4fe1\u606f\u673a\u5668\u4e1a\u52a1\u914d\u7f6e\u4fe1\u606f",3,"click"],["nz-tooltip","","nzTooltipTitle","\u65b0\u589e\u5f15\u7528\u521d\u59cb\u5316",3,"click"],["nz-icon","","nzType","plus","nzTheme","outline"],["nz-button","","nzType","primary","nzSearch","",3,"click"],[3,"nzColor"],["nz-tooltip","","nzTooltipTitle","\u67e5\u770b\u4e8b\u4ef6\u8be6\u60c5",3,"click"],["nz-icon","","nzType","eye","nzTheme","outline"],["nz-typography","","nzType","secondary"],[4,"ngIf"],[4,"ngIf","ngIfElse"],["nz-tooltip","","nzTooltipTitle","\u91cd\u7f6e",1,"margin-right-12",3,"click"],["nz-icon","","nzType","rollback"]],template:function(s,i){if(1&s){const c=e.RV6();e.j41(0,"nz-row",12)(1,"nz-col",13)(2,"nz-card",14)(3,"div",15)(4,"ngx-charts-line-chart",16),e.bIt("select",function(C){return e.eBV(c),e.Njj(i.searchEventPoint(C))}),e.k0s()()(),e.DNE(5,le,2,2,"ng-template",null,0,e.C5r),e.j41(7,"nz-tabset",17)(8,"nz-tab",18)(9,"nz-input-group",19)(10,"input",20),e.mxI("ngModelChange",function(C){return e.eBV(c),e.DH7(i.searchKey,C)||(i.searchKey=C),e.Njj(C)}),e.bIt("keyup",function(C){return e.eBV(c),e.Njj(13===C.which?i.cipherCredentials():null)}),e.k0s(),e.DNE(11,ce,2,0,"ng-template",null,1,e.C5r),e.k0s(),e.j41(13,"nz-table",21,2),e.bIt("nzPageIndexChange",function(C){return e.eBV(c),e.Njj(i.cipherCredentials(C))})("nzPageSizeChange",function(C){return e.eBV(c),e.Njj(i.onPageSizeChange(C))}),e.j41(15,"thead")(16,"tr")(17,"th"),e.EFF(18,"AppId"),e.k0s(),e.j41(19,"th"),e.EFF(20,"Cluster"),e.k0s(),e.j41(21,"th"),e.EFF(22,"Namespace"),e.k0s(),e.j41(23,"th"),e.EFF(24,"\u7248\u672c\u53f7"),e.k0s(),e.j41(25,"th"),e.EFF(26,"\u66f4\u65b0\u65f6\u95f4"),e.k0s(),e.j41(27,"th"),e.EFF(28,"\u521b\u5efa\u65f6\u95f4"),e.k0s(),e.j41(29,"th",22),e.EFF(30,"\u64cd\u4f5c"),e.k0s()()(),e.j41(31,"tbody"),e.DNE(32,pe,26,12,"tr",23),e.k0s()(),e.DNE(33,ue,1,1,"ng-template",null,3,e.C5r),e.k0s()(),e.DNE(35,me,2,0,"ng-template",null,4,e.C5r),e.k0s(),e.j41(37,"nz-col",24)(38,"nz-card",25)(39,"nz-input-group",26)(40,"input",27),e.mxI("ngModelChange",function(C){return e.eBV(c),e.DH7(i.searchLogKey,C)||(i.searchLogKey=C),e.Njj(C)}),e.bIt("keyup",function(C){return e.eBV(c),e.Njj(13===C.which?i.cipherCredentialsLog():null)}),e.k0s()(),e.DNE(41,de,2,0,"ng-template",null,5,e.C5r),e.j41(43,"div",28)(44,"nz-timeline"),e.DNE(45,he,27,13,"nz-timeline-item",29),e.k0s()(),e.j41(46,"nz-row",30)(47,"nz-col")(48,"nz-pagination",31),e.bIt("nzPageIndexChange",function(C){return e.eBV(c),e.Njj(i.cipherCredentialsLog(C))}),e.k0s()()(),e.DNE(49,Ce,3,2,"ng-template",null,6,e.C5r)(51,Ee,1,1,"ng-template",null,7,e.C5r),e.k0s()()(),e.j41(53,"cipher-cipher-init",32,8),e.bIt("refreshEvent",function(){return e.eBV(c),e.Njj(i.ngOnInit())}),e.k0s(),e.nrm(55,"cipher-log-detail",null,9)(57,"cipher-signature-detail",null,10)}if(2&s){const c=e.sdS(6),D=e.sdS(12),C=e.sdS(14),$=e.sdS(34),Ye=e.sdS(36),We=e.sdS(42),Je=e.sdS(50),Xe=e.sdS(52);e.R7$(2),e.Y8G("nzBorderless",!0)("nzExtra",c),e.R7$(2),e.Y8G("results",i.cipherCredentialsLogMetricModel)("yAxis",!0)("xAxis",!0)("roundDomains",!0)("autoScale",!1)("legend",!0)("legendPosition",i.LegendPosition.Below),e.R7$(3),e.Y8G("nzTabBarExtraContent",Ye),e.R7$(2),e.Y8G("nzAddOnAfter",D),e.R7$(),e.R50("ngModel",i.searchKey),e.R7$(3),e.Y8G("nzData",i.cipherCredentialsModel.records)("nzShowTotal",$)("nzTotal",i.cipherCredentialsModel.total)("nzPageSize",i.cipherCredentialsModel.size)("nzPageIndex",i.cipherCredentialsModel.current)("nzFrontPagination",!1),e.R7$(19),e.Y8G("ngForOf",C.data),e.R7$(6),e.Y8G("nzBorderless",!0)("nzTitle",Je)("nzExtra",Xe),e.R7$(),e.Y8G("nzAddOnAfter",We),e.R7$(),e.R50("ngModel",i.searchLogKey),e.R7$(5),e.Y8G("ngForOf",i.cipherCredentialsLogModel.records),e.R7$(3),e.Y8G("nzPageIndex",i.cipherCredentialsLogModel.current)("nzPageSize",i.cipherCredentialsLogModel.size)("nzTotal",i.cipherCredentialsLogModel.total)}},dependencies:[r.Sq,r.bT,m.Uq,m.e,j.Il,M.cK,p.me,p.BC,p.vS,y.SN,y.vw,b.j,k.Dn,S.LH,_.CP,_.SO,_._4,_.IL,_.aj,_.kt,g.JZ,g.rE,u.aO,l.c,d.p,F.Sy,F.tg,B.Di,P.SO,P.XJ,L.ae,q,ee.L,ae,r.vh,oe.u,se.j]})}return a})(),Te=(()=>{class a{static#e=this.\u0275fac=function(s){return new(s||a)};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["app-cluster"]],decls:2,vars:0,template:function(s,i){1&s&&(e.j41(0,"p"),e.EFF(1,"cluster works!"),e.k0s())}})}return a})(),Me=(()=>{class a{static#e=this.\u0275fac=function(s){return new(s||a)};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["app-app"]],decls:2,vars:0,template:function(s,i){1&s&&(e.j41(0,"p"),e.EFF(1,"app works!"),e.k0s())}})}return a})();var Ie=o(23630),De=o(41261),x=o(38927),Re=o(55528),je=o(46728),ve=o(88058),K=o(72874),Y=o(75699);const Be=()=>({xxl:1,xl:1,lg:1,md:1,sm:1,xs:1});function ye(a,z){if(1&a){const t=e.RV6();e.j41(0,"button",33),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.doSearch())}),e.nrm(1,"span",34),e.k0s()}}function be(a,z){if(1&a&&(e.qex(0),e.j41(1,"nz-tag",15),e.EFF(2),e.k0s(),e.bVm()),2&a){const t=e.XpG().$implicit;e.R7$(),e.Y8G("nzColor",t.isDeleted?"error":""==t.oldValue?"success":"warning"),e.R7$(),e.SpI(" ",t.isDeleted?"\u5220\u9664":""==t.oldValue?"\u65b0\u589e":"\u4fee\u6539"," ")}}function ke(a,z){if(1&a){const t=e.RV6();e.qex(0),e.j41(1,"a",37),e.bIt("click",function(){e.eBV(t);const i=e.XpG().$implicit,c=e.XpG();return e.Njj(c.editItem(i.item))}),e.EFF(2,"\u7f16\u8f91"),e.k0s(),e.nrm(3,"nz-divider",20),e.j41(4,"a",38),e.bIt("nzOnConfirm",function(){e.eBV(t);const i=e.XpG().$implicit,c=e.XpG();return e.Njj(c.deleteItem(i.item.id))}),e.EFF(5,"\u5220\u9664"),e.k0s(),e.bVm()}}function Se(a,z){if(1&a&&(e.j41(0,"tr")(1,"td"),e.EFF(2),e.k0s(),e.j41(3,"td",35),e.EFF(4),e.DNE(5,be,3,2,"ng-container",36),e.k0s(),e.j41(6,"td",35),e.EFF(7),e.k0s(),e.j41(8,"td",35),e.EFF(9),e.k0s(),e.j41(10,"td"),e.EFF(11),e.k0s(),e.j41(12,"td"),e.EFF(13),e.nI1(14,"date"),e.k0s(),e.j41(15,"td"),e.DNE(16,ke,6,0,"ng-container",36),e.k0s()()),2&a){const t=z.$implicit;e.R7$(2),e.JRh(t.item.lineNum),e.R7$(2),e.SpI(" ",t.item.key," "),e.R7$(),e.Y8G("ngIf",t.isModified),e.R7$(2),e.JRh(t.item.value),e.R7$(2),e.JRh(t.item.comment),e.R7$(2),e.JRh(t.item.dataChangeLastModifiedBy),e.R7$(2),e.JRh(e.i5U(14,8,t.item.dataChangeLastModifiedTime,"yyy-MM-dd HH:mm:ss")),e.R7$(3),e.Y8G("ngIf",!t.isDeleted)}}function Oe(a,z){if(1&a){const t=e.RV6();e.j41(0,"a",39),e.bIt("click",function(){e.eBV(t);const i=e.XpG(),c=e.sdS(59);return e.Njj(c.open(i.appId,i.cluster,i.namespace))}),e.EFF(1,"\u53d1\u5e03\u8bb0\u5f55"),e.k0s(),e.nrm(2,"nz-divider",20),e.j41(3,"a",40),e.bIt("click",function(){e.eBV(t);const i=e.XpG(),c=e.sdS(57);return e.Njj(c.open(i.appId,i.cluster,i.namespace))}),e.EFF(4,"\u4fee\u6539\u8bb0\u5f55"),e.k0s()}}function Pe(a,z){if(1&a&&(e.qex(0),e.j41(1,"form",41)(2,"nz-form-item")(3,"nz-form-label",42),e.EFF(4,"Key"),e.k0s(),e.j41(5,"nz-form-control",43)(6,"nz-input-group"),e.nrm(7,"input",44),e.k0s()()(),e.j41(8,"nz-form-item")(9,"nz-form-label",45),e.EFF(10,"\u5185\u5bb9"),e.k0s(),e.j41(11,"nz-form-control",46)(12,"nz-textarea-count",47),e.nrm(13,"textarea",48),e.k0s()()(),e.j41(14,"nz-form-item")(15,"nz-form-label",49),e.EFF(16,"\u5907\u6ce8"),e.k0s(),e.j41(17,"nz-form-control",50)(18,"nz-input-group"),e.nrm(19,"input",51),e.k0s()()()(),e.bVm()),2&a){const t=e.XpG();e.R7$(),e.Y8G("formGroup",t.validateForm),e.R7$(11),e.Y8G("nzMaxCharacterCount",1048576)}}function Ve(a,z){1&a&&(e.qex(0),e.EFF(1," \u65b0\u589e\u914d\u7f6e "),e.bVm())}function Ge(a,z){1&a&&e.EFF(0," \u7f16\u8f91\u914d\u7f6e ")}function xe(a,z){if(1&a&&(e.DNE(0,Ve,2,0,"ng-container",52)(1,Ge,1,0,"ng-template",null,6,e.C5r),e.EFF(3)),2&a){const t=e.sdS(2),s=e.XpG();e.Y8G("ngIf",null==s.validateForm.value.id)("ngIfElse",t),e.R7$(3),e.SpI(" (",null==s.namespaceModel.baseInfo?null:s.namespaceModel.baseInfo.namespaceName,")\n")}}const $e=[{path:"",component:n,children:[{path:"cluster",component:Te},{path:"app",component:Me},{path:"cipher",component:fe},{path:"governance",component:(()=>{class a{constructor(t,s){this.formBuilder=t,this.configService=s,this.itemModel=[],this.namespaceModel={},this.itemStoreModel=[],this.modalVisible=!1,this.appId="global.governance.configurator",this.cluster="default",this.namespace="global.governance.configurator"}ngOnInit(){this.namespaceInfo(),this.initForm()}namespaceInfo(){this.configService.namespace(this.appId,this.cluster,this.namespace).subscribe(t=>{t.success&&((0,f.forEach)(t.data.items,s=>{s.name=s.item.key+s.item.value}),this.namespaceModel=t.data,this.itemModel=t.data.items,this.itemStoreModel=t.data.items)})}initForm(){this.validateForm=this.formBuilder.group({id:[null],namespaceId:[null],key:[null,[p.k0.required]],value:[null,[p.k0.required]],comment:[null]})}closeModal(){this.modalVisible=!1,this.validateForm.reset({id:null,namespaceId:null,key:null,value:null,comment:null})}submit(){if(this.validateForm.valid){if(this.validateForm.value.namespaceId=this.namespaceModel.baseInfo.id,Ie.h.isNotEmpty(this.validateForm.value.id))return void this.configService.putNamespaceItem(this.namespaceModel.baseInfo.appId,this.namespaceModel.baseInfo.clusterName,this.namespaceModel.baseInfo.namespaceName,this.validateForm.value).subscribe(t=>{t.success&&(this.closeModal(),this.namespaceInfo())});this.configService.postNamespaceItem(this.namespaceModel.baseInfo.appId,this.namespaceModel.baseInfo.clusterName,this.namespaceModel.baseInfo.namespaceName,this.validateForm.value).subscribe(t=>{t.success&&(this.closeModal(),this.namespaceInfo())})}else(0,De.wh)(this.validateForm)}doSearch(){this.itemModel=A.G.search(this.searchKey,this.itemStoreModel)}editItem(t){this.validateForm.patchValue(t,{onlySelf:!0,emitEvent:!0}),this.openModal()}openModal(){this.modalVisible=!0}namespaceRelease(){this.configService.namespaceRelease(this.namespaceModel.baseInfo.appId,this.namespaceModel.baseInfo.clusterName,this.namespaceModel.baseInfo.namespaceName).subscribe(t=>{t.success&&this.namespaceInfo()})}deleteItem(t){this.configService.deleteNamespaceItem(this.namespaceModel.baseInfo.appId,this.namespaceModel.baseInfo.clusterName,this.namespaceModel.baseInfo.namespaceName,t).subscribe(s=>{s.success&&this.namespaceInfo()})}static#e=this.\u0275fac=function(s){return new(s||a)(e.rXU(p.ze),e.rXU(N.w))};static#n=this.\u0275cmp=e.VBU({type:a,selectors:[["app-governance"]],decls:60,vars:21,consts:[["suffixButtonTemplate",""],["basicTable",""],["namespaceExtraTemplate",""],["modalTitleTemplate",""],["itemLogComponent",""],["namespaceReleaseComponent",""],["editTitleTemplate",""],["content","\u5168\u5c40\u670d\u52a1\u6cbb\u7406\u914d\u7f6e\u4fe1\u606f\uff0c\u6b64\u914d\u7f6e\u4fe1\u606f\u7684\u8c03\u6574\u5c06\u5f71\u54cd\u5230\u5168\u5c40\u670d\u52a1"],["nzGutter","12","nzJustify","center"],["nzSpan","22"],[1,"action-card",3,"nzTabBarExtraContent"],[3,"nzTitle"],["nzSpan","4"],[3,"nzColumn"],["nzTitle","\u72b6\u6001"],[3,"nzColor"],["nzTitle","\u7c7b\u578b"],["nzTitle","\u63cf\u8ff0"],["nzTitle","\u64cd\u4f5c"],["nz-tooltip","","nzTooltipTitle","\u53d1\u5e03\u5f53\u524d\u5df2\u4fee\u6539\u72b6\u6001\u7684\u914d\u7f6e","nz-popconfirm","","nzPopconfirmTitle","\u786e\u8ba4\u53d1\u5e03\u914d\u7f6e?","nzPopconfirmPlacement","top",3,"nzOnConfirm"],["nzType","vertical"],["nz-tooltip","","nzTooltipTitle","\u65b0\u589e\u914d\u7f6e\u4fe1\u606f",3,"click"],["nzTitle","\u65f6\u95f4"],["nzSpan","20"],["nzSearch","",3,"nzAddOnAfter"],["type","text","placeholder","\u8bf7\u8f93\u5165\u6240\u9700\u68c0\u7d22\u7684\u914d\u7f6eKEY\u6216\u8005VALUE","nz-input","","autofocus","",3,"ngModelChange","keyup","ngModel"],["nzShowSizeChanger","",3,"nzBordered","nzData","nzPageSize"],["nzWidth","70px"],["nzWidth","20%"],["nzWidth","108px"],[4,"ngFor","ngForOf"],["nzWidth","40%","nzMaskClosable","false",3,"nzVisibleChange","nzOnCancel","nzOnOk","nzVisible","nzTitle"],[4,"nzModalContent"],["nz-button","","nzType","primary","nzSearch","",3,"click"],["nz-icon","","nzType","search"],["nzBreakWord",""],[4,"ngIf"],[3,"click"],["nz-popconfirm","","nzPopconfirmTitle","\u786e\u8ba4\u5220\u9664\u6b64\u914d\u7f6e\u9879?","nzPopconfirmPlacement","top",3,"nzOnConfirm"],["nz-tooltip","","nzTooltipTitle","namespace\u7684\u53d1\u5e03\u8bb0\u5f55",3,"click"],["nz-tooltip","","nzTooltipTitle","\u914d\u7f6e\u9879\u4fee\u6539\u8bb0\u5f55",3,"click"],["nz-form","",3,"formGroup"],["nzFlex","60px","nzRequired","","nzFor","key"],["nzFlex","auto","nzErrorTip","\u8bf7\u8f93\u5165\u914d\u7f6eKey"],["nz-input","","formControlName","key","placeholder","\u914d\u7f6eKey"],["nzFlex","60px","nzRequired","","nzFor","value"],["nzFlex","auto","nzErrorTip","\u8bf7\u8f93\u5165Value"],[3,"nzMaxCharacterCount"],["rows","10","formControlName","value","nz-input",""],["nzFlex","60px","nzFor","comment"],["nzFlex","auto","nzErrorTip","\u8bf7\u8f93\u5165\u914d\u7f6e\u914d\u7f6e\u4fe1\u606f"],["nz-input","","formControlName","comment","placeholder","\u5907\u6ce8"],[4,"ngIf","ngIfElse"]],template:function(s,i){if(1&s){const c=e.RV6();e.nrm(0,"app-block-quote",7),e.j41(1,"nz-row",8)(2,"nz-col",9)(3,"nz-tabset",10)(4,"nz-tab",11)(5,"nz-row")(6,"nz-col",12)(7,"nz-descriptions",13)(8,"nz-descriptions-item",14)(9,"nz-tag",15),e.EFF(10),e.k0s()(),e.j41(11,"nz-descriptions-item",16)(12,"nz-tag",15),e.EFF(13),e.k0s()(),e.j41(14,"nz-descriptions-item",17),e.EFF(15),e.k0s(),e.j41(16,"nz-descriptions-item",18)(17,"a",19),e.bIt("nzOnConfirm",function(){return e.eBV(c),e.Njj(i.namespaceRelease())}),e.EFF(18," \u53d1\u5e03\u914d\u7f6e "),e.k0s(),e.nrm(19,"nz-divider",20),e.j41(20,"a",21),e.bIt("click",function(){return e.eBV(c),e.Njj(i.openModal())}),e.EFF(21," \u65b0\u589e\u914d\u7f6e "),e.k0s()(),e.j41(22,"nz-descriptions-item",22),e.EFF(23),e.nI1(24,"date"),e.k0s()()(),e.j41(25,"nz-col",23)(26,"nz-input-group",24)(27,"input",25),e.mxI("ngModelChange",function(C){return e.eBV(c),e.DH7(i.searchKey,C)||(i.searchKey=C),e.Njj(C)}),e.bIt("keyup",function(){return e.eBV(c),e.Njj(i.doSearch())}),e.k0s()(),e.DNE(28,ye,2,0,"ng-template",null,0,e.C5r),e.j41(30,"nz-table",26,1)(32,"thead")(33,"tr")(34,"th",27),e.EFF(35,"\u884c\u53f7"),e.k0s(),e.j41(36,"th",28),e.EFF(37,"Key"),e.k0s(),e.j41(38,"th"),e.EFF(39,"Value"),e.k0s(),e.j41(40,"th"),e.EFF(41,"\u5907\u6ce8"),e.k0s(),e.j41(42,"th"),e.EFF(43,"\u6700\u540e\u4fee\u6539\u8005"),e.k0s(),e.j41(44,"th"),e.EFF(45,"\u6700\u540e\u4fee\u6539\u65f6\u95f4"),e.k0s(),e.j41(46,"th",29),e.EFF(47,"\u64cd\u4f5c"),e.k0s()()(),e.j41(48,"tbody"),e.DNE(49,Se,17,11,"tr",30),e.k0s()()()()()(),e.DNE(50,Oe,5,0,"ng-template",null,2,e.C5r),e.k0s()(),e.j41(52,"nz-modal",31),e.mxI("nzVisibleChange",function(C){return e.eBV(c),e.DH7(i.modalVisible,C)||(i.modalVisible=C),e.Njj(C)}),e.bIt("nzOnCancel",function(){return e.eBV(c),e.Njj(i.closeModal())})("nzOnOk",function(){return e.eBV(c),e.Njj(i.submit())}),e.DNE(53,Pe,20,2,"ng-container",32),e.k0s(),e.DNE(54,xe,4,3,"ng-template",null,3,e.C5r),e.nrm(56,"component-item-log",null,4)(58,"component-namespace-release",null,5)}if(2&s){const c=e.sdS(29),D=e.sdS(31),C=e.sdS(51),$=e.sdS(55);e.R7$(3),e.Y8G("nzTabBarExtraContent",C),e.R7$(),e.Y8G("nzTitle",null==i.namespaceModel.baseInfo?null:i.namespaceModel.baseInfo.namespaceName),e.R7$(3),e.Y8G("nzColumn",e.lJ4(20,Be)),e.R7$(2),e.Y8G("nzColor",i.namespaceModel.itemModifiedCnt>0?"warning":"default"),e.R7$(),e.SpI(" ",i.namespaceModel.itemModifiedCnt>0?"\u6709\u4fee\u6539:"+i.namespaceModel.itemModifiedCnt:"\u5df2\u53d1\u5e03"," "),e.R7$(2),e.Y8G("nzColor",i.namespaceModel.public?"success":i.namespaceModel.parentAppId==(null==i.namespaceModel.baseInfo?null:i.namespaceModel.baseInfo.appId)?"purple":"warning"),e.R7$(),e.SpI(" ",i.namespaceModel.public?"\u516c\u5171":i.namespaceModel.parentAppId==(null==i.namespaceModel.baseInfo?null:i.namespaceModel.baseInfo.appId)?"\u79c1\u6709":"\u5173\u8054"," "),e.R7$(2),e.SpI(" ",i.namespaceModel.comment," "),e.R7$(8),e.SpI(" ",e.i5U(24,17,null==i.namespaceModel.baseInfo?null:i.namespaceModel.baseInfo.dataChangeCreatedTime,"yyy-MM-dd HH:mm:ss")," "),e.R7$(3),e.Y8G("nzAddOnAfter",c),e.R7$(),e.R50("ngModel",i.searchKey),e.R7$(3),e.Y8G("nzBordered",!0)("nzData",i.itemModel)("nzPageSize",20),e.R7$(19),e.Y8G("ngForOf",D.data),e.R7$(3),e.R50("nzVisible",i.modalVisible),e.Y8G("nzTitle",$)}},dependencies:[r.Sq,r.bT,m.Uq,m.e,x.CA,x.Ls,x.Mo,x.zS,p.qT,p.me,p.BC,p.cb,p.vS,b.j,k.Dn,S.LH,_.CP,_.SO,_._4,_.IL,_.aj,_.kt,_.BC,g.JZ,g.rE,u.aO,l.c,d.p,F.F8,F.Sy,F.tg,V.F2,V.A9,G.xA,G.gr,Re.J,je.q,ve.n,K.PV,Y.s,p.j4,p.JD,r.vh]})}return a})()},{path:"",redirectTo:"cipher",pathMatch:"full"}]}];let Le=(()=>{class a{static#e=this.\u0275fac=function(s){return new(s||a)};static#n=this.\u0275mod=e.$C({type:a});static#t=this.\u0275inj=e.G2t({imports:[E.iI.forChild($e),E.iI]})}return a})();var Ae=o(1997),Ne=o(81665),Ue=o(45079);let Ke=(()=>{class a{static#e=this.\u0275fac=function(s){return new(s||a)};static#n=this.\u0275mod=e.$C({type:a});static#t=this.\u0275inj=e.G2t({imports:[r.MD,Le,x.PQ,j.JK,M.Ti,p.YN,y.LE,b.g,k.Y3,S.Qt,_.$G,g.hM,u.Zw,j.dV,F.j,d.o7,Ae.DH,V.U6,B.kT,P.ki,L.Hf,Ne.L,U.s,G.WF,Ue.Q,K.g9,Y.T,p.X1]})}return a})()},96309:(O,R,o)=>{o.d(R,{j:()=>e});var r=o(41261),E=o(54438);let e=(()=>{class n{constructor(){}transform(I){return(0,r.hs)(I)}static#e=this.\u0275fac=function(v){return new(v||n)};static#n=this.\u0275pipe=E.EJ8({name:"actionType",type:n,pure:!0});static#t=this.\u0275prov=E.jDH({token:n,factory:n.\u0275fac})}return n})()},25784:(O,R,o)=>{o.d(R,{u:()=>j});var r=o(23630),E=o(67376),n=o(54438);let j=(()=>{class I{constructor(){}transform(f,T){return r.h.isEmpty(f)?"":f.length<T?f:(0,E.truncate)(f,{length:T})}static#e=this.\u0275fac=function(T){return new(T||I)};static#n=this.\u0275pipe=n.EJ8({name:"ellipsis",type:I,pure:!0});static#t=this.\u0275prov=n.jDH({token:I,factory:I.\u0275fac})}return I})()},67562:(O,R,o)=>{o.d(R,{p:()=>v});var r=o(88141),E=o(67376),n=o(41261),j=o(54438),I=o(21626);let v=(()=>{class f{constructor(h){this.httpClient=h}cipherCredentialsLogMetrics(h,m){return this.httpClient.get("cipher/credentials/logMetrics",{params:{startTime:h,endTime:m}}).pipe((0,r.M)(M=>{M.success&&((0,E.forEach)(M.data,p=>{p.extra=p.name,p.name=(0,n.hs)(p.name)}),M.data.push({name:"\u6240\u6709\u7c7b\u578b",series:[]}))}))}cipherCredentials(h,m,M){return this.httpClient.get("cipher/credentials",{params:{appId:h,num:m,size:M}})}cipherNamespace(h,m){return this.httpClient.get("cipher/namespace",{params:{appId:h,cluster:m}})}cipherInit(h,m){return this.httpClient.post("cipher/init",null,{params:{appId:h,cluster:m}})}cipherCredentialsLog(h,m,M,p){return this.httpClient.get("cipher/credentials/log",{params:{appId:h,ids:m,num:M,size:p}})}deleteCipher(h){return this.httpClient.delete("cipher/credentials",{params:{id:h}})}cipherSignature(h){return this.httpClient.get("cipher/signature",{params:{id:h}})}getNamespaceItem(h,m,M,p){return this.httpClient.get("cipher/namespace/showItem",{params:{appId:h,cluster:m,namespace:M,id:p}})}postNamespaceItem(h,m,M,p){return this.httpClient.post("cipher/namespace/addItem",p,{params:{appId:h,cluster:m,namespace:M}})}putNamespaceItem(h,m,M,p){return this.httpClient.put("cipher/namespace/modifyItem",p,{params:{appId:h,cluster:m,namespace:M}})}putNamespaceText(h,m,M,p){return this.httpClient.put("cipher/namespace/text",p,{params:{appId:h,cluster:m,namespace:M}})}deleteNamespaceItem(h,m,M,p){return this.httpClient.delete("cipher/namespace/deleteItem",{params:{appId:h,cluster:m,namespace:M,id:p}})}static#e=this.\u0275fac=function(m){return new(m||f)(j.KVO(I.Qq))};static#n=this.\u0275prov=j.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}return f})()}}]);