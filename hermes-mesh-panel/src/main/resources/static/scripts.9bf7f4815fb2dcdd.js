!function(T,I){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=T.document?I(T,!0):function(L){if(!L.document)throw new Error("jQuery requires a window with a document");return I(L)}:I(T)}(typeof window<"u"?window:this,function(T,I){"use strict";var L=[],ae=Object.getPrototypeOf,E=L.slice,te=L.flat?function(e){return L.flat.call(e)}:function(e){return L.concat.apply([],e)},xe=L.push,oe=L.indexOf,de={},Ce=de.toString,he=de.hasOwnProperty,_e=he.toString,ne=_e.call(Object),F={},N=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},b=function(t){return null!=t&&t===t.window},p=T.document,S={type:!0,src:!0,nonce:!0,noModule:!0};function k(e,t,n){var i,a,s=(n=n||p).createElement("script");if(s.text=e,t)for(i in S)(a=t[i]||t.getAttribute&&t.getAttribute(i))&&s.setAttribute(i,a);n.head.appendChild(s).parentNode.removeChild(s)}function A(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?de[Ce.call(e)]||"object":typeof e}var H="3.7.1",z=/HTML$/i,r=function(e,t){return new r.fn.init(e,t)};function B(e){var t=!!e&&"length"in e&&e.length,n=A(e);return!N(e)&&!b(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function M(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}r.fn=r.prototype={jquery:H,constructor:r,length:0,toArray:function(){return E.call(this)},get:function(e){return null==e?E.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=r.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return r.each(this,e)},map:function(e){return this.pushStack(r.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(E.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(r.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(r.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:xe,sort:L.sort,splice:L.splice},r.extend=r.fn.extend=function(){var e,t,n,i,a,s,o=arguments[0]||{},l=1,f=arguments.length,d=!1;for("boolean"==typeof o&&(d=o,o=arguments[l]||{},l++),"object"!=typeof o&&!N(o)&&(o={}),l===f&&(o=this,l--);l<f;l++)if(null!=(e=arguments[l]))for(t in e)i=e[t],"__proto__"!==t&&o!==i&&(d&&i&&(r.isPlainObject(i)||(a=Array.isArray(i)))?(n=o[t],s=a&&!Array.isArray(n)?[]:a||r.isPlainObject(n)?n:{},a=!1,o[t]=r.extend(d,s,i)):void 0!==i&&(o[t]=i));return o},r.extend({expando:"jQuery"+(H+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==Ce.call(e)||(t=ae(e))&&("function"!=typeof(n=he.call(t,"constructor")&&t.constructor)||_e.call(n)!==ne))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){k(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(B(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},text:function(e){var t,n="",i=0,a=e.nodeType;if(!a)for(;t=e[i++];)n+=r.text(t);return 1===a||11===a?e.textContent:9===a?e.documentElement.textContent:3===a||4===a?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(B(Object(e))?r.merge(n,"string"==typeof e?[e]:e):xe.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:oe.call(t,e,n)},isXMLDoc:function(e){var n=e&&(e.ownerDocument||e).documentElement;return!z.test(e&&e.namespaceURI||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,i=0,a=e.length;i<n;i++)e[a++]=t[i];return e.length=a,e},grep:function(e,t,n){for(var a=[],s=0,o=e.length,l=!n;s<o;s++)!t(e[s],s)!==l&&a.push(e[s]);return a},map:function(e,t,n){var i,a,s=0,o=[];if(B(e))for(i=e.length;s<i;s++)null!=(a=t(e[s],s,n))&&o.push(a);else for(s in e)null!=(a=t(e[s],s,n))&&o.push(a);return te(o)},guid:1,support:F}),"function"==typeof Symbol&&(r.fn[Symbol.iterator]=L[Symbol.iterator]),r.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){de["[object "+t+"]"]=t.toLowerCase()});var we=L.pop,Ve=L.sort,Ge=L.splice,Q="[\\x20\\t\\r\\n\\f]",Ne=new RegExp("^"+Q+"+|((?:^|[^\\\\])(?:\\\\.)*)"+Q+"+$","g");r.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var nt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function re(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}r.escapeSelector=function(e){return(e+"").replace(nt,re)};var ee=p,Te=xe;!function(){var e,t,n,i,a,o,l,f,d,v,s=Te,x=r.expando,g=0,w=0,_=Pt(),G=Pt(),W=Pt(),le=Pt(),se=function(u,c){return u===c&&(a=!0),0},qe="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Pe="(?:\\\\[\\da-fA-F]{1,6}"+Q+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",V="\\["+Q+"*("+Pe+")(?:"+Q+"*([*^$|!~]?=)"+Q+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+Pe+"))|)"+Q+"*\\]",et=":("+Pe+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+V+")*)|.*)\\)|)",Y=new RegExp(Q+"+","g"),ie=new RegExp("^"+Q+"*,"+Q+"*"),St=new RegExp("^"+Q+"*([>+~]|"+Q+")"+Q+"*"),Kt=new RegExp(Q+"|>"),He=new RegExp(et),Ct=new RegExp("^"+Pe+"$"),Oe={ID:new RegExp("^#("+Pe+")"),CLASS:new RegExp("^\\.("+Pe+")"),TAG:new RegExp("^("+Pe+"|[*])"),ATTR:new RegExp("^"+V),PSEUDO:new RegExp("^"+et),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+Q+"*(even|odd|(([+-]|)(\\d*)n|)"+Q+"*(?:([+-]|)"+Q+"*(\\d+)|))"+Q+"*\\)|)","i"),bool:new RegExp("^(?:"+qe+")$","i"),needsContext:new RegExp("^"+Q+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+Q+"*((?:-\\d)?\\d*)"+Q+"*\\)|)(?=[^-]|$)","i")},ze=/^(?:input|select|textarea|button)$/i,Ue=/^h\d$/i,Ee=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,en=/[+~]/,Ie=new RegExp("\\\\[\\da-fA-F]{1,6}"+Q+"?|\\\\([^\\r\\n\\f])","g"),We=function(u,c){var h="0x"+u.slice(1)-65536;return c||(h<0?String.fromCharCode(h+65536):String.fromCharCode(h>>10|55296,1023&h|56320))},Nr=function(){Xe()},Lr=Ot(function(u){return!0===u.disabled&&M(u,"fieldset")},{dir:"parentNode",next:"legend"});try{s.apply(L=E.call(ee.childNodes),ee.childNodes)}catch{s={apply:function(c,h){Te.apply(c,E.call(h))},call:function(c){Te.apply(c,E.call(arguments,1))}}}function Z(u,c,h,y){var m,C,D,q,j,U,$,R=c&&c.ownerDocument,X=c?c.nodeType:9;if(h=h||[],"string"!=typeof u||!u||1!==X&&9!==X&&11!==X)return h;if(!y&&(Xe(c),c=c||o,f)){if(11!==X&&(j=Ee.exec(u)))if(m=j[1]){if(9===X){if(!(D=c.getElementById(m)))return h;if(D.id===m)return s.call(h,D),h}else if(R&&(D=R.getElementById(m))&&Z.contains(c,D)&&D.id===m)return s.call(h,D),h}else{if(j[2])return s.apply(h,c.getElementsByTagName(u)),h;if((m=j[3])&&c.getElementsByClassName)return s.apply(h,c.getElementsByClassName(m)),h}if(!(le[u+" "]||d&&d.test(u))){if($=u,R=c,1===X&&(Kt.test(u)||St.test(u))){for(((R=en.test(u)&&tn(c.parentNode)||c)!=c||!F.scope)&&((q=c.getAttribute("id"))?q=r.escapeSelector(q):c.setAttribute("id",q=x)),C=(U=At(u)).length;C--;)U[C]=(q?"#"+q:":scope")+" "+Ht(U[C]);$=U.join(",")}try{return s.apply(h,R.querySelectorAll($)),h}catch{le(u,!0)}finally{q===x&&c.removeAttribute("id")}}}return In(u.replace(Ne,"$1"),c,h,y)}function Pt(){var u=[];return function c(h,y){return u.push(h+" ")>t.cacheLength&&delete c[u.shift()],c[h+" "]=y}}function je(u){return u[x]=!0,u}function lt(u){var c=o.createElement("fieldset");try{return!!u(c)}catch{return!1}finally{c.parentNode&&c.parentNode.removeChild(c),c=null}}function Pr(u){return function(c){return M(c,"input")&&c.type===u}}function Hr(u){return function(c){return(M(c,"input")||M(c,"button"))&&c.type===u}}function Mn(u){return function(c){return"form"in c?c.parentNode&&!1===c.disabled?"label"in c?"label"in c.parentNode?c.parentNode.disabled===u:c.disabled===u:c.isDisabled===u||c.isDisabled!==!u&&Lr(c)===u:c.disabled===u:"label"in c&&c.disabled===u}}function tt(u){return je(function(c){return c=+c,je(function(h,y){for(var m,C=u([],h.length,c),D=C.length;D--;)h[m=C[D]]&&(h[m]=!(y[m]=h[m]))})})}function tn(u){return u&&typeof u.getElementsByTagName<"u"&&u}function Xe(u){var c,h=u?u.ownerDocument||u:ee;return h==o||9!==h.nodeType||!h.documentElement||(l=(o=h).documentElement,f=!r.isXMLDoc(o),v=l.matches||l.webkitMatchesSelector||l.msMatchesSelector,l.msMatchesSelector&&ee!=o&&(c=o.defaultView)&&c.top!==c&&c.addEventListener("unload",Nr),F.getById=lt(function(y){return l.appendChild(y).id=r.expando,!o.getElementsByName||!o.getElementsByName(r.expando).length}),F.disconnectedMatch=lt(function(y){return v.call(y,"*")}),F.scope=lt(function(){return o.querySelectorAll(":scope")}),F.cssHas=lt(function(){try{return o.querySelector(":has(*,:jqfake)"),!1}catch{return!0}}),F.getById?(t.filter.ID=function(y){var m=y.replace(Ie,We);return function(C){return C.getAttribute("id")===m}},t.find.ID=function(y,m){if(typeof m.getElementById<"u"&&f){var C=m.getElementById(y);return C?[C]:[]}}):(t.filter.ID=function(y){var m=y.replace(Ie,We);return function(C){var D=typeof C.getAttributeNode<"u"&&C.getAttributeNode("id");return D&&D.value===m}},t.find.ID=function(y,m){if(typeof m.getElementById<"u"&&f){var C,D,q,j=m.getElementById(y);if(j){if((C=j.getAttributeNode("id"))&&C.value===y)return[j];for(q=m.getElementsByName(y),D=0;j=q[D++];)if((C=j.getAttributeNode("id"))&&C.value===y)return[j]}return[]}}),t.find.TAG=function(y,m){return typeof m.getElementsByTagName<"u"?m.getElementsByTagName(y):m.querySelectorAll(y)},t.find.CLASS=function(y,m){if(typeof m.getElementsByClassName<"u"&&f)return m.getElementsByClassName(y)},d=[],lt(function(y){var m;l.appendChild(y).innerHTML="<a id='"+x+"' href='' disabled='disabled'></a><select id='"+x+"-\r\\' disabled='disabled'><option selected=''></option></select>",y.querySelectorAll("[selected]").length||d.push("\\["+Q+"*(?:value|"+qe+")"),y.querySelectorAll("[id~="+x+"-]").length||d.push("~="),y.querySelectorAll("a#"+x+"+*").length||d.push(".#.+[+~]"),y.querySelectorAll(":checked").length||d.push(":checked"),(m=o.createElement("input")).setAttribute("type","hidden"),y.appendChild(m).setAttribute("name","D"),l.appendChild(y).disabled=!0,2!==y.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(m=o.createElement("input")).setAttribute("name",""),y.appendChild(m),y.querySelectorAll("[name='']").length||d.push("\\["+Q+"*name"+Q+"*="+Q+"*(?:''|\"\")")}),F.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),se=function(y,m){if(y===m)return a=!0,0;var C=!y.compareDocumentPosition-!m.compareDocumentPosition;return C||(1&(C=(y.ownerDocument||y)==(m.ownerDocument||m)?y.compareDocumentPosition(m):1)||!F.sortDetached&&m.compareDocumentPosition(y)===C?y===o||y.ownerDocument==ee&&Z.contains(ee,y)?-1:m===o||m.ownerDocument==ee&&Z.contains(ee,m)?1:i?oe.call(i,y)-oe.call(i,m):0:4&C?-1:1)}),o}for(e in Z.matches=function(u,c){return Z(u,null,null,c)},Z.matchesSelector=function(u,c){if(Xe(u),f&&!le[c+" "]&&(!d||!d.test(c)))try{var h=v.call(u,c);if(h||F.disconnectedMatch||u.document&&11!==u.document.nodeType)return h}catch{le(c,!0)}return Z(c,o,null,[u]).length>0},Z.contains=function(u,c){return(u.ownerDocument||u)!=o&&Xe(u),r.contains(u,c)},Z.attr=function(u,c){(u.ownerDocument||u)!=o&&Xe(u);var h=t.attrHandle[c.toLowerCase()],y=h&&he.call(t.attrHandle,c.toLowerCase())?h(u,c,!f):void 0;return void 0!==y?y:u.getAttribute(c)},Z.error=function(u){throw new Error("Syntax error, unrecognized expression: "+u)},r.uniqueSort=function(u){var c,h=[],y=0,m=0;if(a=!F.sortStable,i=!F.sortStable&&E.call(u,0),Ve.call(u,se),a){for(;c=u[m++];)c===u[m]&&(y=h.push(m));for(;y--;)Ge.call(u,h[y],1)}return i=null,u},r.fn.uniqueSort=function(){return this.pushStack(r.uniqueSort(E.apply(this)))},(t=r.expr={cacheLength:50,createPseudo:je,match:Oe,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(u){return u[1]=u[1].replace(Ie,We),u[3]=(u[3]||u[4]||u[5]||"").replace(Ie,We),"~="===u[2]&&(u[3]=" "+u[3]+" "),u.slice(0,4)},CHILD:function(u){return u[1]=u[1].toLowerCase(),"nth"===u[1].slice(0,3)?(u[3]||Z.error(u[0]),u[4]=+(u[4]?u[5]+(u[6]||1):2*("even"===u[3]||"odd"===u[3])),u[5]=+(u[7]+u[8]||"odd"===u[3])):u[3]&&Z.error(u[0]),u},PSEUDO:function(u){var c,h=!u[6]&&u[2];return Oe.CHILD.test(u[0])?null:(u[3]?u[2]=u[4]||u[5]||"":h&&He.test(h)&&(c=At(h,!0))&&(c=h.indexOf(")",h.length-c)-h.length)&&(u[0]=u[0].slice(0,c),u[2]=h.slice(0,c)),u.slice(0,3))}},filter:{TAG:function(u){var c=u.replace(Ie,We).toLowerCase();return"*"===u?function(){return!0}:function(h){return M(h,c)}},CLASS:function(u){var c=_[u+" "];return c||(c=new RegExp("(^|"+Q+")"+u+"("+Q+"|$)"))&&_(u,function(h){return c.test("string"==typeof h.className&&h.className||typeof h.getAttribute<"u"&&h.getAttribute("class")||"")})},ATTR:function(u,c,h){return function(y){var m=Z.attr(y,u);return null==m?"!="===c:!c||(m+="","="===c?m===h:"!="===c?m!==h:"^="===c?h&&0===m.indexOf(h):"*="===c?h&&m.indexOf(h)>-1:"$="===c?h&&m.slice(-h.length)===h:"~="===c?(" "+m.replace(Y," ")+" ").indexOf(h)>-1:"|="===c&&(m===h||m.slice(0,h.length+1)===h+"-"))}},CHILD:function(u,c,h,y,m){var C="nth"!==u.slice(0,3),D="last"!==u.slice(-4),q="of-type"===c;return 1===y&&0===m?function(j){return!!j.parentNode}:function(j,U,$){var R,X,O,J,me,ce=C!==D?"nextSibling":"previousSibling",Fe=j.parentNode,$e=q&&j.nodeName.toLowerCase(),ct=!$&&!q,pe=!1;if(Fe){if(C){for(;ce;){for(O=j;O=O[ce];)if(q?M(O,$e):1===O.nodeType)return!1;me=ce="only"===u&&!me&&"nextSibling"}return!0}if(me=[D?Fe.firstChild:Fe.lastChild],D&&ct){for(pe=(J=(R=(X=Fe[x]||(Fe[x]={}))[u]||[])[0]===g&&R[1])&&R[2],O=J&&Fe.childNodes[J];O=++J&&O&&O[ce]||(pe=J=0)||me.pop();)if(1===O.nodeType&&++pe&&O===j){X[u]=[g,J,pe];break}}else if(ct&&(pe=J=(R=(X=j[x]||(j[x]={}))[u]||[])[0]===g&&R[1]),!1===pe)for(;(O=++J&&O&&O[ce]||(pe=J=0)||me.pop())&&(!(q?M(O,$e):1===O.nodeType)||!++pe||(ct&&((X=O[x]||(O[x]={}))[u]=[g,pe]),O!==j)););return(pe-=m)===y||pe%y==0&&pe/y>=0}}},PSEUDO:function(u,c){var h,y=t.pseudos[u]||t.setFilters[u.toLowerCase()]||Z.error("unsupported pseudo: "+u);return y[x]?y(c):y.length>1?(h=[u,u,"",c],t.setFilters.hasOwnProperty(u.toLowerCase())?je(function(m,C){for(var D,q=y(m,c),j=q.length;j--;)m[D=oe.call(m,q[j])]=!(C[D]=q[j])}):function(m){return y(m,0,h)}):y}},pseudos:{not:je(function(u){var c=[],h=[],y=sn(u.replace(Ne,"$1"));return y[x]?je(function(m,C,D,q){for(var j,U=y(m,null,q,[]),$=m.length;$--;)(j=U[$])&&(m[$]=!(C[$]=j))}):function(m,C,D){return c[0]=m,y(c,null,D,h),c[0]=null,!h.pop()}}),has:je(function(u){return function(c){return Z(u,c).length>0}}),contains:je(function(u){return u=u.replace(Ie,We),function(c){return(c.textContent||r.text(c)).indexOf(u)>-1}}),lang:je(function(u){return Ct.test(u||"")||Z.error("unsupported lang: "+u),u=u.replace(Ie,We).toLowerCase(),function(c){var h;do{if(h=f?c.lang:c.getAttribute("xml:lang")||c.getAttribute("lang"))return(h=h.toLowerCase())===u||0===h.indexOf(u+"-")}while((c=c.parentNode)&&1===c.nodeType);return!1}}),target:function(u){var c=T.location&&T.location.hash;return c&&c.slice(1)===u.id},root:function(u){return u===l},focus:function(u){return u===function qr(){try{return o.activeElement}catch{}}()&&o.hasFocus()&&!!(u.type||u.href||~u.tabIndex)},enabled:Mn(!1),disabled:Mn(!0),checked:function(u){return M(u,"input")&&!!u.checked||M(u,"option")&&!!u.selected},selected:function(u){return!0===u.selected},empty:function(u){for(u=u.firstChild;u;u=u.nextSibling)if(u.nodeType<6)return!1;return!0},parent:function(u){return!t.pseudos.empty(u)},header:function(u){return Ue.test(u.nodeName)},input:function(u){return ze.test(u.nodeName)},button:function(u){return M(u,"input")&&"button"===u.type||M(u,"button")},text:function(u){var c;return M(u,"input")&&"text"===u.type&&(null==(c=u.getAttribute("type"))||"text"===c.toLowerCase())},first:tt(function(){return[0]}),last:tt(function(u,c){return[c-1]}),eq:tt(function(u,c,h){return[h<0?h+c:h]}),even:tt(function(u,c){for(var h=0;h<c;h+=2)u.push(h);return u}),odd:tt(function(u,c){for(var h=1;h<c;h+=2)u.push(h);return u}),lt:tt(function(u,c,h){var y;for(y=h<0?h+c:h>c?c:h;--y>=0;)u.push(y);return u}),gt:tt(function(u,c,h){for(var y=h<0?h+c:h;++y<c;)u.push(y);return u})}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=Pr(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=Hr(e);function Rn(){}function At(u,c){var h,y,m,C,D,q,j,U=G[u+" "];if(U)return c?0:U.slice(0);for(D=u,q=[],j=t.preFilter;D;){for(C in(!h||(y=ie.exec(D)))&&(y&&(D=D.slice(y[0].length)||D),q.push(m=[])),h=!1,(y=St.exec(D))&&(h=y.shift(),m.push({value:h,type:y[0].replace(Ne," ")}),D=D.slice(h.length)),t.filter)(y=Oe[C].exec(D))&&(!j[C]||(y=j[C](y)))&&(h=y.shift(),m.push({value:h,type:C,matches:y}),D=D.slice(h.length));if(!h)break}return c?D.length:D?Z.error(u):G(u,q).slice(0)}function Ht(u){for(var c=0,h=u.length,y="";c<h;c++)y+=u[c].value;return y}function Ot(u,c,h){var y=c.dir,m=c.next,C=m||y,D=h&&"parentNode"===C,q=w++;return c.first?function(j,U,$){for(;j=j[y];)if(1===j.nodeType||D)return u(j,U,$);return!1}:function(j,U,$){var R,X,O=[g,q];if($){for(;j=j[y];)if((1===j.nodeType||D)&&u(j,U,$))return!0}else for(;j=j[y];)if(1===j.nodeType||D)if(X=j[x]||(j[x]={}),m&&M(j,m))j=j[y]||j;else{if((R=X[C])&&R[0]===g&&R[1]===q)return O[2]=R[2];if(X[C]=O,O[2]=u(j,U,$))return!0}return!1}}function nn(u){return u.length>1?function(c,h,y){for(var m=u.length;m--;)if(!u[m](c,h,y))return!1;return!0}:u[0]}function $t(u,c,h,y,m){for(var C,D=[],q=0,j=u.length,U=null!=c;q<j;q++)(C=u[q])&&(!h||h(C,y,m))&&(D.push(C),U&&c.push(q));return D}function rn(u,c,h,y,m,C){return y&&!y[x]&&(y=rn(y)),m&&!m[x]&&(m=rn(m,C)),je(function(D,q,j,U){var $,R,X,O,J=[],me=[],ce=q.length,Fe=D||function Or(u,c,h){for(var y=0,m=c.length;y<m;y++)Z(u,c[y],h);return h}(c||"*",j.nodeType?[j]:j,[]),$e=!u||!D&&c?Fe:$t(Fe,J,u,j,U);if(h?h($e,O=m||(D?u:ce||y)?[]:q,j,U):O=$e,y)for($=$t(O,me),y($,[],j,U),R=$.length;R--;)(X=$[R])&&(O[me[R]]=!($e[me[R]]=X));if(D){if(m||u){if(m){for($=[],R=O.length;R--;)(X=O[R])&&$.push($e[R]=X);m(null,O=[],$,U)}for(R=O.length;R--;)(X=O[R])&&($=m?oe.call(D,X):J[R])>-1&&(D[$]=!(q[$]=X))}}else O=$t(O===q?O.splice(ce,O.length):O),m?m(null,q,O,U):s.apply(q,O)})}function an(u){for(var c,h,y,m=u.length,C=t.relative[u[0].type],D=C||t.relative[" "],q=C?1:0,j=Ot(function(R){return R===c},D,!0),U=Ot(function(R){return oe.call(c,R)>-1},D,!0),$=[function(R,X,O){var J=!C&&(O||X!=n)||((c=X).nodeType?j(R,X,O):U(R,X,O));return c=null,J}];q<m;q++)if(h=t.relative[u[q].type])$=[Ot(nn($),h)];else{if((h=t.filter[u[q].type].apply(null,u[q].matches))[x]){for(y=++q;y<m&&!t.relative[u[y].type];y++);return rn(q>1&&nn($),q>1&&Ht(u.slice(0,q-1).concat({value:" "===u[q-2].type?"*":""})).replace(Ne,"$1"),h,q<y&&an(u.slice(q,y)),y<m&&an(u=u.slice(y)),y<m&&Ht(u))}$.push(h)}return nn($)}function sn(u,c){var h,y=[],m=[],C=W[u+" "];if(!C){for(c||(c=At(u)),h=c.length;h--;)(C=an(c[h]))[x]?y.push(C):m.push(C);C=W(u,function $r(u,c){var h=c.length>0,y=u.length>0,m=function(C,D,q,j,U){var $,R,X,O=0,J="0",me=C&&[],ce=[],Fe=n,$e=C||y&&t.find.TAG("*",U),ct=g+=null==Fe?1:Math.random()||.1,pe=$e.length;for(U&&(n=D==o||D||U);J!==pe&&null!=($=$e[J]);J++){if(y&&$){for(R=0,!D&&$.ownerDocument!=o&&(Xe($),q=!f);X=u[R++];)if(X($,D||o,q)){s.call(j,$);break}U&&(g=ct)}h&&(($=!X&&$)&&O--,C&&me.push($))}if(O+=J,h&&J!==O){for(R=0;X=c[R++];)X(me,ce,D,q);if(C){if(O>0)for(;J--;)me[J]||ce[J]||(ce[J]=we.call(j));ce=$t(ce)}s.apply(j,ce),U&&!C&&ce.length>0&&O+c.length>1&&r.uniqueSort(j)}return U&&(g=ct,n=Fe),me};return h?je(m):m}(m,y)),C.selector=u}return C}function In(u,c,h,y){var m,C,D,q,j,U="function"==typeof u&&u,$=!y&&At(u=U.selector||u);if(h=h||[],1===$.length){if((C=$[0]=$[0].slice(0)).length>2&&"ID"===(D=C[0]).type&&9===c.nodeType&&f&&t.relative[C[1].type]){if(!(c=(t.find.ID(D.matches[0].replace(Ie,We),c)||[])[0]))return h;U&&(c=c.parentNode),u=u.slice(C.shift().value.length)}for(m=Oe.needsContext.test(u)?0:C.length;m--&&!t.relative[q=(D=C[m]).type];)if((j=t.find[q])&&(y=j(D.matches[0].replace(Ie,We),en.test(C[0].type)&&tn(c.parentNode)||c))){if(C.splice(m,1),!(u=y.length&&Ht(C)))return s.apply(h,y),h;break}}return(U||sn(u,$))(y,c,!f,h,!c||en.test(u)&&tn(c.parentNode)||c),h}Rn.prototype=t.filters=t.pseudos,t.setFilters=new Rn,F.sortStable=x.split("").sort(se).join("")===x,Xe(),F.sortDetached=lt(function(u){return 1&u.compareDocumentPosition(o.createElement("fieldset"))}),r.find=Z,r.expr[":"]=r.expr.pseudos,r.unique=r.uniqueSort,Z.compile=sn,Z.select=In,Z.setDocument=Xe,Z.tokenize=At,Z.escape=r.escapeSelector,Z.getText=r.text,Z.isXML=r.isXMLDoc,Z.selectors=r.expr,Z.support=r.support,Z.uniqueSort=r.uniqueSort}();var ge=function(e,t,n){for(var i=[],a=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&r(e).is(n))break;i.push(e)}return i},ue=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},Qe=r.expr.match.needsContext,K=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function fe(e,t,n){return N(t)?r.grep(e,function(i,a){return!!t.call(i,a,i)!==n}):t.nodeType?r.grep(e,function(i){return i===t!==n}):"string"!=typeof t?r.grep(e,function(i){return oe.call(t,i)>-1!==n}):r.filter(t,e,n)}r.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?r.find.matchesSelector(i,e)?[i]:[]:r.find.matches(e,r.grep(t,function(a){return 1===a.nodeType}))},r.fn.extend({find:function(e){var t,n,i=this.length,a=this;if("string"!=typeof e)return this.pushStack(r(e).filter(function(){for(t=0;t<i;t++)if(r.contains(a[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)r.find(e,a[t],n);return i>1?r.uniqueSort(n):n},filter:function(e){return this.pushStack(fe(this,e||[],!1))},not:function(e){return this.pushStack(fe(this,e||[],!0))},is:function(e){return!!fe(this,"string"==typeof e&&Qe.test(e)?r(e):e||[],!1).length}});var Se,Be=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,dt=r.fn.init=function(e,t,n){var i,a;if(!e)return this;if(n=n||Se,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:Be.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(r.merge(this,r.parseHTML(i[1],(t=t instanceof r?t[0]:t)&&t.nodeType?t.ownerDocument||t:p,!0)),K.test(i[1])&&r.isPlainObject(t))for(i in t)N(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(a=p.getElementById(i[2]))&&(this[0]=a,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):N(e)?void 0!==n.ready?n.ready(e):e(r):r.makeArray(e,this)};dt.prototype=r.fn,Se=r(p);var kt=/^(?:parents|prev(?:Until|All))/,pt={children:!0,contents:!0,next:!0,prev:!0};function Ye(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}r.fn.extend({has:function(e){var t=r(e,this),n=t.length;return this.filter(function(){for(var i=0;i<n;i++)if(r.contains(this,t[i]))return!0})},closest:function(e,t){var n,i=0,a=this.length,s=[],o="string"!=typeof e&&r(e);if(!Qe.test(e))for(;i<a;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(o?o.index(n)>-1:1===n.nodeType&&r.find.matchesSelector(n,e))){s.push(n);break}return this.pushStack(s.length>1?r.uniqueSort(s):s)},index:function(e){return e?"string"==typeof e?oe.call(r(e),this[0]):oe.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(r.uniqueSort(r.merge(this.get(),r(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),r.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return ge(e,"parentNode")},parentsUntil:function(e,t,n){return ge(e,"parentNode",n)},next:function(e){return Ye(e,"nextSibling")},prev:function(e){return Ye(e,"previousSibling")},nextAll:function(e){return ge(e,"nextSibling")},prevAll:function(e){return ge(e,"previousSibling")},nextUntil:function(e,t,n){return ge(e,"nextSibling",n)},prevUntil:function(e,t,n){return ge(e,"previousSibling",n)},siblings:function(e){return ue((e.parentNode||{}).firstChild,e)},children:function(e){return ue(e.firstChild)},contents:function(e){return null!=e.contentDocument&&ae(e.contentDocument)?e.contentDocument:(M(e,"template")&&(e=e.content||e),r.merge([],e.childNodes))}},function(e,t){r.fn[e]=function(n,i){var a=r.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(a=r.filter(i,a)),this.length>1&&(pt[e]||r.uniqueSort(a),kt.test(e)&&a.reverse()),this.pushStack(a)}});var Ae=/[^\x20\t\r\n\f]+/g;function rt(e){return e}function Et(e){throw e}function on(e,t,n,i){var a;try{e&&N(a=e.promise)?a.call(e).done(t).fail(n):e&&N(a=e.then)?a.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(s){n.apply(void 0,[s])}}r.Callbacks=function(e){e="string"==typeof e?function ht(e){var t={};return r.each(e.match(Ae)||[],function(n,i){t[i]=!0}),t}(e):r.extend({},e);var t,n,i,a,s=[],o=[],l=-1,f=function(){for(a=a||e.once,i=t=!0;o.length;l=-1)for(n=o.shift();++l<s.length;)!1===s[l].apply(n[0],n[1])&&e.stopOnFalse&&(l=s.length,n=!1);e.memory||(n=!1),t=!1,a&&(s=n?[]:"")},d={add:function(){return s&&(n&&!t&&(l=s.length-1,o.push(n)),function v(x){r.each(x,function(g,w){N(w)?(!e.unique||!d.has(w))&&s.push(w):w&&w.length&&"string"!==A(w)&&v(w)})}(arguments),n&&!t&&f()),this},remove:function(){return r.each(arguments,function(v,x){for(var g;(g=r.inArray(x,s,g))>-1;)s.splice(g,1),g<=l&&l--}),this},has:function(v){return v?r.inArray(v,s)>-1:s.length>0},empty:function(){return s&&(s=[]),this},disable:function(){return a=o=[],s=n="",this},disabled:function(){return!s},lock:function(){return a=o=[],!n&&!t&&(s=n=""),this},locked:function(){return!!a},fireWith:function(v,x){return a||(x=[v,(x=x||[]).slice?x.slice():x],o.push(x),t||f()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!i}};return d},r.extend({Deferred:function(e){var t=[["notify","progress",r.Callbacks("memory"),r.Callbacks("memory"),2],["resolve","done",r.Callbacks("once memory"),r.Callbacks("once memory"),0,"resolved"],["reject","fail",r.Callbacks("once memory"),r.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return a.done(arguments).fail(arguments),this},catch:function(s){return i.then(null,s)},pipe:function(){var s=arguments;return r.Deferred(function(o){r.each(t,function(l,f){var d=N(s[f[4]])&&s[f[4]];a[f[1]](function(){var v=d&&d.apply(this,arguments);v&&N(v.promise)?v.promise().progress(o.notify).done(o.resolve).fail(o.reject):o[f[0]+"With"](this,d?[v]:arguments)})}),s=null}).promise()},then:function(s,o,l){var f=0;function d(v,x,g,w){return function(){var _=this,G=arguments,W=function(){var se,qe;if(!(v<f)){if((se=g.apply(_,G))===x.promise())throw new TypeError("Thenable self-resolution");N(qe=se&&("object"==typeof se||"function"==typeof se)&&se.then)?w?qe.call(se,d(f,x,rt,w),d(f,x,Et,w)):(f++,qe.call(se,d(f,x,rt,w),d(f,x,Et,w),d(f,x,rt,x.notifyWith))):(g!==rt&&(_=void 0,G=[se]),(w||x.resolveWith)(_,G))}},le=w?W:function(){try{W()}catch(se){r.Deferred.exceptionHook&&r.Deferred.exceptionHook(se,le.error),v+1>=f&&(g!==Et&&(_=void 0,G=[se]),x.rejectWith(_,G))}};v?le():(r.Deferred.getErrorHook?le.error=r.Deferred.getErrorHook():r.Deferred.getStackHook&&(le.error=r.Deferred.getStackHook()),T.setTimeout(le))}}return r.Deferred(function(v){t[0][3].add(d(0,v,N(l)?l:rt,v.notifyWith)),t[1][3].add(d(0,v,N(s)?s:rt)),t[2][3].add(d(0,v,N(o)?o:Et))}).promise()},promise:function(s){return null!=s?r.extend(s,i):i}},a={};return r.each(t,function(s,o){var l=o[2],f=o[5];i[o[1]]=l.add,f&&l.add(function(){n=f},t[3-s][2].disable,t[3-s][3].disable,t[0][2].lock,t[0][3].lock),l.add(o[3].fire),a[o[0]]=function(){return a[o[0]+"With"](this===a?void 0:this,arguments),this},a[o[0]+"With"]=l.fireWith}),i.promise(a),e&&e.call(a,a),a},when:function(e){var t=arguments.length,n=t,i=Array(n),a=E.call(arguments),s=r.Deferred(),o=function(l){return function(f){i[l]=this,a[l]=arguments.length>1?E.call(arguments):f,--t||s.resolveWith(i,a)}};if(t<=1&&(on(e,s.done(o(n)).resolve,s.reject,!t),"pending"===s.state()||N(a[n]&&a[n].then)))return s.then();for(;n--;)on(a[n],o(n),s.reject);return s.promise()}});var Wn=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;r.Deferred.exceptionHook=function(e,t){T.console&&T.console.warn&&e&&Wn.test(e.name)&&T.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},r.readyException=function(e){T.setTimeout(function(){throw e})};var _t=r.Deferred();function Ft(){p.removeEventListener("DOMContentLoaded",Ft),T.removeEventListener("load",Ft),r.ready()}r.fn.ready=function(e){return _t.then(e).catch(function(t){r.readyException(t)}),this},r.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--r.readyWait:r.isReady)||(r.isReady=!0,!(!0!==e&&--r.readyWait>0)&&_t.resolveWith(p,[r]))}}),r.ready.then=_t.then,"complete"===p.readyState||"loading"!==p.readyState&&!p.documentElement.doScroll?T.setTimeout(r.ready):(p.addEventListener("DOMContentLoaded",Ft),T.addEventListener("load",Ft));var Me=function(e,t,n,i,a,s,o){var l=0,f=e.length,d=null==n;if("object"===A(n))for(l in a=!0,n)Me(e,t,l,n[l],!0,s,o);else if(void 0!==i&&(a=!0,N(i)||(o=!0),d&&(o?(t.call(e,i),t=null):(d=t,t=function(v,x,g){return d.call(r(v),g)})),t))for(;l<f;l++)t(e[l],n,o?i:i.call(e[l],l,t(e[l],n)));return a?e:d?t.call(e):f?t(e[0],n):s},Bn=/^-ms-/,zn=/-([a-z])/g;function Un(e,t){return t.toUpperCase()}function Le(e){return e.replace(Bn,"ms-").replace(zn,Un)}var gt=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function yt(){this.expando=r.expando+yt.uid++}yt.uid=1,yt.prototype={cache:function(e){var t=e[this.expando];return t||(t={},gt(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,a=this.cache(e);if("string"==typeof t)a[Le(t)]=n;else for(i in t)a[Le(i)]=t[i];return a},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][Le(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t)for((n=(t=Array.isArray(t)?t.map(Le):(t=Le(t))in i?[t]:t.match(Ae)||[]).length);n--;)delete i[t[n]];(void 0===t||r.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!r.isEmptyObject(t)}};var P=new yt,ye=new yt,Xn=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Vn=/[A-Z]/g;function un(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(Vn,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n=function Gn(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:Xn.test(e)?JSON.parse(e):e)}(n)}catch{}ye.set(e,t,n)}else n=void 0;return n}r.extend({hasData:function(e){return ye.hasData(e)||P.hasData(e)},data:function(e,t,n){return ye.access(e,t,n)},removeData:function(e,t){ye.remove(e,t)},_data:function(e,t,n){return P.access(e,t,n)},_removeData:function(e,t){P.remove(e,t)}}),r.fn.extend({data:function(e,t){var n,i,a,s=this[0],o=s&&s.attributes;if(void 0===e){if(this.length&&(a=ye.get(s),1===s.nodeType&&!P.get(s,"hasDataAttrs"))){for(n=o.length;n--;)o[n]&&0===(i=o[n].name).indexOf("data-")&&(i=Le(i.slice(5)),un(s,i,a[i]));P.set(s,"hasDataAttrs",!0)}return a}return"object"==typeof e?this.each(function(){ye.set(this,e)}):Me(this,function(l){var f;if(s&&void 0===l)return void 0!==(f=ye.get(s,e))||void 0!==(f=un(s,e))?f:void 0;this.each(function(){ye.set(this,e,l)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){ye.remove(this,e)})}}),r.extend({queue:function(e,t,n){var i;if(e)return i=P.get(e,t=(t||"fx")+"queue"),n&&(!i||Array.isArray(n)?i=P.access(e,t,r.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){var n=r.queue(e,t=t||"fx"),i=n.length,a=n.shift(),s=r._queueHooks(e,t);"inprogress"===a&&(a=n.shift(),i--),a&&("fx"===t&&n.unshift("inprogress"),delete s.stop,a.call(e,function(){r.dequeue(e,t)},s)),!i&&s&&s.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return P.get(e,n)||P.access(e,n,{empty:r.Callbacks("once memory").add(function(){P.remove(e,[t+"queue",n])})})}}),r.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?r.queue(this[0],e):void 0===t?this:this.each(function(){var i=r.queue(this,e,t);r._queueHooks(this,e),"fx"===e&&"inprogress"!==i[0]&&r.dequeue(this,e)})},dequeue:function(e){return this.each(function(){r.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,a=r.Deferred(),s=this,o=this.length,l=function(){--i||a.resolveWith(s,[s])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";o--;)(n=P.get(s[o],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(l));return l(),a.promise(t)}});var fn=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,vt=new RegExp("^(?:([+-])=|)("+fn+")([a-z%]*)$","i"),Re=["Top","Right","Bottom","Left"],Ze=p.documentElement,it=function(e){return r.contains(e.ownerDocument,e)},Qn={composed:!0};Ze.getRootNode&&(it=function(e){return r.contains(e.ownerDocument,e)||e.getRootNode(Qn)===e.ownerDocument});var Dt=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&it(e)&&"none"===r.css(e,"display")};function ln(e,t,n,i){var a,s,o=20,l=i?function(){return i.cur()}:function(){return r.css(e,t,"")},f=l(),d=n&&n[3]||(r.cssNumber[t]?"":"px"),v=e.nodeType&&(r.cssNumber[t]||"px"!==d&&+f)&&vt.exec(r.css(e,t));if(v&&v[3]!==d){for(d=d||v[3],v=+(f/=2)||1;o--;)r.style(e,t,v+d),(1-s)*(1-(s=l()/f||.5))<=0&&(o=0),v/=s;r.style(e,t,(v*=2)+d),n=n||[]}return n&&(v=+v||+f||0,a=n[1]?v+(n[1]+1)*n[2]:+n[2],i&&(i.unit=d,i.start=v,i.end=a)),a}var cn={};function Yn(e){var t,n=e.ownerDocument,i=e.nodeName,a=cn[i];return a||(t=n.body.appendChild(n.createElement(i)),a=r.css(t,"display"),t.parentNode.removeChild(t),"none"===a&&(a="block"),cn[i]=a,a)}function at(e,t){for(var n,i,a=[],s=0,o=e.length;s<o;s++)(i=e[s]).style&&(n=i.style.display,t?("none"===n&&(a[s]=P.get(i,"display")||null,a[s]||(i.style.display="")),""===i.style.display&&Dt(i)&&(a[s]=Yn(i))):"none"!==n&&(a[s]="none",P.set(i,"display",n)));for(s=0;s<o;s++)null!=a[s]&&(e[s].style.display=a[s]);return e}r.fn.extend({show:function(){return at(this,!0)},hide:function(){return at(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Dt(this)?r(this).show():r(this).hide()})}});var t,n,bt=/^(?:checkbox|radio)$/i,dn=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,pn=/^$|^module$|\/(?:java|ecma)script/i;t=p.createDocumentFragment().appendChild(p.createElement("div")),(n=p.createElement("input")).setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),F.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",F.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,t.innerHTML="<option></option>",F.option=!!t.lastChild;var ke={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ve(e,t){var n;return n=typeof e.getElementsByTagName<"u"?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll<"u"?e.querySelectorAll(t||"*"):[],void 0===t||t&&M(e,t)?r.merge([e],n):n}function Mt(e,t){for(var n=0,i=e.length;n<i;n++)P.set(e[n],"globalEval",!t||P.get(t[n],"globalEval"))}ke.tbody=ke.tfoot=ke.colgroup=ke.caption=ke.thead,ke.th=ke.td,F.option||(ke.optgroup=ke.option=[1,"<select multiple='multiple'>","</select>"]);var Zn=/<|&#?\w+;/;function hn(e,t,n,i,a){for(var s,o,l,f,d,v,x=t.createDocumentFragment(),g=[],w=0,_=e.length;w<_;w++)if((s=e[w])||0===s)if("object"===A(s))r.merge(g,s.nodeType?[s]:s);else if(Zn.test(s)){for(o=o||x.appendChild(t.createElement("div")),l=(dn.exec(s)||["",""])[1].toLowerCase(),o.innerHTML=(f=ke[l]||ke._default)[1]+r.htmlPrefilter(s)+f[2],v=f[0];v--;)o=o.lastChild;r.merge(g,o.childNodes),(o=x.firstChild).textContent=""}else g.push(t.createTextNode(s));for(x.textContent="",w=0;s=g[w++];)if(i&&r.inArray(s,i)>-1)a&&a.push(s);else if(d=it(s),o=ve(x.appendChild(s),"script"),d&&Mt(o),n)for(v=0;s=o[v++];)pn.test(s.type||"")&&n.push(s);return x}var gn=/^([^.]*)(?:\.(.+)|)/;function st(){return!0}function ot(){return!1}function Rt(e,t,n,i,a,s){var o,l;if("object"==typeof t){for(l in"string"!=typeof n&&(i=i||n,n=void 0),t)Rt(e,l,n,i,t[l],s);return e}if(null==i&&null==a?(a=n,i=n=void 0):null==a&&("string"==typeof n?(a=i,i=void 0):(a=i,i=n,n=void 0)),!1===a)a=ot;else if(!a)return e;return 1===s&&(o=a,a=function(f){return r().off(f),o.apply(this,arguments)},a.guid=o.guid||(o.guid=r.guid++)),e.each(function(){r.event.add(this,t,a,i,n)})}function jt(e,t,n){n?(P.set(e,t,!1),r.event.add(e,t,{namespace:!1,handler:function(i){var a,s=P.get(this,t);if(1&i.isTrigger&&this[t]){if(s)(r.event.special[t]||{}).delegateType&&i.stopPropagation();else if(s=E.call(arguments),P.set(this,t,s),this[t](),a=P.get(this,t),P.set(this,t,!1),s!==a)return i.stopImmediatePropagation(),i.preventDefault(),a}else s&&(P.set(this,t,r.event.trigger(s[0],s.slice(1),this)),i.stopPropagation(),i.isImmediatePropagationStopped=st)}})):void 0===P.get(e,t)&&r.event.add(e,t,st)}r.event={global:{},add:function(e,t,n,i,a){var s,o,l,f,d,v,x,g,w,_,G,W=P.get(e);if(gt(e))for(n.handler&&(n=(s=n).handler,a=s.selector),a&&r.find.matchesSelector(Ze,a),n.guid||(n.guid=r.guid++),(f=W.events)||(f=W.events=Object.create(null)),(o=W.handle)||(o=W.handle=function(le){return typeof r<"u"&&r.event.triggered!==le.type?r.event.dispatch.apply(e,arguments):void 0}),d=(t=(t||"").match(Ae)||[""]).length;d--;)w=G=(l=gn.exec(t[d])||[])[1],_=(l[2]||"").split(".").sort(),w&&(x=r.event.special[w]||{},x=r.event.special[w=(a?x.delegateType:x.bindType)||w]||{},v=r.extend({type:w,origType:G,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&r.expr.match.needsContext.test(a),namespace:_.join(".")},s),(g=f[w])||((g=f[w]=[]).delegateCount=0,(!x.setup||!1===x.setup.call(e,i,_,o))&&e.addEventListener&&e.addEventListener(w,o)),x.add&&(x.add.call(e,v),v.handler.guid||(v.handler.guid=n.guid)),a?g.splice(g.delegateCount++,0,v):g.push(v),r.event.global[w]=!0)},remove:function(e,t,n,i,a){var s,o,l,f,d,v,x,g,w,_,G,W=P.hasData(e)&&P.get(e);if(W&&(f=W.events)){for(d=(t=(t||"").match(Ae)||[""]).length;d--;)if(w=G=(l=gn.exec(t[d])||[])[1],_=(l[2]||"").split(".").sort(),w){for(x=r.event.special[w]||{},g=f[w=(i?x.delegateType:x.bindType)||w]||[],l=l[2]&&new RegExp("(^|\\.)"+_.join("\\.(?:.*\\.|)")+"(\\.|$)"),o=s=g.length;s--;)v=g[s],(a||G===v.origType)&&(!n||n.guid===v.guid)&&(!l||l.test(v.namespace))&&(!i||i===v.selector||"**"===i&&v.selector)&&(g.splice(s,1),v.selector&&g.delegateCount--,x.remove&&x.remove.call(e,v));o&&!g.length&&((!x.teardown||!1===x.teardown.call(e,_,W.handle))&&r.removeEvent(e,w,W.handle),delete f[w])}else for(w in f)r.event.remove(e,w+t[d],n,i,!0);r.isEmptyObject(f)&&P.remove(e,"handle events")}},dispatch:function(e){var t,n,i,a,s,o,l=new Array(arguments.length),f=r.event.fix(e),d=(P.get(this,"events")||Object.create(null))[f.type]||[],v=r.event.special[f.type]||{};for(l[0]=f,t=1;t<arguments.length;t++)l[t]=arguments[t];if(f.delegateTarget=this,!v.preDispatch||!1!==v.preDispatch.call(this,f)){for(o=r.event.handlers.call(this,f,d),t=0;(a=o[t++])&&!f.isPropagationStopped();)for(f.currentTarget=a.elem,n=0;(s=a.handlers[n++])&&!f.isImmediatePropagationStopped();)(!f.rnamespace||!1===s.namespace||f.rnamespace.test(s.namespace))&&(f.handleObj=s,f.data=s.data,void 0!==(i=((r.event.special[s.origType]||{}).handle||s.handler).apply(a.elem,l))&&!1===(f.result=i)&&(f.preventDefault(),f.stopPropagation()));return v.postDispatch&&v.postDispatch.call(this,f),f.result}},handlers:function(e,t){var n,i,a,s,o,l=[],f=t.delegateCount,d=e.target;if(f&&d.nodeType&&!("click"===e.type&&e.button>=1))for(;d!==this;d=d.parentNode||this)if(1===d.nodeType&&("click"!==e.type||!0!==d.disabled)){for(s=[],o={},n=0;n<f;n++)void 0===o[a=(i=t[n]).selector+" "]&&(o[a]=i.needsContext?r(a,this).index(d)>-1:r.find(a,this,null,[d]).length),o[a]&&s.push(i);s.length&&l.push({elem:d,handlers:s})}return d=this,f<t.length&&l.push({elem:d,handlers:t.slice(f)}),l},addProp:function(e,t){Object.defineProperty(r.Event.prototype,e,{enumerable:!0,configurable:!0,get:N(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(n){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:n})}})},fix:function(e){return e[r.expando]?e:new r.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return bt.test(t.type)&&t.click&&M(t,"input")&&jt(t,"click",!0),!1},trigger:function(e){var t=this||e;return bt.test(t.type)&&t.click&&M(t,"input")&&jt(t,"click"),!0},_default:function(e){var t=e.target;return bt.test(t.type)&&t.click&&M(t,"input")&&P.get(t,"click")||M(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},r.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},r.Event=function(e,t){if(!(this instanceof r.Event))return new r.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?st:ot,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&r.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[r.expando]=!0},r.Event.prototype={constructor:r.Event,isDefaultPrevented:ot,isPropagationStopped:ot,isImmediatePropagationStopped:ot,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=st,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=st,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=st,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},r.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},r.event.addProp),r.each({focus:"focusin",blur:"focusout"},function(e,t){function n(i){if(p.documentMode){var a=P.get(this,"handle"),s=r.event.fix(i);s.type="focusin"===i.type?"focus":"blur",s.isSimulated=!0,a(i),s.target===s.currentTarget&&a(s)}else r.event.simulate(t,i.target,r.event.fix(i))}r.event.special[e]={setup:function(){var i;if(jt(this,e,!0),!p.documentMode)return!1;(i=P.get(this,t))||this.addEventListener(t,n),P.set(this,t,(i||0)+1)},trigger:function(){return jt(this,e),!0},teardown:function(){var i;if(!p.documentMode)return!1;(i=P.get(this,t)-1)?P.set(this,t,i):(this.removeEventListener(t,n),P.remove(this,t))},_default:function(i){return P.get(i.target,e)},delegateType:t},r.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,a=p.documentMode?this:i,s=P.get(a,t);s||(p.documentMode?this.addEventListener(t,n):i.addEventListener(e,n,!0)),P.set(a,t,(s||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,a=p.documentMode?this:i,s=P.get(a,t)-1;s?P.set(a,t,s):(p.documentMode?this.removeEventListener(t,n):i.removeEventListener(e,n,!0),P.remove(a,t))}}}),r.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){r.event.special[e]={delegateType:t,bindType:t,handle:function(n){var i,s=n.relatedTarget,o=n.handleObj;return(!s||s!==this&&!r.contains(this,s))&&(n.type=o.origType,i=o.handler.apply(this,arguments),n.type=t),i}}}),r.fn.extend({on:function(e,t,n,i){return Rt(this,e,t,n,i)},one:function(e,t,n,i){return Rt(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,r(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(a in e)this.off(a,t,e[a]);return this}return(!1===t||"function"==typeof t)&&(n=t,t=void 0),!1===n&&(n=ot),this.each(function(){r.event.remove(this,e,n,t)})}});var Jn=/<script|<style|<link/i,Kn=/checked\s*(?:[^=]|=\s*.checked.)/i,er=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function yn(e,t){return M(e,"table")&&M(11!==t.nodeType?t:t.firstChild,"tr")&&r(e).children("tbody")[0]||e}function tr(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function nr(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function vn(e,t){var n,i,a,o,l,f;if(1===t.nodeType){if(P.hasData(e)&&(f=P.get(e).events))for(a in P.remove(t,"handle events"),f)for(n=0,i=f[a].length;n<i;n++)r.event.add(t,a,f[a][n]);ye.hasData(e)&&(o=ye.access(e),l=r.extend({},o),ye.set(t,l))}}function rr(e,t){var n=t.nodeName.toLowerCase();"input"===n&&bt.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function ut(e,t,n,i){t=te(t);var a,s,o,l,f,d,v=0,x=e.length,g=x-1,w=t[0],_=N(w);if(_||x>1&&"string"==typeof w&&!F.checkClone&&Kn.test(w))return e.each(function(G){var W=e.eq(G);_&&(t[0]=w.call(this,G,W.html())),ut(W,t,n,i)});if(x&&(s=(a=hn(t,e[0].ownerDocument,!1,e,i)).firstChild,1===a.childNodes.length&&(a=s),s||i)){for(l=(o=r.map(ve(a,"script"),tr)).length;v<x;v++)f=a,v!==g&&(f=r.clone(f,!0,!0),l&&r.merge(o,ve(f,"script"))),n.call(e[v],f,v);if(l)for(d=o[o.length-1].ownerDocument,r.map(o,nr),v=0;v<l;v++)pn.test((f=o[v]).type||"")&&!P.access(f,"globalEval")&&r.contains(d,f)&&(f.src&&"module"!==(f.type||"").toLowerCase()?r._evalUrl&&!f.noModule&&r._evalUrl(f.src,{nonce:f.nonce||f.getAttribute("nonce")},d):k(f.textContent.replace(er,""),f,d))}return e}function bn(e,t,n){for(var i,a=t?r.filter(t,e):e,s=0;null!=(i=a[s]);s++)!n&&1===i.nodeType&&r.cleanData(ve(i)),i.parentNode&&(n&&it(i)&&Mt(ve(i,"script")),i.parentNode.removeChild(i));return e}r.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,a,s,o,l=e.cloneNode(!0),f=it(e);if(!(F.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||r.isXMLDoc(e)))for(o=ve(l),i=0,a=(s=ve(e)).length;i<a;i++)rr(s[i],o[i]);if(t)if(n)for(s=s||ve(e),o=o||ve(l),i=0,a=s.length;i<a;i++)vn(s[i],o[i]);else vn(e,l);return(o=ve(l,"script")).length>0&&Mt(o,!f&&ve(e,"script")),l},cleanData:function(e){for(var t,n,i,a=r.event.special,s=0;void 0!==(n=e[s]);s++)if(gt(n)){if(t=n[P.expando]){if(t.events)for(i in t.events)a[i]?r.event.remove(n,i):r.removeEvent(n,i,t.handle);n[P.expando]=void 0}n[ye.expando]&&(n[ye.expando]=void 0)}}}),r.fn.extend({detach:function(e){return bn(this,e,!0)},remove:function(e){return bn(this,e)},text:function(e){return Me(this,function(t){return void 0===t?r.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=t)})},null,e,arguments.length)},append:function(){return ut(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||yn(this,e).appendChild(e)})},prepend:function(){return ut(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=yn(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return ut(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return ut(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(r.cleanData(ve(e,!1)),e.textContent="");return this},clone:function(e,t){return e=e??!1,t=t??e,this.map(function(){return r.clone(this,e,t)})},html:function(e){return Me(this,function(t){var n=this[0]||{},i=0,a=this.length;if(void 0===t&&1===n.nodeType)return n.innerHTML;if("string"==typeof t&&!Jn.test(t)&&!ke[(dn.exec(t)||["",""])[1].toLowerCase()]){t=r.htmlPrefilter(t);try{for(;i<a;i++)1===(n=this[i]||{}).nodeType&&(r.cleanData(ve(n,!1)),n.innerHTML=t);n=0}catch{}}n&&this.empty().append(t)},null,e,arguments.length)},replaceWith:function(){var e=[];return ut(this,arguments,function(t){var n=this.parentNode;r.inArray(this,e)<0&&(r.cleanData(ve(this)),n&&n.replaceChild(t,this))},e)}}),r.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){r.fn[e]=function(n){for(var i,a=[],s=r(n),o=s.length-1,l=0;l<=o;l++)i=l===o?this:this.clone(!0),r(s[l])[t](i),xe.apply(a,i.get());return this.pushStack(a)}});var It=new RegExp("^("+fn+")(?!px)[a-z%]+$","i"),Wt=/^--/,Nt=function(e){var t=e.ownerDocument.defaultView;return(!t||!t.opener)&&(t=T),t.getComputedStyle(e)},mn=function(e,t,n){var i,a,s={};for(a in t)s[a]=e.style[a],e.style[a]=t[a];for(a in i=n.call(e),t)e.style[a]=s[a];return i},ir=new RegExp(Re.join("|"),"i");function mt(e,t,n){var i,a,s,o,l=Wt.test(t),f=e.style;return(n=n||Nt(e))&&(o=n.getPropertyValue(t)||n[t],l&&o&&(o=o.replace(Ne,"$1")||void 0),""===o&&!it(e)&&(o=r.style(e,t)),!F.pixelBoxStyles()&&It.test(o)&&ir.test(t)&&(i=f.width,a=f.minWidth,s=f.maxWidth,f.minWidth=f.maxWidth=f.width=o,o=n.width,f.width=i,f.minWidth=a,f.maxWidth=s)),void 0!==o?o+"":o}function xn(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(d){f.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",d.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Ze.appendChild(f).appendChild(d);var v=T.getComputedStyle(d);n="1%"!==v.top,l=12===t(v.marginLeft),d.style.right="60%",s=36===t(v.right),i=36===t(v.width),d.style.position="absolute",a=12===t(d.offsetWidth/3),Ze.removeChild(f),d=null}}function t(v){return Math.round(parseFloat(v))}var n,i,a,s,o,l,f=p.createElement("div"),d=p.createElement("div");d.style&&(d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",F.clearCloneStyle="content-box"===d.style.backgroundClip,r.extend(F,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var v,x,g,w;return null==o&&(v=p.createElement("table"),x=p.createElement("tr"),g=p.createElement("div"),v.style.cssText="position:absolute;left:-11111px;border-collapse:separate",x.style.cssText="box-sizing:content-box;border:1px solid",x.style.height="1px",g.style.height="9px",g.style.display="block",Ze.appendChild(v).appendChild(x).appendChild(g),w=T.getComputedStyle(x),o=parseInt(w.height,10)+parseInt(w.borderTopWidth,10)+parseInt(w.borderBottomWidth,10)===x.offsetHeight,Ze.removeChild(v)),o}}))}();var wn=["Webkit","Moz","ms"],Tn=p.createElement("div").style,Sn={};function Bt(e){return r.cssProps[e]||Sn[e]||(e in Tn?e:Sn[e]=function ar(e){for(var t=e[0].toUpperCase()+e.slice(1),n=wn.length;n--;)if((e=wn[n]+t)in Tn)return e}(e)||e)}var sr=/^(none|table(?!-c[ea]).+)/,or={position:"absolute",visibility:"hidden",display:"block"},Cn={letterSpacing:"0",fontWeight:"400"};function An(e,t,n){var i=vt.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function zt(e,t,n,i,a,s){var o="width"===t?1:0,l=0,f=0,d=0;if(n===(i?"border":"content"))return 0;for(;o<4;o+=2)"margin"===n&&(d+=r.css(e,n+Re[o],!0,a)),i?("content"===n&&(f-=r.css(e,"padding"+Re[o],!0,a)),"margin"!==n&&(f-=r.css(e,"border"+Re[o]+"Width",!0,a))):(f+=r.css(e,"padding"+Re[o],!0,a),"padding"!==n?f+=r.css(e,"border"+Re[o]+"Width",!0,a):l+=r.css(e,"border"+Re[o]+"Width",!0,a));return!i&&s>=0&&(f+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-s-f-l-.5))||0),f+d}function kn(e,t,n){var i=Nt(e),s=(!F.boxSizingReliable()||n)&&"border-box"===r.css(e,"boxSizing",!1,i),o=s,l=mt(e,t,i),f="offset"+t[0].toUpperCase()+t.slice(1);if(It.test(l)){if(!n)return l;l="auto"}return(!F.boxSizingReliable()&&s||!F.reliableTrDimensions()&&M(e,"tr")||"auto"===l||!parseFloat(l)&&"inline"===r.css(e,"display",!1,i))&&e.getClientRects().length&&(s="border-box"===r.css(e,"boxSizing",!1,i),(o=f in e)&&(l=e[f])),(l=parseFloat(l)||0)+zt(e,t,n||(s?"border":"content"),o,i,l)+"px"}function be(e,t,n,i,a){return new be.prototype.init(e,t,n,i,a)}r.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=mt(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,s,o,l=Le(t),f=Wt.test(t),d=e.style;if(f||(t=Bt(l)),o=r.cssHooks[t]||r.cssHooks[l],void 0===n)return o&&"get"in o&&void 0!==(a=o.get(e,!1,i))?a:d[t];if("string"==(s=typeof n)&&(a=vt.exec(n))&&a[1]&&(n=ln(e,t,a),s="number"),null==n||n!=n)return;"number"===s&&!f&&(n+=a&&a[3]||(r.cssNumber[l]?"":"px")),!F.clearCloneStyle&&""===n&&0===t.indexOf("background")&&(d[t]="inherit"),(!o||!("set"in o)||void 0!==(n=o.set(e,n,i)))&&(f?d.setProperty(t,n):d[t]=n)}},css:function(e,t,n,i){var a,s,o,l=Le(t);return Wt.test(t)||(t=Bt(l)),(o=r.cssHooks[t]||r.cssHooks[l])&&"get"in o&&(a=o.get(e,!0,n)),void 0===a&&(a=mt(e,t,i)),"normal"===a&&t in Cn&&(a=Cn[t]),""===n||n?(s=parseFloat(a),!0===n||isFinite(s)?s||0:a):a}}),r.each(["height","width"],function(e,t){r.cssHooks[t]={get:function(n,i,a){if(i)return!sr.test(r.css(n,"display"))||n.getClientRects().length&&n.getBoundingClientRect().width?kn(n,t,a):mn(n,or,function(){return kn(n,t,a)})},set:function(n,i,a){var s,o=Nt(n),l=!F.scrollboxSize()&&"absolute"===o.position,d=(l||a)&&"border-box"===r.css(n,"boxSizing",!1,o),v=a?zt(n,t,a,d,o):0;return d&&l&&(v-=Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-zt(n,t,"border",!1,o)-.5)),v&&(s=vt.exec(i))&&"px"!==(s[3]||"px")&&(n.style[t]=i,i=r.css(n,t)),An(0,i,v)}}}),r.cssHooks.marginLeft=xn(F.reliableMarginLeft,function(e,t){if(t)return(parseFloat(mt(e,"marginLeft"))||e.getBoundingClientRect().left-mn(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),r.each({margin:"",padding:"",border:"Width"},function(e,t){r.cssHooks[e+t]={expand:function(n){for(var i=0,a={},s="string"==typeof n?n.split(" "):[n];i<4;i++)a[e+Re[i]+t]=s[i]||s[i-2]||s[0];return a}},"margin"!==e&&(r.cssHooks[e+t].set=An)}),r.fn.extend({css:function(e,t){return Me(this,function(n,i,a){var s,o,l={},f=0;if(Array.isArray(i)){for(s=Nt(n),o=i.length;f<o;f++)l[i[f]]=r.css(n,i[f],!1,s);return l}return void 0!==a?r.style(n,i,a):r.css(n,i)},e,t,arguments.length>1)}}),r.Tween=be,be.prototype={constructor:be,init:function(e,t,n,i,a,s){this.elem=e,this.prop=n,this.easing=a||r.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=s||(r.cssNumber[n]?"":"px")},cur:function(){var e=be.propHooks[this.prop];return e&&e.get?e.get(this):be.propHooks._default.get(this)},run:function(e){var t,n=be.propHooks[this.prop];return this.pos=t=this.options.duration?r.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):be.propHooks._default.set(this),this}},be.prototype.init.prototype=be.prototype,be.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=r.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){r.fx.step[e.prop]?r.fx.step[e.prop](e):1!==e.elem.nodeType||!r.cssHooks[e.prop]&&null==e.elem.style[Bt(e.prop)]?e.elem[e.prop]=e.now:r.style(e.elem,e.prop,e.now+e.unit)}}},be.propHooks.scrollTop=be.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},r.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},r.fx=be.prototype.init,r.fx.step={};var ft,Lt,ur=/^(?:toggle|show|hide)$/,fr=/queueHooks$/;function Ut(){Lt&&(!1===p.hidden&&T.requestAnimationFrame?T.requestAnimationFrame(Ut):T.setTimeout(Ut,r.fx.interval),r.fx.tick())}function En(){return T.setTimeout(function(){ft=void 0}),ft=Date.now()}function qt(e,t){var n,i=0,a={height:e};for(t=t?1:0;i<4;i+=2-t)a["margin"+(n=Re[i])]=a["padding"+n]=e;return t&&(a.opacity=a.width=e),a}function Fn(e,t,n){for(var i,a=(De.tweeners[t]||[]).concat(De.tweeners["*"]),s=0,o=a.length;s<o;s++)if(i=a[s].call(n,t,e))return i}function De(e,t,n){var i,a,s=0,o=De.prefilters.length,l=r.Deferred().always(function(){delete f.elem}),f=function(){if(a)return!1;for(var x=ft||En(),g=Math.max(0,d.startTime+d.duration-x),_=1-(g/d.duration||0),G=0,W=d.tweens.length;G<W;G++)d.tweens[G].run(_);return l.notifyWith(e,[d,_,g]),_<1&&W?g:(W||l.notifyWith(e,[d,1,0]),l.resolveWith(e,[d]),!1)},d=l.promise({elem:e,props:r.extend({},t),opts:r.extend(!0,{specialEasing:{},easing:r.easing._default},n),originalProperties:t,originalOptions:n,startTime:ft||En(),duration:n.duration,tweens:[],createTween:function(x,g){var w=r.Tween(e,d.opts,x,g,d.opts.specialEasing[x]||d.opts.easing);return d.tweens.push(w),w},stop:function(x){var g=0,w=x?d.tweens.length:0;if(a)return this;for(a=!0;g<w;g++)d.tweens[g].run(1);return x?(l.notifyWith(e,[d,1,0]),l.resolveWith(e,[d,x])):l.rejectWith(e,[d,x]),this}}),v=d.props;for(function cr(e,t){var n,i,a,s,o;for(n in e)if(a=t[i=Le(n)],s=e[n],Array.isArray(s)&&(a=s[1],s=e[n]=s[0]),n!==i&&(e[i]=s,delete e[n]),(o=r.cssHooks[i])&&"expand"in o)for(n in s=o.expand(s),delete e[i],s)n in e||(e[n]=s[n],t[n]=a);else t[i]=a}(v,d.opts.specialEasing);s<o;s++)if(i=De.prefilters[s].call(d,e,v,d.opts))return N(i.stop)&&(r._queueHooks(d.elem,d.opts.queue).stop=i.stop.bind(i)),i;return r.map(v,Fn,d),N(d.opts.start)&&d.opts.start.call(e,d),d.progress(d.opts.progress).done(d.opts.done,d.opts.complete).fail(d.opts.fail).always(d.opts.always),r.fx.timer(r.extend(f,{elem:e,anim:d,queue:d.opts.queue})),d}r.Animation=r.extend(De,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ln(n.elem,e,vt.exec(t),n),n}]},tweener:function(e,t){N(e)?(t=e,e=["*"]):e=e.match(Ae);for(var n,i=0,a=e.length;i<a;i++)(De.tweeners[n=e[i]]=De.tweeners[n]||[]).unshift(t)},prefilters:[function lr(e,t,n){var i,a,s,o,l,f,d,v,x="width"in t||"height"in t,g=this,w={},_=e.style,G=e.nodeType&&Dt(e),W=P.get(e,"fxshow");for(i in n.queue||(null==(o=r._queueHooks(e,"fx")).unqueued&&(o.unqueued=0,l=o.empty.fire,o.empty.fire=function(){o.unqueued||l()}),o.unqueued++,g.always(function(){g.always(function(){o.unqueued--,r.queue(e,"fx").length||o.empty.fire()})})),t)if(ur.test(a=t[i])){if(delete t[i],s=s||"toggle"===a,a===(G?"hide":"show")){if("show"!==a||!W||void 0===W[i])continue;G=!0}w[i]=W&&W[i]||r.style(e,i)}if((f=!r.isEmptyObject(t))||!r.isEmptyObject(w))for(i in x&&1===e.nodeType&&(n.overflow=[_.overflow,_.overflowX,_.overflowY],null==(d=W&&W.display)&&(d=P.get(e,"display")),"none"===(v=r.css(e,"display"))&&(d?v=d:(at([e],!0),d=e.style.display||d,v=r.css(e,"display"),at([e]))),("inline"===v||"inline-block"===v&&null!=d)&&"none"===r.css(e,"float")&&(f||(g.done(function(){_.display=d}),null==d&&(d="none"===(v=_.display)?"":v)),_.display="inline-block")),n.overflow&&(_.overflow="hidden",g.always(function(){_.overflow=n.overflow[0],_.overflowX=n.overflow[1],_.overflowY=n.overflow[2]})),f=!1,w)f||(W?"hidden"in W&&(G=W.hidden):W=P.access(e,"fxshow",{display:d}),s&&(W.hidden=!G),G&&at([e],!0),g.done(function(){for(i in G||at([e]),P.remove(e,"fxshow"),w)r.style(e,i,w[i])})),f=Fn(G?W[i]:0,i,g),i in W||(W[i]=f.start,G&&(f.end=f.start,f.start=0))}],prefilter:function(e,t){t?De.prefilters.unshift(e):De.prefilters.push(e)}}),r.speed=function(e,t,n){var i=e&&"object"==typeof e?r.extend({},e):{complete:n||!n&&t||N(e)&&e,duration:e,easing:n&&t||t&&!N(t)&&t};return r.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration=i.duration in r.fx.speeds?r.fx.speeds[i.duration]:r.fx.speeds._default),(null==i.queue||!0===i.queue)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){N(i.old)&&i.old.call(this),i.queue&&r.dequeue(this,i.queue)},i},r.fn.extend({fadeTo:function(e,t,n,i){return this.filter(Dt).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var a=r.isEmptyObject(e),s=r.speed(t,n,i),o=function(){var l=De(this,r.extend({},e),s);(a||P.get(this,"finish"))&&l.stop(!0)};return o.finish=o,a||!1===s.queue?this.each(o):this.queue(s.queue,o)},stop:function(e,t,n){var i=function(a){var s=a.stop;delete a.stop,s(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var a=!0,s=null!=e&&e+"queueHooks",o=r.timers,l=P.get(this);if(s)l[s]&&l[s].stop&&i(l[s]);else for(s in l)l[s]&&l[s].stop&&fr.test(s)&&i(l[s]);for(s=o.length;s--;)o[s].elem===this&&(null==e||o[s].queue===e)&&(o[s].anim.stop(n),a=!1,o.splice(s,1));(a||!n)&&r.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=P.get(this),i=n[e+"queue"],a=n[e+"queueHooks"],s=r.timers,o=i?i.length:0;for(n.finish=!0,r.queue(this,e,[]),a&&a.stop&&a.stop.call(this,!0),t=s.length;t--;)s[t].elem===this&&s[t].queue===e&&(s[t].anim.stop(!0),s.splice(t,1));for(t=0;t<o;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),r.each(["toggle","show","hide"],function(e,t){var n=r.fn[t];r.fn[t]=function(i,a,s){return null==i||"boolean"==typeof i?n.apply(this,arguments):this.animate(qt(t,!0),i,a,s)}}),r.each({slideDown:qt("show"),slideUp:qt("hide"),slideToggle:qt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){r.fn[e]=function(n,i,a){return this.animate(t,n,i,a)}}),r.timers=[],r.fx.tick=function(){var e,t=0,n=r.timers;for(ft=Date.now();t<n.length;t++)!(e=n[t])()&&n[t]===e&&n.splice(t--,1);n.length||r.fx.stop(),ft=void 0},r.fx.timer=function(e){r.timers.push(e),r.fx.start()},r.fx.interval=13,r.fx.start=function(){Lt||(Lt=!0,Ut())},r.fx.stop=function(){Lt=null},r.fx.speeds={slow:600,fast:200,_default:400},r.fn.delay=function(e,t){return e=r.fx&&r.fx.speeds[e]||e,this.queue(t=t||"fx",function(n,i){var a=T.setTimeout(n,e);i.stop=function(){T.clearTimeout(a)}})},function(){var e=p.createElement("input"),n=p.createElement("select").appendChild(p.createElement("option"));e.type="checkbox",F.checkOn=""!==e.value,F.optSelected=n.selected,(e=p.createElement("input")).value="t",e.type="radio",F.radioValue="t"===e.value}();var Dn,xt=r.expr.attrHandle;r.fn.extend({attr:function(e,t){return Me(this,r.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){r.removeAttr(this,e)})}}),r.extend({attr:function(e,t,n){var i,a,s=e.nodeType;if(3!==s&&8!==s&&2!==s)return typeof e.getAttribute>"u"?r.prop(e,t,n):((1!==s||!r.isXMLDoc(e))&&(a=r.attrHooks[t.toLowerCase()]||(r.expr.match.bool.test(t)?Dn:void 0)),void 0!==n?null===n?void r.removeAttr(e,t):a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:(e.setAttribute(t,n+""),n):a&&"get"in a&&null!==(i=a.get(e,t))?i:(i=r.find.attr(e,t))??void 0)},attrHooks:{type:{set:function(e,t){if(!F.radioValue&&"radio"===t&&M(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,a=t&&t.match(Ae);if(a&&1===e.nodeType)for(;n=a[i++];)e.removeAttribute(n)}}),Dn={set:function(e,t,n){return!1===t?r.removeAttr(e,n):e.setAttribute(n,n),n}},r.each(r.expr.match.bool.source.match(/\w+/g),function(e,t){var n=xt[t]||r.find.attr;xt[t]=function(i,a,s){var o,l,f=a.toLowerCase();return s||(l=xt[f],xt[f]=o,o=null!=n(i,a,s)?f:null,xt[f]=l),o}});var dr=/^(?:input|select|textarea|button)$/i,pr=/^(?:a|area)$/i;function Je(e){return(e.match(Ae)||[]).join(" ")}function Ke(e){return e.getAttribute&&e.getAttribute("class")||""}function Xt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(Ae)||[]}r.fn.extend({prop:function(e,t){return Me(this,r.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[r.propFix[e]||e]})}}),r.extend({prop:function(e,t,n){var i,a,s=e.nodeType;if(3!==s&&8!==s&&2!==s)return(1!==s||!r.isXMLDoc(e))&&(a=r.propHooks[t=r.propFix[t]||t]),void 0!==n?a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:e[t]=n:a&&"get"in a&&null!==(i=a.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=r.find.attr(e,"tabindex");return t?parseInt(t,10):dr.test(e.nodeName)||pr.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),F.optSelected||(r.propHooks.selected={get:function(e){return null},set:function(e){}}),r.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){r.propFix[this.toLowerCase()]=this}),r.fn.extend({addClass:function(e){var t,n,i,a,s,o;return N(e)?this.each(function(l){r(this).addClass(e.call(this,l,Ke(this)))}):(t=Xt(e)).length?this.each(function(){if(i=Ke(this),n=1===this.nodeType&&" "+Je(i)+" "){for(s=0;s<t.length;s++)n.indexOf(" "+(a=t[s])+" ")<0&&(n+=a+" ");o=Je(n),i!==o&&this.setAttribute("class",o)}}):this},removeClass:function(e){var t,n,i,a,s,o;return N(e)?this.each(function(l){r(this).removeClass(e.call(this,l,Ke(this)))}):arguments.length?(t=Xt(e)).length?this.each(function(){if(i=Ke(this),n=1===this.nodeType&&" "+Je(i)+" "){for(s=0;s<t.length;s++)for(a=t[s];n.indexOf(" "+a+" ")>-1;)n=n.replace(" "+a+" "," ");o=Je(n),i!==o&&this.setAttribute("class",o)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,i,a,s,o=typeof e,l="string"===o||Array.isArray(e);return N(e)?this.each(function(f){r(this).toggleClass(e.call(this,f,Ke(this),t),t)}):"boolean"==typeof t&&l?t?this.addClass(e):this.removeClass(e):(n=Xt(e),this.each(function(){if(l)for(s=r(this),a=0;a<n.length;a++)s.hasClass(i=n[a])?s.removeClass(i):s.addClass(i);else(void 0===e||"boolean"===o)&&((i=Ke(this))&&P.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===e?"":P.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+Je(Ke(n))+" ").indexOf(t)>-1)return!0;return!1}});var hr=/\r/g;r.fn.extend({val:function(e){var t,n,i,a=this[0];return arguments.length?(i=N(e),this.each(function(s){var o;1===this.nodeType&&(null==(o=i?e.call(this,s,r(this).val()):e)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=r.map(o,function(l){return null==l?"":l+""})),(!(t=r.valHooks[this.type]||r.valHooks[this.nodeName.toLowerCase()])||!("set"in t)||void 0===t.set(this,o,"value"))&&(this.value=o))})):a?(t=r.valHooks[a.type]||r.valHooks[a.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(a,"value"))?n:"string"==typeof(n=a.value)?n.replace(hr,""):n??"":void 0}}),r.extend({valHooks:{option:{get:function(e){return r.find.attr(e,"value")??Je(r.text(e))}},select:{get:function(e){var t,n,i,a=e.options,s=e.selectedIndex,o="select-one"===e.type,l=o?null:[],f=o?s+1:a.length;for(i=s<0?f:o?s:0;i<f;i++)if(((n=a[i]).selected||i===s)&&!n.disabled&&(!n.parentNode.disabled||!M(n.parentNode,"optgroup"))){if(t=r(n).val(),o)return t;l.push(t)}return l},set:function(e,t){for(var n,i,a=e.options,s=r.makeArray(t),o=a.length;o--;)((i=a[o]).selected=r.inArray(r.valHooks.option.get(i),s)>-1)&&(n=!0);return n||(e.selectedIndex=-1),s}}}}),r.each(["radio","checkbox"],function(){r.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=r.inArray(r(e).val(),t)>-1}},F.checkOn||(r.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var wt=T.location,jn={guid:Date.now()},Vt=/\?/;r.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new T.DOMParser).parseFromString(e,"text/xml")}catch{}return n=t&&t.getElementsByTagName("parsererror")[0],(!t||n)&&r.error("Invalid XML: "+(n?r.map(n.childNodes,function(i){return i.textContent}).join("\n"):e)),t};var Nn=/^(?:focusinfocus|focusoutblur)$/,Ln=function(e){e.stopPropagation()};r.extend(r.event,{trigger:function(e,t,n,i){var a,s,o,l,f,d,v,x,g=[n||p],w=he.call(e,"type")?e.type:e,_=he.call(e,"namespace")?e.namespace.split("."):[];if(s=x=o=n=n||p,3!==n.nodeType&&8!==n.nodeType&&!Nn.test(w+r.event.triggered)&&(w.indexOf(".")>-1&&(_=w.split("."),w=_.shift(),_.sort()),f=w.indexOf(":")<0&&"on"+w,(e=e[r.expando]?e:new r.Event(w,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=_.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+_.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:r.makeArray(t,[e]),v=r.event.special[w]||{},i||!v.trigger||!1!==v.trigger.apply(n,t))){if(!i&&!v.noBubble&&!b(n)){for(Nn.test((l=v.delegateType||w)+w)||(s=s.parentNode);s;s=s.parentNode)g.push(s),o=s;o===(n.ownerDocument||p)&&g.push(o.defaultView||o.parentWindow||T)}for(a=0;(s=g[a++])&&!e.isPropagationStopped();)x=s,e.type=a>1?l:v.bindType||w,(d=(P.get(s,"events")||Object.create(null))[e.type]&&P.get(s,"handle"))&&d.apply(s,t),(d=f&&s[f])&&d.apply&&gt(s)&&(e.result=d.apply(s,t),!1===e.result&&e.preventDefault());return e.type=w,!i&&!e.isDefaultPrevented()&&(!v._default||!1===v._default.apply(g.pop(),t))&&gt(n)&&f&&N(n[w])&&!b(n)&&((o=n[f])&&(n[f]=null),r.event.triggered=w,e.isPropagationStopped()&&x.addEventListener(w,Ln),n[w](),e.isPropagationStopped()&&x.removeEventListener(w,Ln),r.event.triggered=void 0,o&&(n[f]=o)),e.result}},simulate:function(e,t,n){var i=r.extend(new r.Event,n,{type:e,isSimulated:!0});r.event.trigger(i,null,t)}}),r.fn.extend({trigger:function(e,t){return this.each(function(){r.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return r.event.trigger(e,t,n,!0)}});var gr=/\[\]$/,qn=/\r?\n/g,yr=/^(?:submit|button|image|reset|file)$/i,vr=/^(?:input|select|textarea|keygen)/i;function Gt(e,t,n,i){var a;if(Array.isArray(t))r.each(t,function(s,o){n||gr.test(e)?i(e,o):Gt(e+"["+("object"==typeof o&&null!=o?s:"")+"]",o,n,i)});else if(n||"object"!==A(t))i(e,t);else for(a in t)Gt(e+"["+a+"]",t[a],n,i)}r.param=function(e,t){var n,i=[],a=function(s,o){var l=N(o)?o():o;i[i.length]=encodeURIComponent(s)+"="+encodeURIComponent(l??"")};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!r.isPlainObject(e))r.each(e,function(){a(this.name,this.value)});else for(n in e)Gt(n,e[n],t,a);return i.join("&")},r.fn.extend({serialize:function(){return r.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=r.prop(this,"elements");return e?r.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!r(this).is(":disabled")&&vr.test(this.nodeName)&&!yr.test(e)&&(this.checked||!bt.test(e))}).map(function(e,t){var n=r(this).val();return null==n?null:Array.isArray(n)?r.map(n,function(i){return{name:t.name,value:i.replace(qn,"\r\n")}}):{name:t.name,value:n.replace(qn,"\r\n")}}).get()}});var br=/%20/g,mr=/#.*$/,xr=/([?&])_=[^&]*/,wr=/^(.*?):[ \t]*([^\r\n]*)$/gm,Sr=/^(?:GET|HEAD)$/,Cr=/^\/\//,Pn={},Qt={},Hn="*/".concat("*"),Yt=p.createElement("a");function On(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,a=0,s=t.toLowerCase().match(Ae)||[];if(N(n))for(;i=s[a++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function $n(e,t,n,i){var a={},s=e===Qt;function o(l){var f;return a[l]=!0,r.each(e[l]||[],function(d,v){var x=v(t,n,i);return"string"!=typeof x||s||a[x]?s?!(f=x):void 0:(t.dataTypes.unshift(x),o(x),!1)}),f}return o(t.dataTypes[0])||!a["*"]&&o("*")}function Zt(e,t){var n,i,a=r.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((a[n]?e:i||(i={}))[n]=t[n]);return i&&r.extend(!0,e,i),e}Yt.href=wt.href,r.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:wt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(wt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Hn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":r.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Zt(Zt(e,r.ajaxSettings),t):Zt(r.ajaxSettings,e)},ajaxPrefilter:On(Pn),ajaxTransport:On(Qt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var n,i,a,s,o,l,f,d,v,x,g=r.ajaxSetup({},t=t||{}),w=g.context||g,_=g.context&&(w.nodeType||w.jquery)?r(w):r.event,G=r.Deferred(),W=r.Callbacks("once memory"),le=g.statusCode||{},se={},qe={},Pe="canceled",V={readyState:0,getResponseHeader:function(Y){var ie;if(f){if(!s)for(s={};ie=wr.exec(a);)s[ie[1].toLowerCase()+" "]=(s[ie[1].toLowerCase()+" "]||[]).concat(ie[2]);ie=s[Y.toLowerCase()+" "]}return null==ie?null:ie.join(", ")},getAllResponseHeaders:function(){return f?a:null},setRequestHeader:function(Y,ie){return null==f&&(Y=qe[Y.toLowerCase()]=qe[Y.toLowerCase()]||Y,se[Y]=ie),this},overrideMimeType:function(Y){return null==f&&(g.mimeType=Y),this},statusCode:function(Y){var ie;if(Y)if(f)V.always(Y[V.status]);else for(ie in Y)le[ie]=[le[ie],Y[ie]];return this},abort:function(Y){var ie=Y||Pe;return n&&n.abort(ie),et(0,ie),this}};if(G.promise(V),g.url=((e||g.url||wt.href)+"").replace(Cr,wt.protocol+"//"),g.type=t.method||t.type||g.method||g.type,g.dataTypes=(g.dataType||"*").toLowerCase().match(Ae)||[""],null==g.crossDomain){l=p.createElement("a");try{l.href=g.url,l.href=l.href,g.crossDomain=Yt.protocol+"//"+Yt.host!=l.protocol+"//"+l.host}catch{g.crossDomain=!0}}if(g.data&&g.processData&&"string"!=typeof g.data&&(g.data=r.param(g.data,g.traditional)),$n(Pn,g,t,V),f)return V;for(v in(d=r.event&&g.global)&&0==r.active++&&r.event.trigger("ajaxStart"),g.type=g.type.toUpperCase(),g.hasContent=!Sr.test(g.type),i=g.url.replace(mr,""),g.hasContent?g.data&&g.processData&&0===(g.contentType||"").indexOf("application/x-www-form-urlencoded")&&(g.data=g.data.replace(br,"+")):(x=g.url.slice(i.length),g.data&&(g.processData||"string"==typeof g.data)&&(i+=(Vt.test(i)?"&":"?")+g.data,delete g.data),!1===g.cache&&(i=i.replace(xr,"$1"),x=(Vt.test(i)?"&":"?")+"_="+jn.guid+++x),g.url=i+x),g.ifModified&&(r.lastModified[i]&&V.setRequestHeader("If-Modified-Since",r.lastModified[i]),r.etag[i]&&V.setRequestHeader("If-None-Match",r.etag[i])),(g.data&&g.hasContent&&!1!==g.contentType||t.contentType)&&V.setRequestHeader("Content-Type",g.contentType),V.setRequestHeader("Accept",g.dataTypes[0]&&g.accepts[g.dataTypes[0]]?g.accepts[g.dataTypes[0]]+("*"!==g.dataTypes[0]?", "+Hn+"; q=0.01":""):g.accepts["*"]),g.headers)V.setRequestHeader(v,g.headers[v]);if(g.beforeSend&&(!1===g.beforeSend.call(w,V,g)||f))return V.abort();if(Pe="abort",W.add(g.complete),V.done(g.success),V.fail(g.error),n=$n(Qt,g,t,V)){if(V.readyState=1,d&&_.trigger("ajaxSend",[V,g]),f)return V;g.async&&g.timeout>0&&(o=T.setTimeout(function(){V.abort("timeout")},g.timeout));try{f=!1,n.send(se,et)}catch(Y){if(f)throw Y;et(-1,Y)}}else et(-1,"No Transport");function et(Y,ie,St,Kt){var He,Ct,Oe,ze,Ue,Ee=ie;f||(f=!0,o&&T.clearTimeout(o),n=void 0,a=Kt||"",V.readyState=Y>0?4:0,He=Y>=200&&Y<300||304===Y,St&&(ze=function Ar(e,t,n){for(var i,a,s,o,l=e.contents,f=e.dataTypes;"*"===f[0];)f.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in l)if(l[a]&&l[a].test(i)){f.unshift(a);break}if(f[0]in n)s=f[0];else{for(a in n){if(!f[0]||e.converters[a+" "+f[0]]){s=a;break}o||(o=a)}s=s||o}if(s)return s!==f[0]&&f.unshift(s),n[s]}(g,V,St)),!He&&r.inArray("script",g.dataTypes)>-1&&r.inArray("json",g.dataTypes)<0&&(g.converters["text script"]=function(){}),ze=function kr(e,t,n,i){var a,s,o,l,f,d={},v=e.dataTypes.slice();if(v[1])for(o in e.converters)d[o.toLowerCase()]=e.converters[o];for(s=v.shift();s;)if(e.responseFields[s]&&(n[e.responseFields[s]]=t),!f&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),f=s,s=v.shift())if("*"===s)s=f;else if("*"!==f&&f!==s){if(!(o=d[f+" "+s]||d["* "+s]))for(a in d)if((l=a.split(" "))[1]===s&&(o=d[f+" "+l[0]]||d["* "+l[0]])){!0===o?o=d[a]:!0!==d[a]&&(s=l[0],v.unshift(l[1]));break}if(!0!==o)if(o&&e.throws)t=o(t);else try{t=o(t)}catch(x){return{state:"parsererror",error:o?x:"No conversion from "+f+" to "+s}}}return{state:"success",data:t}}(g,ze,V,He),He?(g.ifModified&&((Ue=V.getResponseHeader("Last-Modified"))&&(r.lastModified[i]=Ue),(Ue=V.getResponseHeader("etag"))&&(r.etag[i]=Ue)),204===Y||"HEAD"===g.type?Ee="nocontent":304===Y?Ee="notmodified":(Ee=ze.state,Ct=ze.data,He=!(Oe=ze.error))):(Oe=Ee,(Y||!Ee)&&(Ee="error",Y<0&&(Y=0))),V.status=Y,V.statusText=(ie||Ee)+"",He?G.resolveWith(w,[Ct,Ee,V]):G.rejectWith(w,[V,Ee,Oe]),V.statusCode(le),le=void 0,d&&_.trigger(He?"ajaxSuccess":"ajaxError",[V,g,He?Ct:Oe]),W.fireWith(w,[V,Ee]),d&&(_.trigger("ajaxComplete",[V,g]),--r.active||r.event.trigger("ajaxStop")))}return V},getJSON:function(e,t,n){return r.get(e,t,n,"json")},getScript:function(e,t){return r.get(e,void 0,t,"script")}}),r.each(["get","post"],function(e,t){r[t]=function(n,i,a,s){return N(i)&&(s=s||a,a=i,i=void 0),r.ajax(r.extend({url:n,type:t,dataType:s,data:i,success:a},r.isPlainObject(n)&&n))}}),r.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),r._evalUrl=function(e,t,n){return r.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(i){r.globalEval(i,t,n)}})},r.fn.extend({wrapAll:function(e){var t;return this[0]&&(N(e)&&(e=e.call(this[0])),t=r(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this},wrapInner:function(e){return N(e)?this.each(function(t){r(this).wrapInner(e.call(this,t))}):this.each(function(){var t=r(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=N(e);return this.each(function(n){r(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){r(this).replaceWith(this.childNodes)}),this}}),r.expr.pseudos.hidden=function(e){return!r.expr.pseudos.visible(e)},r.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},r.ajaxSettings.xhr=function(){try{return new T.XMLHttpRequest}catch{}};var Er={0:200,1223:204},Tt=r.ajaxSettings.xhr();F.cors=!!Tt&&"withCredentials"in Tt,F.ajax=Tt=!!Tt,r.ajaxTransport(function(e){var t,n;if(F.cors||Tt&&!e.crossDomain)return{send:function(i,a){var s,o=e.xhr();if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)o[s]=e.xhrFields[s];for(s in e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),!e.crossDomain&&!i["X-Requested-With"]&&(i["X-Requested-With"]="XMLHttpRequest"),i)o.setRequestHeader(s,i[s]);t=function(l){return function(){t&&(t=n=o.onload=o.onerror=o.onabort=o.ontimeout=o.onreadystatechange=null,"abort"===l?o.abort():"error"===l?"number"!=typeof o.status?a(0,"error"):a(o.status,o.statusText):a(Er[o.status]||o.status,o.statusText,"text"!==(o.responseType||"text")||"string"!=typeof o.responseText?{binary:o.response}:{text:o.responseText},o.getAllResponseHeaders()))}},o.onload=t(),n=o.onerror=o.ontimeout=t("error"),void 0!==o.onabort?o.onabort=n:o.onreadystatechange=function(){4===o.readyState&&T.setTimeout(function(){t&&n()})},t=t("abort");try{o.send(e.hasContent&&e.data||null)}catch(l){if(t)throw l}},abort:function(){t&&t()}}}),r.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),r.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return r.globalEval(e),e}}}),r.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),r.ajaxTransport("script",function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(i,a){t=r("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(s){t.remove(),n=null,s&&a("error"===s.type?404:200,s.type)}),p.head.appendChild(t[0])},abort:function(){n&&n()}}});var e,_n=[],Jt=/(=)\?(?=&|$)|\?\?/;r.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=_n.pop()||r.expando+"_"+jn.guid++;return this[e]=!0,e}}),r.ajaxPrefilter("json jsonp",function(e,t,n){var i,a,s,o=!1!==e.jsonp&&(Jt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Jt.test(e.data)&&"data");if(o||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=N(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,o?e[o]=e[o].replace(Jt,"$1"+i):!1!==e.jsonp&&(e.url+=(Vt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||r.error(i+" was not called"),s[0]},e.dataTypes[0]="json",a=T[i],T[i]=function(){s=arguments},n.always(function(){void 0===a?r(T).removeProp(i):T[i]=a,e[i]&&(e.jsonpCallback=t.jsonpCallback,_n.push(i)),s&&N(a)&&a(s[0]),s=a=void 0}),"script"}),F.createHTMLDocument=((e=p.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),r.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(F.createHTMLDocument?((i=(t=p.implementation.createHTMLDocument("")).createElement("base")).href=p.location.href,t.head.appendChild(i)):t=p),s=!n&&[],(a=K.exec(e))?[t.createElement(a[1])]:(a=hn([e],t,s),s&&s.length&&r(s).remove(),r.merge([],a.childNodes)));var i,a,s},r.fn.load=function(e,t,n){var i,a,s,o=this,l=e.indexOf(" ");return l>-1&&(i=Je(e.slice(l)),e=e.slice(0,l)),N(t)?(n=t,t=void 0):t&&"object"==typeof t&&(a="POST"),o.length>0&&r.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done(function(f){s=arguments,o.html(i?r("<div>").append(r.parseHTML(f)).find(i):f)}).always(n&&function(f,d){o.each(function(){n.apply(this,s||[f.responseText,d,f])})}),this},r.expr.pseudos.animated=function(e){return r.grep(r.timers,function(t){return e===t.elem}).length},r.offset={setOffset:function(e,t,n){var i,a,s,o,l,f,v=r.css(e,"position"),x=r(e),g={};"static"===v&&(e.style.position="relative"),l=x.offset(),s=r.css(e,"top"),f=r.css(e,"left"),("absolute"===v||"fixed"===v)&&(s+f).indexOf("auto")>-1?(o=(i=x.position()).top,a=i.left):(o=parseFloat(s)||0,a=parseFloat(f)||0),N(t)&&(t=t.call(e,n,r.extend({},l))),null!=t.top&&(g.top=t.top-l.top+o),null!=t.left&&(g.left=t.left-l.left+a),"using"in t?t.using.call(e,g):x.css(g)}},r.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(a){r.offset.setOffset(this,e,a)});var t,n,i=this[0];return i?i.getClientRects().length?{top:(t=i.getBoundingClientRect()).top+(n=i.ownerDocument.defaultView).pageYOffset,left:t.left+n.pageXOffset}:{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],a={top:0,left:0};if("fixed"===r.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===r.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((a=r(e).offset()).top+=r.css(e,"borderTopWidth",!0),a.left+=r.css(e,"borderLeftWidth",!0))}return{top:t.top-a.top-r.css(i,"marginTop",!0),left:t.left-a.left-r.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===r.css(e,"position");)e=e.offsetParent;return e||Ze})}}),r.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;r.fn[e]=function(i){return Me(this,function(a,s,o){var l;if(b(a)?l=a:9===a.nodeType&&(l=a.defaultView),void 0===o)return l?l[t]:a[s];l?l.scrollTo(n?l.pageXOffset:o,n?o:l.pageYOffset):a[s]=o},e,i,arguments.length)}}),r.each(["top","left"],function(e,t){r.cssHooks[t]=xn(F.pixelPosition,function(n,i){if(i)return i=mt(n,t),It.test(i)?r(n).position()[t]+"px":i})}),r.each({Height:"height",Width:"width"},function(e,t){r.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){r.fn[i]=function(a,s){var o=arguments.length&&(n||"boolean"!=typeof a),l=n||(!0===a||!0===s?"margin":"border");return Me(this,function(f,d,v){var x;return b(f)?0===i.indexOf("outer")?f["inner"+e]:f.document.documentElement["client"+e]:9===f.nodeType?(x=f.documentElement,Math.max(f.body["scroll"+e],x["scroll"+e],f.body["offset"+e],x["offset"+e],x["client"+e])):void 0===v?r.css(f,d,l):r.style(f,d,v,l)},t,o?a:void 0,o)}})}),r.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){r.fn[t]=function(n){return this.on(t,n)}}),r.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),r.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){r.fn[t]=function(n,i){return arguments.length>0?this.on(t,null,n,i):this.trigger(t)}});var Fr=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;r.proxy=function(e,t){var n,i,a;if("string"==typeof t&&(n=e[t],t=e,e=n),N(e))return i=E.call(arguments,2),a=function(){return e.apply(t||this,i.concat(E.call(arguments)))},a.guid=e.guid=e.guid||r.guid++,a},r.holdReady=function(e){e?r.readyWait++:r.ready(!0)},r.isArray=Array.isArray,r.parseJSON=JSON.parse,r.nodeName=M,r.isFunction=N,r.isWindow=b,r.camelCase=Le,r.type=A,r.now=Date.now,r.isNumeric=function(e){var t=r.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},r.trim=function(e){return null==e?"":(e+"").replace(Fr,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return r});var Dr=T.jQuery,jr=T.$;return r.noConflict=function(e){return T.$===r&&(T.$=jr),e&&T.jQuery===r&&(T.jQuery=Dr),r},typeof I>"u"&&(T.jQuery=T.$=r),r});var _self=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{},Prism=function(T){var I=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,L=0,ae={},E={manual:T.Prism&&T.Prism.manual,disableWorkerMessageHandler:T.Prism&&T.Prism.disableWorkerMessageHandler,util:{encode:function b(p){return p instanceof te?new te(p.type,b(p.content),p.alias):Array.isArray(p)?p.map(b):p.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(b){return Object.prototype.toString.call(b).slice(8,-1)},objId:function(b){return b.__id||Object.defineProperty(b,"__id",{value:++L}),b.__id},clone:function b(p,S){var k,A;switch(S=S||{},E.util.type(p)){case"Object":if(A=E.util.objId(p),S[A])return S[A];for(var H in S[A]=k={},p)p.hasOwnProperty(H)&&(k[H]=b(p[H],S));return k;case"Array":return A=E.util.objId(p),S[A]?S[A]:(S[A]=k=[],p.forEach(function(z,r){k[r]=b(z,S)}),k);default:return p}},getLanguage:function(b){for(;b;){var p=I.exec(b.className);if(p)return p[1].toLowerCase();b=b.parentElement}return"none"},setLanguage:function(b,p){b.className=b.className.replace(RegExp(I,"gi"),""),b.classList.add("language-"+p)},currentScript:function(){if(typeof document>"u")return null;if(document.currentScript&&"SCRIPT"===document.currentScript.tagName)return document.currentScript;try{throw new Error}catch(k){var b=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(k.stack)||[])[1];if(b){var p=document.getElementsByTagName("script");for(var S in p)if(p[S].src==b)return p[S]}return null}},isActive:function(b,p,S){for(var k="no-"+p;b;){var A=b.classList;if(A.contains(p))return!0;if(A.contains(k))return!1;b=b.parentElement}return!!S}},languages:{plain:ae,plaintext:ae,text:ae,txt:ae,extend:function(b,p){var S=E.util.clone(E.languages[b]);for(var k in p)S[k]=p[k];return S},insertBefore:function(b,p,S,k){var A=(k=k||E.languages)[b],H={};for(var z in A)if(A.hasOwnProperty(z)){if(z==p)for(var r in S)S.hasOwnProperty(r)&&(H[r]=S[r]);S.hasOwnProperty(z)||(H[z]=A[z])}var B=k[b];return k[b]=H,E.languages.DFS(E.languages,function(M,we){we===B&&M!=b&&(this[M]=H)}),H},DFS:function b(p,S,k,A){A=A||{};var H=E.util.objId;for(var z in p)if(p.hasOwnProperty(z)){S.call(p,z,p[z],k||z);var r=p[z],B=E.util.type(r);"Object"!==B||A[H(r)]?"Array"===B&&!A[H(r)]&&(A[H(r)]=!0,b(r,S,z,A)):(A[H(r)]=!0,b(r,S,null,A))}}},plugins:{},highlightAll:function(b,p){E.highlightAllUnder(document,b,p)},highlightAllUnder:function(b,p,S){var k={callback:S,container:b,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};E.hooks.run("before-highlightall",k),k.elements=Array.prototype.slice.apply(k.container.querySelectorAll(k.selector)),E.hooks.run("before-all-elements-highlight",k);for(var H,A=0;H=k.elements[A++];)E.highlightElement(H,!0===p,k.callback)},highlightElement:function(b,p,S){var k=E.util.getLanguage(b),A=E.languages[k];E.util.setLanguage(b,k);var H=b.parentElement;H&&"pre"===H.nodeName.toLowerCase()&&E.util.setLanguage(H,k);var r={element:b,language:k,grammar:A,code:b.textContent};function B(we){r.highlightedCode=we,E.hooks.run("before-insert",r),r.element.innerHTML=r.highlightedCode,E.hooks.run("after-highlight",r),E.hooks.run("complete",r),S&&S.call(r.element)}if(E.hooks.run("before-sanity-check",r),(H=r.element.parentElement)&&"pre"===H.nodeName.toLowerCase()&&!H.hasAttribute("tabindex")&&H.setAttribute("tabindex","0"),!r.code)return E.hooks.run("complete",r),void(S&&S.call(r.element));if(E.hooks.run("before-highlight",r),r.grammar)if(p&&T.Worker){var M=new Worker(E.filename);M.onmessage=function(we){B(we.data)},M.postMessage(JSON.stringify({language:r.language,code:r.code,immediateClose:!0}))}else B(E.highlight(r.code,r.grammar,r.language));else B(E.util.encode(r.code))},highlight:function(b,p,S){var k={code:b,grammar:p,language:S};if(E.hooks.run("before-tokenize",k),!k.grammar)throw new Error('The language "'+k.language+'" has no grammar.');return k.tokens=E.tokenize(k.code,k.grammar),E.hooks.run("after-tokenize",k),te.stringify(E.util.encode(k.tokens),k.language)},tokenize:function(b,p){var S=p.rest;if(S){for(var k in S)p[k]=S[k];delete p.rest}var A=new de;return Ce(A,A.head,b),oe(b,A,p,A.head,0),function _e(b){for(var p=[],S=b.head.next;S!==b.tail;)p.push(S.value),S=S.next;return p}(A)},hooks:{all:{},add:function(b,p){var S=E.hooks.all;S[b]=S[b]||[],S[b].push(p)},run:function(b,p){var S=E.hooks.all[b];if(S&&S.length)for(var A,k=0;A=S[k++];)A(p)}},Token:te};function te(b,p,S,k){this.type=b,this.content=p,this.alias=S,this.length=0|(k||"").length}function xe(b,p,S,k){b.lastIndex=p;var A=b.exec(S);if(A&&k&&A[1]){var H=A[1].length;A.index+=H,A[0]=A[0].slice(H)}return A}function oe(b,p,S,k,A,H){for(var z in S)if(S.hasOwnProperty(z)&&S[z]){var r=S[z];r=Array.isArray(r)?r:[r];for(var B=0;B<r.length;++B){if(H&&H.cause==z+","+B)return;var M=r[B],we=M.inside,Ve=!!M.lookbehind,Ge=!!M.greedy,Q=M.alias;if(Ge&&!M.pattern.global){var Ne=M.pattern.toString().match(/[imsuy]*$/)[0];M.pattern=RegExp(M.pattern.source,Ne+"g")}for(var nt=M.pattern||M,re=k.next,ee=A;re!==p.tail&&!(H&&ee>=H.reach);ee+=re.value.length,re=re.next){var Te=re.value;if(p.length>b.length)return;if(!(Te instanceof te)){var ue,ge=1;if(Ge){if(!(ue=xe(nt,ee,b,Ve))||ue.index>=b.length)break;var Se=ue.index,Qe=ue.index+ue[0].length,K=ee;for(K+=re.value.length;Se>=K;)K+=(re=re.next).value.length;if(ee=K-=re.value.length,re.value instanceof te)continue;for(var fe=re;fe!==p.tail&&(K<Qe||"string"==typeof fe.value);fe=fe.next)ge++,K+=fe.value.length;ge--,Te=b.slice(ee,K),ue.index-=ee}else if(!(ue=xe(nt,0,Te,Ve)))continue;var Be=ue[0],dt=Te.slice(0,Se=ue.index),kt=Te.slice(Se+Be.length),pt=ee+Te.length;H&&pt>H.reach&&(H.reach=pt);var Ye=re.prev;if(dt&&(Ye=Ce(p,Ye,dt),ee+=dt.length),he(p,Ye,ge),re=Ce(p,Ye,new te(z,we?E.tokenize(Be,we):Be,Q,Be)),kt&&Ce(p,re,kt),ge>1){var ht={cause:z+","+B,reach:pt};oe(b,p,S,re.prev,ee,ht),H&&ht.reach>H.reach&&(H.reach=ht.reach)}}}}}}function de(){var b={value:null,prev:null,next:null},p={value:null,prev:b,next:null};b.next=p,this.head=b,this.tail=p,this.length=0}function Ce(b,p,S){var k=p.next,A={value:S,prev:p,next:k};return p.next=A,k.prev=A,b.length++,A}function he(b,p,S){for(var k=p.next,A=0;A<S&&k!==b.tail;A++)k=k.next;p.next=k,k.prev=p,b.length-=A}if(T.Prism=E,te.stringify=function b(p,S){if("string"==typeof p)return p;if(Array.isArray(p)){var k="";return p.forEach(function(B){k+=b(B,S)}),k}var A={type:p.type,content:b(p.content,S),tag:"span",classes:["token",p.type],attributes:{},language:S},H=p.alias;H&&(Array.isArray(H)?Array.prototype.push.apply(A.classes,H):A.classes.push(H)),E.hooks.run("wrap",A);var z="";for(var r in A.attributes)z+=" "+r+'="'+(A.attributes[r]||"").replace(/"/g,"&quot;")+'"';return"<"+A.tag+' class="'+A.classes.join(" ")+'"'+z+">"+A.content+"</"+A.tag+">"},!T.document)return T.addEventListener&&(E.disableWorkerMessageHandler||T.addEventListener("message",function(b){var p=JSON.parse(b.data),S=p.language,A=p.immediateClose;T.postMessage(E.highlight(p.code,E.languages[S],S)),A&&T.close()},!1)),E;var ne=E.util.currentScript();function F(){E.manual||E.highlightAll()}if(ne&&(E.filename=ne.src,ne.hasAttribute("data-manual")&&(E.manual=!0)),!E.manual){var N=document.readyState;"loading"===N||"interactive"===N&&ne&&ne.defer?document.addEventListener("DOMContentLoaded",F):window.requestAnimationFrame?window.requestAnimationFrame(F):window.setTimeout(F,16)}return E}(_self);typeof module<"u"&&module.exports&&(module.exports=Prism),typeof global<"u"&&(global.Prism=Prism),Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",function(T){"entity"===T.type&&(T.attributes.title=T.content.replace(/&amp;/,"&"))}),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(I,L){var ae={};ae["language-"+L]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[L]},ae.cdata=/^<!\[CDATA\[|\]\]>$/i;var E={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:ae}};E["language-"+L]={pattern:/[\s\S]+/,inside:Prism.languages[L]};var te={};te[I]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return I}),"i"),lookbehind:!0,greedy:!0,inside:E},Prism.languages.insertBefore("markup","cdata",te)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(T,I){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+T+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[I,"language-"+I],inside:Prism.languages[I]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,function(T){var I=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;T.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+I.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+I.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+I.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+I.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:I,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},T.languages.css.atrule.inside.rest=T.languages.css;var L=T.languages.markup;L&&(L.tag.addInlined("style","css"),L.tag.addAttribute("style","css"))}(Prism),Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript,function(){if(!(typeof Prism>"u"||typeof document>"u")){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var ae={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},E="data-src-status",te="loading",de="pre[data-src]:not(["+E+'="loaded"]):not(['+E+'="'+te+'"])';Prism.hooks.add("before-highlightall",function(ne){ne.selector+=", "+de}),Prism.hooks.add("before-sanity-check",function(ne){var F=ne.element;if(F.matches(de)){ne.code="",F.setAttribute(E,te);var N=F.appendChild(document.createElement("CODE"));N.textContent="Loading\u2026";var b=F.getAttribute("data-src"),p=ne.language;if("none"===p){var S=(/\.(\w+)$/.exec(b)||[,"none"])[1];p=ae[S]||S}Prism.util.setLanguage(N,p),Prism.util.setLanguage(F,p);var k=Prism.plugins.autoloader;k&&k.loadLanguages(p),function Ce(ne,F,N){var b=new XMLHttpRequest;b.open("GET",ne,!0),b.onreadystatechange=function(){4==b.readyState&&(b.status<400&&b.responseText?F(b.responseText):N(b.status>=400?function(ne,F){return"\u2716 Error "+ne+" while fetching file: "+F}(b.status,b.statusText):"\u2716 Error: File does not exist or is empty"))},b.send(null)}(b,function(A){F.setAttribute(E,"loaded");var H=function he(ne){var F=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(ne||"");if(F){var N=Number(F[1]),p=F[3];return F[2]?p?[N,Number(p)]:[N,void 0]:[N,N]}}(F.getAttribute("data-range"));if(H){var z=A.split(/\r\n?|\n/g),r=H[0],B=null==H[1]?z.length:H[1];r<0&&(r+=z.length),r=Math.max(0,Math.min(r-1,z.length)),B<0&&(B+=z.length),B=Math.max(0,Math.min(B,z.length)),A=z.slice(r,B).join("\n"),F.hasAttribute("data-start")||F.setAttribute("data-start",String(r+1))}N.textContent=A,Prism.highlightElement(N)},function(A){F.setAttribute(E,"failed"),N.textContent=A})}}),Prism.plugins.fileHighlight={highlight:function(F){for(var p,N=(F||document).querySelectorAll(de),b=0;p=N[b++];)Prism.highlightElement(p)}};var _e=!1;Prism.fileHighlight=function(){_e||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),_e=!0),Prism.plugins.fileHighlight.highlight.apply(this,arguments)}}}(),function(T){function I(K,fe){return K.replace(/<<(\d+)>>/g,function(Se,Be){return"(?:"+fe[+Be]+")"})}function L(K,fe,Se){return RegExp(I(K,fe),Se||"")}function ae(K,fe){for(var Se=0;Se<fe;Se++)K=K.replace(/<<self>>/g,function(){return"(?:"+K+")"});return K.replace(/<<self>>/g,"[^\\s\\S]")}var E="bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void",te="class enum interface record struct",xe="add alias and ascending async await by descending from(?=\\s*(?:\\w|$)) get global group into init(?=\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\s*{)",oe="abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield";function de(K){return"\\b(?:"+K.trim().replace(/ /g,"|")+")\\b"}var Ce=de(te),he=RegExp(de(E+" "+te+" "+xe+" "+oe)),_e=de(te+" "+xe+" "+oe),ne=de(E+" "+te+" "+oe),F=ae("<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>",2),N=ae("\\((?:[^()]|<<self>>)*\\)",2),b="@?\\b[A-Za-z_]\\w*\\b",p=I("<<0>>(?:\\s*<<1>>)?",[b,F]),S=I("(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*",[_e,p]),k="\\[\\s*(?:,\\s*)*\\]",A=I("<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?",[S,k]),H=I("[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>",[F,N,k]),z=I("\\(<<0>>+(?:,<<0>>+)+\\)",[H]),r=I("(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?",[z,S,k]),B={keyword:he,punctuation:/[<>()?,.:[\]]/},M="'(?:[^\r\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'",we='"(?:\\\\.|[^\\\\"\r\n])*"';T.languages.csharp=T.languages.extend("clike",{string:[{pattern:L("(^|[^$\\\\])<<0>>",['@"(?:""|\\\\[^]|[^\\\\"])*"(?!")']),lookbehind:!0,greedy:!0},{pattern:L("(^|[^@$\\\\])<<0>>",[we]),lookbehind:!0,greedy:!0}],"class-name":[{pattern:L("(\\busing\\s+static\\s+)<<0>>(?=\\s*;)",[S]),lookbehind:!0,inside:B},{pattern:L("(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)",[b,r]),lookbehind:!0,inside:B},{pattern:L("(\\busing\\s+)<<0>>(?=\\s*=)",[b]),lookbehind:!0},{pattern:L("(\\b<<0>>\\s+)<<1>>",[Ce,p]),lookbehind:!0,inside:B},{pattern:L("(\\bcatch\\s*\\(\\s*)<<0>>",[S]),lookbehind:!0,inside:B},{pattern:L("(\\bwhere\\s+)<<0>>",[b]),lookbehind:!0},{pattern:L("(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>",[A]),lookbehind:!0,inside:B},{pattern:L("\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))",[r,ne,b]),inside:B}],keyword:he,number:/(?:\b0(?:x[\da-f_]*[\da-f]|b[01_]*[01])|(?:\B\.\d+(?:_+\d+)*|\b\d+(?:_+\d+)*(?:\.\d+(?:_+\d+)*)?)(?:e[-+]?\d+(?:_+\d+)*)?)(?:[dflmu]|lu|ul)?\b/i,operator:/>>=?|<<=?|[-=]>|([-+&|])\1|~|\?\?=?|[-+*/%&|^!=<>]=?/,punctuation:/\?\.?|::|[{}[\];(),.:]/}),T.languages.insertBefore("csharp","number",{range:{pattern:/\.\./,alias:"operator"}}),T.languages.insertBefore("csharp","punctuation",{"named-parameter":{pattern:L("([(,]\\s*)<<0>>(?=\\s*:)",[b]),lookbehind:!0,alias:"punctuation"}}),T.languages.insertBefore("csharp","class-name",{namespace:{pattern:L("(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])",[b]),lookbehind:!0,inside:{punctuation:/\./}},"type-expression":{pattern:L("(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))",[N]),lookbehind:!0,alias:"class-name",inside:B},"return-type":{pattern:L("<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))",[r,S]),inside:B,alias:"class-name"},"constructor-invocation":{pattern:L("(\\bnew\\s+)<<0>>(?=\\s*[[({])",[r]),lookbehind:!0,inside:B,alias:"class-name"},"generic-method":{pattern:L("<<0>>\\s*<<1>>(?=\\s*\\()",[b,F]),inside:{function:L("^<<0>>",[b]),generic:{pattern:RegExp(F),alias:"class-name",inside:B}}},"type-list":{pattern:L("\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))",[Ce,p,b,r,he.source,N,"\\bnew\\s*\\(\\s*\\)"]),lookbehind:!0,inside:{"record-arguments":{pattern:L("(^(?!new\\s*\\()<<0>>\\s*)<<1>>",[p,N]),lookbehind:!0,greedy:!0,inside:T.languages.csharp},keyword:he,"class-name":{pattern:RegExp(r),greedy:!0,inside:B},punctuation:/[,()]/}},preprocessor:{pattern:/(^[\t ]*)#.*/m,lookbehind:!0,alias:"property",inside:{directive:{pattern:/(#)\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\b/,lookbehind:!0,alias:"keyword"}}}});var Ve=we+"|"+M,Ge=I("/(?![*/])|//[^\r\n]*[\r\n]|/\\*(?:[^*]|\\*(?!/))*\\*/|<<0>>",[Ve]),Q=ae(I("[^\"'/()]|<<0>>|\\(<<self>>*\\)",[Ge]),2),Ne="\\b(?:assembly|event|field|method|module|param|property|return|type)\\b",nt=I("<<0>>(?:\\s*\\(<<1>>*\\))?",[S,Q]);T.languages.insertBefore("csharp","class-name",{attribute:{pattern:L("((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])",[Ne,nt]),lookbehind:!0,greedy:!0,inside:{target:{pattern:L("^<<0>>(?=\\s*:)",[Ne]),alias:"keyword"},"attribute-arguments":{pattern:L("\\(<<0>>*\\)",[Q]),inside:T.languages.csharp},"class-name":{pattern:RegExp(S),inside:{punctuation:/\./}},punctuation:/[:,]/}}});var re=":[^}\r\n]+",ee=ae(I("[^\"'/()]|<<0>>|\\(<<self>>*\\)",[Ge]),2),Te=I("\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}",[ee,re]),ge=ae(I("[^\"'/()]|/(?!\\*)|/\\*(?:[^*]|\\*(?!/))*\\*/|<<0>>|\\(<<self>>*\\)",[Ve]),2),ue=I("\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}",[ge,re]);function Qe(K,fe){return{interpolation:{pattern:L("((?:^|[^{])(?:\\{\\{)*)<<0>>",[K]),lookbehind:!0,inside:{"format-string":{pattern:L("(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)",[fe,re]),lookbehind:!0,inside:{punctuation:/^:/}},punctuation:/^\{|\}$/,expression:{pattern:/[\s\S]+/,alias:"language-csharp",inside:T.languages.csharp}}},string:/[\s\S]+/}}T.languages.insertBefore("csharp","string",{"interpolation-string":[{pattern:L('(^|[^\\\\])(?:\\$@|@\\$)"(?:""|\\\\[^]|\\{\\{|<<0>>|[^\\\\{"])*"',[Te]),lookbehind:!0,greedy:!0,inside:Qe(Te,ee)},{pattern:L('(^|[^@\\\\])\\$"(?:\\\\.|\\{\\{|<<0>>|[^\\\\"{])*"',[ue]),lookbehind:!0,greedy:!0,inside:Qe(ue,ge)}],char:{pattern:RegExp(M),greedy:!0}}),T.languages.dotnet=T.languages.cs=T.languages.csharp}(Prism),function(T){var I=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;T.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:[^;{\\s\"']|\\s+(?!\\s)|"+I.source+")*?(?:;|(?=\\s*\\{))"),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+I.source+"|(?:[^\\\\\r\n()\"']|\\\\[^])*)\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+I.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+I.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:I,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},T.languages.css.atrule.inside.rest=T.languages.css;var L=T.languages.markup;L&&(L.tag.addInlined("style","css"),L.tag.addAttribute("style","css"))}(Prism);