package cn.huolala.arch.hermes.mesh.panel.handler;

import cn.huolala.arch.hermes.mesh.common.model.Tag;
import cn.huolala.arch.hermes.mesh.panel.entity.IssuesFunction;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class IssuesFunctionTagsHandler extends JacksonTypeHandler {

    public IssuesFunctionTagsHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<IssuesFunction.Tag>>() {});
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
