package cn.huolala.arch.hermes.mesh.panel.annotation;


import cn.huolala.arch.hermes.mesh.panel.enums.Env;

import java.lang.annotation.*;

@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Permission {

    /**
     * 权限名称
     */
    String value();

    /**
     * 限制环境列表
     */
    Env[] envs() default {Env.PRD, Env.PRE, Env.STG};

    /**
     * 是否关联发布窗口
     */
    boolean window() default true;
}
