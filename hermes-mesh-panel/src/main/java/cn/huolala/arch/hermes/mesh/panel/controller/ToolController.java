package cn.huolala.arch.hermes.mesh.panel.controller;

import cn.huolala.arch.hermes.mesh.api.RpcAppService;
import cn.huolala.arch.hermes.mesh.api.RpcDebugService;
import cn.huolala.arch.hermes.mesh.api.RpcGovernanceService;
import cn.huolala.arch.hermes.mesh.api.RpcInspectionService;
import cn.huolala.arch.hermes.mesh.api.bos.HttpRequestBo;
import cn.huolala.arch.hermes.mesh.api.bos.HttpResponseBo;
import cn.huolala.arch.hermes.mesh.api.bos.SoaRequestBo;
import cn.huolala.arch.hermes.mesh.api.bos.SoaResponseBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppGrayConditionBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiTimeoutCommandKeyBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.AppZoneRouterConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.AppZoneRouterLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.timeout.MultilevelTimeoutBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.InspectionReportBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.InspectionRuleBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.ScriptParamBo;
import cn.huolala.arch.hermes.mesh.common.GlobalConstants;
import cn.huolala.arch.hermes.mesh.common.el.function.Describe;
import cn.huolala.arch.hermes.mesh.common.el.function.Function;
import cn.huolala.arch.hermes.mesh.common.enums.ApiType;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.model.Pair;
import cn.huolala.arch.hermes.mesh.common.model.ParamContext;
import cn.huolala.arch.hermes.mesh.panel.annotation.*;
import cn.huolala.arch.hermes.mesh.panel.entity.*;
import cn.huolala.arch.hermes.mesh.panel.service.ToolService;
import cn.huolala.arch.hermes.mesh.panel.vos.tool.FolderVo;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("tool/")
@ControllerPermission(value = "工具中心")
public class ToolController {

    @Resource
    private ToolService toolService;

    @Resource
    private RpcAppService rpcAppService;

    @Resource
    private RpcDebugService rpcDebugService;

    @Resource
    private RpcInspectionService rpcInspectionService;

    @Resource
    private RpcGovernanceService rpcGovernanceService;

    /**
     * 缓存
     */
    private static final TimedCache<String, List<MultilevelTimeoutBo>> TIMEOUT_CACHE = CacheUtil.newTimedCache(1000 * 60 * 5);


    @GetMapping("mvc/folder")
    public RespMsg<List<MvcFolder>> mvcFolder(@UserId String userId) {
        return RespMsg.respSuc(toolService.mvcFolder(userId));
    }

    @PostMapping("mvc/folder")
    @Permission(value = "MVC调试目录管理")
    public RespMsg<Void> postMvcFolder(@UserId String userId, @RequestBody MvcFolder mvcFolder) {
        mvcFolder.setUserId(userId);
        toolService.postMvcFolder(mvcFolder);
        return RespMsg.respSuc();
    }

    @PutMapping("mvc/folder")
    @Permission(value = "MVC调试目录管理")
    public RespMsg<Void> putMvcFolder(@RequestBody MvcFolder mvcFolder) {
        toolService.putMvcFolder(mvcFolder);
        return RespMsg.respSuc();
    }

    @DeleteMapping("mvc/folder")
    @Permission(value = "MVC调试目录管理")
    public RespMsg<Void> deleteMvcFolder(@RequestParam Long id) {
        toolService.deleteMvcFolder(id);
        return RespMsg.respSuc();
    }

    @GetMapping("mvc/folders")
    public RespMsg<List<FolderVo>> mvcFolders(@UserId String userId) {
        return RespMsg.respSuc(toolService.mvcFolders(userId));
    }

    @RpcMapping
    @PostMapping("mvc/debug")
    @Permission(value = "MVC调试")
    public RespMsg<HttpResponseBo> mvcDebug(@UserId String userId, @AreaEnvId Long envId, @RequestBody MvcRequest mvcRequest) {
        List<Pair> contextParams = toolService.requestParams(userId, envId).stream().map(requestParam -> Pair.builder().name(requestParam.getName()).value(requestParam.getValue()).build()).toList();
        HttpRequestBo request = mvcRequest.getRequest();
        request.setContext(contextParams);
        RespMsg<HttpResponseBo> respMsg = rpcDebugService.mvcDebug(request);
        request.setContext(List.of());
        toolService.postMvcRequestLog(userId, envId, request, respMsg.getData());
        return respMsg;
    }

    @GetMapping("mvc/request")
    public RespMsg<MvcRequest> getMvcRequest(@RequestParam Long id) {
        return RespMsg.respSuc(toolService.getMvcRequest(id));
    }

    @PostMapping("mvc/request")
    public RespMsg<MvcRequest> postMvcRequest(@UserId String userId, @RequestBody MvcRequest mvcRequest) {
        toolService.postMvcRequest(userId, mvcRequest);
        return RespMsg.respSuc(mvcRequest);
    }

    @DeleteMapping("mvc/request")
    @Permission(value = "MVC调试管理")
    public RespMsg<MvcRequest> deleteMvcRequest(@RequestParam Long id) {
        toolService.deleteMvcRequest(id);
        return RespMsg.respSuc();
    }

    @GetMapping("mvc/request/logs")
    public RespMsg<Page<MvcRequestLog>> mvcRequestLogs(@UserId String userId, @AreaEnvId Long areaEnvId, @RequestParam int num, @RequestParam int size) {
        return RespMsg.respSuc(toolService.mvcRequestLogs(userId, areaEnvId, num, size));
    }

    @GetMapping("soa/folder")
    public RespMsg<List<SoaFolder>> soaFolder(@UserId String userId) {
        return RespMsg.respSuc(toolService.soaFolder(userId));
    }


    @PostMapping("soa/folder")
    @Permission(value = "SOA调试目录管理")
    public RespMsg<Void> postSoaFolder(@UserId String userId, @RequestBody SoaFolder soaFolder) {
        soaFolder.setUserId(userId);
        toolService.postSoaFolder(soaFolder);
        return RespMsg.respSuc();
    }


    @PutMapping("soa/folder")
    @Permission(value = "SOA调试目录管理")
    public RespMsg<Void> putSoaFolder(@RequestBody SoaFolder soaFolder) {
        toolService.putSoaFolder(soaFolder);
        return RespMsg.respSuc();
    }

    @DeleteMapping("soa/folder")
    @Permission(value = "SOA调试目录管理")
    public RespMsg<Void> deleteSoaFolder(@RequestParam Long id) {
        toolService.deleteSoaFolder(id);
        return RespMsg.respSuc();
    }

    @GetMapping("soa/folders")
    public RespMsg<List<FolderVo>> soaFolders(@UserId String userId) {
        return RespMsg.respSuc(toolService.soaFolders(userId));
    }

    @RpcMapping
    @PostMapping("soa/debug")
    @Permission(value = "SOA调试")
    public RespMsg<SoaResponseBo> soaDebug(@UserId String userId, @AreaEnvId Long envId, @RequestBody SoaRequest soaRequest) {
        List<Pair> contextParams = toolService.requestParams(userId, envId).stream().map(requestParam -> Pair.builder().name(requestParam.getName()).value(requestParam.getValue()).build()).toList();
        SoaRequestBo request = soaRequest.getRequest();
        request.setContext(contextParams);
        RespMsg<SoaResponseBo> respMsg = rpcDebugService.soaDebug(request);
        request.setContext(List.of());
        toolService.postSoaRequestLog(userId, envId, soaRequest, respMsg.getData());
        return respMsg;
    }

    @GetMapping("soa/request")
    public RespMsg<SoaRequest> getSoaRequest(@RequestParam Long id) {
        return RespMsg.respSuc(toolService.getSoaRequest(id));
    }

    @PostMapping("soa/request")
    public RespMsg<SoaRequest> postSoaRequest(@UserId String userId, @RequestBody SoaRequest soaRequest) {
        toolService.postSoaRequest(userId, soaRequest);
        return RespMsg.respSuc(soaRequest);
    }

    @DeleteMapping("soa/request")
    @Permission(value = "SOA调试管理")
    public RespMsg<MvcRequest> deleteSoaRequest(@RequestParam Long id) {
        toolService.deleteSoaRequest(id);
        return RespMsg.respSuc();
    }

    @GetMapping("soa/request/logs")
    public RespMsg<Page<SoaRequestLog>> soaRequestLogs(@UserId String userId, @AreaEnvId Long areaEnvId, @RequestParam int num, @RequestParam int size) {
        return RespMsg.respSuc(toolService.soaRequestLogs(userId, areaEnvId, num, size));
    }

    @GetMapping("soa/app/request/logs")
    public RespMsg<Page<SoaRequestLog>> soaAppRequestLogs(@AppId String appId, @AreaEnvId Long areaEnvId, @RequestParam(required = false) String name, @RequestParam int num, @RequestParam int size) {
        return RespMsg.respSuc(toolService.soaAppRequestLogs(appId, name, areaEnvId, num, size));
    }


    @GetMapping("function/describes")
    public RespMsg<List<Describe>> functions() {
        return RespMsg.respSuc(Function.functionDescribes());
    }


    @GetMapping("function/invoke")
    public RespMsg<Object> functionInvoke(@NonNull String function) {
        try {
            return RespMsg.respSuc(Function.invoke(function));
        } catch (Exception e) {
            return RespMsg.respErr(e.getMessage());
        }
    }

    @GetMapping("request/params")
    public RespMsg<List<cn.huolala.arch.hermes.mesh.panel.entity.RequestParam>> requestParams(@UserId String userId, @AreaEnvId Long areaEnvId) {
        return RespMsg.respSuc(toolService.requestParams(userId, areaEnvId));
    }

    @PostMapping("request/param")
    public RespMsg<Void> postRequestParam(@UserId String userId, @AreaEnvId Long areaEnvId, @RequestBody cn.huolala.arch.hermes.mesh.panel.entity.RequestParam requestParam) {
        requestParam.setUserId(userId);
        requestParam.setAreaEnvId(areaEnvId);
        toolService.postRequestParam(requestParam);
        return RespMsg.respSuc();
    }

    @PutMapping("request/param")
    public RespMsg<Void> putRequestParam(@RequestBody cn.huolala.arch.hermes.mesh.panel.entity.RequestParam requestParam) {
        toolService.putRequestParam(requestParam);
        return RespMsg.respSuc();
    }

    @DeleteMapping("request/param")
    public RespMsg<Void> deleteRequestParam(@RequestParam Long id) {
        toolService.deleteRequestParam(id);
        return RespMsg.respSuc();
    }

    @RpcMapping
    @GetMapping("gary/condition")
    public RespMsg<AppGrayConditionBo> grayCondition(@RequestParam String appId) {
        return rpcAppService.appGrayCondition(appId);
    }

    @RpcMapping
    @GetMapping("zone/routerConfig")
    public RespMsg<AppZoneRouterConfigBo> appAZRouterConfig(@AppId String appId) {
        return rpcGovernanceService.appZoneRouterConfig(appId);
    }

    @RpcMapping
    @PostMapping("zone/routerConfig")
    @Permission(value = "泳道路由规则配置")
    public RespMsg<Void> appAZRouterConfig(@AppId String appId, @UserId String userId, @RequestBody AppZoneRouterConfigBo appZoneRouterConfig) {
        return rpcGovernanceService.appZoneRouterConfig(appId, userId, appZoneRouterConfig);
    }

    @RpcMapping
    @GetMapping("zone/routerLog")
    public RespMsg<Page<AppZoneRouterLogBo>> appZoneRouterLog(@AppId String appId, @RequestParam int num, @RequestParam int size) {
        return rpcGovernanceService.appZoneRouterLog(appId, num, size);
    }


    /*超时组手*/
    @RpcMapping
    @GetMapping("timeout/config")
    public RespMsg<List<ApiTimeoutCommandKeyBo>> appTimeoutConfigFlat(@AppId String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        return rpcGovernanceService.appTimeoutConfigFlat(appId, ApiType.CONSUMER, cluster);
    }


    @GetMapping("timeout/download")
    public void download(HttpServletResponse response, @RequestParam String fileName) throws IOException {
        List<MultilevelTimeoutBo> timeoutBos = TIMEOUT_CACHE.get(fileName);
        if (CollUtil.isEmpty(timeoutBos)) {
            throw new IllegalArgumentException(StrUtil.format("无法找到文件下载,请重新尝试上传"));
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
        EasyExcel.write(response.getOutputStream(), MultilevelTimeoutBo.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet().doWrite(timeoutBos);
        TIMEOUT_CACHE.remove(fileName);
    }


    @RpcMapping
    @PostMapping("timeout/file")
    public RespMsg<String> appTimeoutFileComplete(@RequestParam MultipartFile file, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        List<MultilevelTimeoutBo> timeoutBos = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), MultilevelTimeoutBo.class, new ReadListener<MultilevelTimeoutBo>() {
                @Override
                public void invoke(MultilevelTimeoutBo timeoutBo, AnalysisContext context) {
                    timeoutBos.add(timeoutBo);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {

                }
            }).sheet().doRead();
        } catch (IOException e) {
            RespMsg.respErrF("读取文件流失败:{}", e.getMessage());
        }
        RespMsg<List<MultilevelTimeoutBo>> respMsg = rpcGovernanceService.multilevelTimeoutComplete(timeoutBos,ApiType.CONSUMER,cluster);
        if (respMsg.isSuccess()) {
            TIMEOUT_CACHE.put(file.getOriginalFilename(), respMsg.getData());
            return RespMsg.respDataSuc(file.getOriginalFilename());
        }
        return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
    }


    @RpcMapping
    @GetMapping("inspection/report")
    public RespMsg<InspectionReportBo> inspectionReport(@RequestParam InspectionScene scene, @RequestParam Long id) {
        return rpcInspectionService.inspectionReport(scene, id);
    }


    @RpcMapping
    @PostMapping("inspection/execute")
    public RespMsg<InspectionReportBo> inspectionExecute(@RequestParam InspectionScene scene) {
        return rpcInspectionService.executeInspection(scene, ParamContext.of());
    }


    @RpcMapping
    @GetMapping("inspection/rules")
    public RespMsg<List<InspectionRuleBo>> inspectionRules(@RequestParam InspectionScene scene) {
        return rpcInspectionService.inspectionRules(scene);
    }

    @RpcMapping
    @PostMapping("inspection/rule")
    public RespMsg<InspectionRuleBo> inspectionRule(@RequestBody InspectionRuleBo ruleBo) {
        return rpcInspectionService.updateInspectionRule(ruleBo);
    }

    @RpcMapping
    @DeleteMapping("inspection/rule")
    @Permission(value = "删除自检规则")
    public RespMsg<Void> deleteInspectionRule(@RequestParam Long id) {
        rpcInspectionService.deleteInspectionRule(id);
        return RespMsg.respSuc();
    }


    @RpcMapping
    @PostMapping("inspection/rule/execute")
    public RespMsg<MeterReportBo> executeInspectionRule(@RequestBody InspectionRuleBo ruleBo) {
        return rpcInspectionService.executeInspectionRule(ruleBo, ParamContext.of());
    }


    @RpcMapping
    @GetMapping("inspection/report/history")
    public RespMsg<Page<InspectionReportBo>> inspectionReportHistory(@RequestParam InspectionScene scene, @RequestParam int num, @RequestParam int size) {
        return rpcInspectionService.inspectionReportHistory(num, size, scene);
    }


    @RpcMapping
    @GetMapping("inspection/script/params")
    public RespMsg<List<ScriptParamBo>> inspectionScriptParams() {
        return RespMsg.respSuc(rpcInspectionService.scriptParams());
    }

    @RpcMapping
    @PostMapping("inspection/script/param")
    public RespMsg<Void> postScriptParam(@RequestBody ScriptParamBo scriptParamBo) {
        rpcInspectionService.postScriptParam(scriptParamBo);
        return RespMsg.respSuc();
    }

    @RpcMapping
    @PutMapping("inspection/script/param")
    public RespMsg<Void> putScriptParam(@RequestBody ScriptParamBo scriptParamBo) {
        rpcInspectionService.putScriptParam(scriptParamBo);
        return RespMsg.respSuc();
    }

    @RpcMapping
    @DeleteMapping("inspection/script/param")
    public RespMsg<Void> deleteScriptParam(@RequestParam Long id) {
        rpcInspectionService.deleteScriptParam(id);
        return RespMsg.respSuc();
    }


}
