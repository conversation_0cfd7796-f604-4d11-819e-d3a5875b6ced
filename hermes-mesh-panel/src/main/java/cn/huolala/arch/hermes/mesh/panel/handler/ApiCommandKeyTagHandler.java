package cn.huolala.arch.hermes.mesh.panel.handler;

import cn.huolala.arch.hermes.mesh.panel.entity.ApiCommandKeyTag;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class ApiCommandKeyTagHandler extends JacksonTypeHandler {

    public ApiCommandKeyTagHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<ApiCommandKeyTag>>() {});
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
