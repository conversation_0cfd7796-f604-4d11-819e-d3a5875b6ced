package cn.huolala.arch.hermes.mesh.panel.handler;

import cn.huolala.arch.hermes.mesh.panel.operation.model.AppVisitorModel;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class AppVisitorModelListHandler extends JacksonTypeHandler {

    public AppVisitorModelListHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<AppVisitorModel>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
