package cn.huolala.arch.hermes.mesh.panel.handler;

import cn.huolala.arch.hermes.mesh.panel.entity.PlatformMessageNotice;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import java.io.IOException;

public class PlatformMessageDataHandler extends JacksonTypeHandler {
    public PlatformMessageDataHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, PlatformMessageNotice.MessageData.class);
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}


