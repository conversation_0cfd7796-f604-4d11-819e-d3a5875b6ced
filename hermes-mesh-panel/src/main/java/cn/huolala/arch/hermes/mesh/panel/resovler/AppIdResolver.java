package cn.huolala.arch.hermes.mesh.panel.resovler;

import cn.huolala.arch.hermes.mesh.panel.annotation.AppId;
import cn.huolala.arch.hermes.mesh.panel.utils.HttpServletUtil;
import cn.hutool.core.lang.Assert;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;


public class AppIdResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(AppId.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest servletRequest = (HttpServletRequest)webRequest.getNativeRequest();
        String appId = HttpServletUtil.getAppId(servletRequest);
        Assert.notNull(appId,"appId must be not empty");
        return appId;
    }


}
