package cn.huolala.arch.hermes.mesh.panel.controller;

import cn.huolala.arch.hermes.mesh.api.*;
import cn.huolala.arch.hermes.mesh.api.bos.HttpRequestBo;
import cn.huolala.arch.hermes.mesh.api.bos.HttpResponseBo;
import cn.huolala.arch.hermes.mesh.api.bos.SoaRequestBo;
import cn.huolala.arch.hermes.mesh.api.bos.SoaResponseBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppGroupInfoBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppSoaInfo;
import cn.huolala.arch.hermes.mesh.api.bos.app.InstanceBo;
import cn.huolala.arch.hermes.mesh.api.bos.cipher.CipherTextBo;
import cn.huolala.arch.hermes.mesh.api.bos.config.AccessKeyBo;
import cn.huolala.arch.hermes.mesh.api.bos.config.EnvClusterInfoBo;
import cn.huolala.arch.hermes.mesh.api.bos.config.ItemDBo;
import cn.huolala.arch.hermes.mesh.api.bos.config.NamespaceBo;
import cn.huolala.arch.hermes.mesh.api.bos.event.ApiCircuitEventLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.*;
import cn.huolala.arch.hermes.mesh.api.bos.governance.degrade.ConsumerDegradeBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.InspectionReportBo;
import cn.huolala.arch.hermes.mesh.api.bos.metadata.ApiDefinitionMetaBo;
import cn.huolala.arch.hermes.mesh.api.bos.metric.LabelTimeValueBo;
import cn.huolala.arch.hermes.mesh.api.bos.open.ComponentStatusBo;
import cn.huolala.arch.hermes.mesh.api.bos.registry.ServiceHealthBo;
import cn.huolala.arch.hermes.mesh.api.bos.resource.DetectionBo;
import cn.huolala.arch.hermes.mesh.common.GlobalConstants;
import cn.huolala.arch.hermes.mesh.common.enums.*;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.model.Pair;
import cn.huolala.arch.hermes.mesh.common.model.ParamContext;
import cn.huolala.arch.hermes.mesh.common.utils.*;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.AppBaseInfo;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.component.lone.model.IpInfo;
import cn.huolala.arch.hermes.mesh.component.lone.model.ReleaseWindow;
import cn.huolala.arch.hermes.mesh.component.lone.service.LoneFacadeService;
import cn.huolala.arch.hermes.mesh.component.open.monitor.*;
import cn.huolala.arch.hermes.mesh.component.open.monitor.log.*;
import cn.huolala.arch.hermes.mesh.open.model.config.AppClusterInfoResponse;
import cn.huolala.arch.hermes.mesh.open.model.enums.AppStatus;
import cn.huolala.arch.hermes.mesh.open.model.request.*;
import cn.huolala.arch.hermes.mesh.open.model.response.*;
import cn.huolala.arch.hermes.mesh.panel.annotation.*;
import cn.huolala.arch.hermes.mesh.panel.entity.AreaEnv;
import cn.huolala.arch.hermes.mesh.panel.entity.MvcRequest;
import cn.huolala.arch.hermes.mesh.panel.entity.SoaRequest;
import cn.huolala.arch.hermes.mesh.panel.enums.Env;
import cn.huolala.arch.hermes.mesh.panel.service.ApiMetadataService;
import cn.huolala.arch.hermes.mesh.panel.service.PermissionService;
import cn.huolala.arch.hermes.mesh.panel.service.ToolService;
import cn.huolala.arch.hermes.mesh.panel.service.WebHookService;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.sink.common.util.SinkCommandKeyUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.mesh.common.GlobalConstants.DEFAULT_CLUSTER;
import static cn.huolala.arch.hermes.mesh.common.GlobalConstants.SOA_RELEASE_VERSION;
import static cn.huolala.arch.hermes.mesh.common.utils.CommandUtils.buildInstanceId;
import static cn.huolala.arch.hermes.mesh.common.utils.DateUtils.PATTERN_HM;
import static cn.huolala.arch.hermes.mesh.panel.enums.WebHookType.*;
import static cn.hutool.core.lang.Opt.ofNullable;


@Slf4j
@RestController
@RequestMapping("open/api/{appId}/{envId}/")
@ControllerPermission(value = "开放平台")
public class OpenApiController {

    private static final String SUCCESS = "success";

    private static final String FAIL = "fail";

    @Resource
    private RpcAppService rpcAppService;

    @Resource
    private RpcConfigService rpcConfigService;

    @Resource
    private RpcCipherService rpcCipherService;

    @Resource
    private RpcMetadataService rpcMetadataService;

    @Resource
    private RpcGovernanceService rpcGovernanceService;

    @Resource
    private RpcComponentService rpcComponentService;

    @Resource
    private RpcRegistryService rpcRegistryService;

    @Resource
    private RpcMetricService rpcMetricService;

    @Resource
    private RpcInspectionService rpcInspectionService;

    @Resource
    private WebHookService webHookService;

    @Resource
    private ApiMetadataService apiMetadataService;

    @Resource
    private RpcOpenapiMonitorService rpcOpenapiMonitorService;

    @Resource
    private LoneFacadeService loneFacadeService;

    @Resource
    private CmdbFacadeService cmdbFacadeService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private RpcResourceIdService rpcResourceIdService;

    @Resource
    private ToolService toolService;

    @Resource
    private RpcDebugService rpcDebugService;

    @RpcMapping
    @PostMapping("app/destroy")
    @Permission(value = "服务销毁", window = false)
    public RespMsg<Void> appDestroy(@PathVariable String appId, @RequestBody @Valid AppDestroyRequest request) {
        return rpcAppService.appDestroy(appId, request.getHost(), request.getMemo());
    }


    @RpcMapping
    @PostMapping("app/status")
    @Permission(value = "状态查询", window = false)
    public RespMsg<AppStatusResponse> appStatus(@PathVariable String appId, @RequestBody @Valid AppStatusRequest request) {
        // 获取SOA相关信息
        RespMsg<AppSoaInfo> appSoaInfoRespMsg = rpcAppService.appSoaInfo(appId);
        AppSoaInfo appSoaInfo = appSoaInfoRespMsg.getData();
        AppStatusResponse response = new AppStatusResponse();
        response.setType(appSoaInfo.getLang());

        // 没接入
        if (!appSoaInfo.isAccess()) {
            response.setSoa(false);
            return RespMsg.respSuc(response);
        }

        //JAVA需要检测版本
        if (appSoaInfo.java()) {
            boolean check = upDownConditionVersionCheck(appSoaInfo.getVersion());
            if (!check) { //版本检查不通
                response.setSoa(false);
                return RespMsg.respSuc(response);
            }
        }

        response.setSoa(true);
        response.setVersion(appSoaInfo.getVersion());
        appNodeStatusCheck(request, appId, response.getNodes());
        return RespMsg.respSuc(response);
    }

    /**
     * App节点状态检测
     */
    private void appNodeStatusCheck(AppStatusRequest request, String appId, List<AppStatusResponse.AppNode> nodes) {
        List<String> hosts = request.getHosts();
        if (CollUtil.isEmpty(hosts)) {
            return;
        }
        Map<String, InstanceBo> online = rpcAppService.appInstances(appId).getData().stream().collect(Collectors.toMap(InstanceBo::getHost, v -> v));
        for (String host : hosts) {
            InstanceBo instanceBo = online.get(host);
            AppStatusResponse.AppNode appNode = new AppStatusResponse.AppNode();
            appNode.setHost(host);
            appNode.setStatus(AppStatus.OFFLINE);
            // 如果是在线节点,更新信息
            Optional.ofNullable(instanceBo).ifPresent(instance -> {
                if (instance.getStatus() == InstanceStatus.ONLINE){
                    appNode.setStatus(AppStatus.ONLINE);
                }else if (instance.getStatus() == InstanceStatus.ONLINE_CRITICAL){
                    appNode.setStatus(AppStatus.ONLINE_CRITICAL);
                }else{
                    appNode.setStatus(AppStatus.OFFLINE);
                }
                appNode.setVersion(resolveVersion(CommandUtils.toTagsMap(instance.getTags())));
            });
            nodes.add(appNode);
        }
    }


    /**
     * 不关联窗口的主因在于此接口仅提供给lone调用
     */
    @RpcMapping
    @PostMapping("app/up")
    @Permission(value = "节点上线", window = false)
    public RespMsg<Void> appUp(@PathVariable String appId, @RequestBody @Valid AppUpRequest request) {
        return rpcAppService.appUp(appId, request.getHost(), request.getUserId());
    }


    /**
     * 不关联窗口的主因在于此接口仅提供给lone调用
     */
    @RpcMapping
    @PostMapping("app/down")
    @Permission(value = "节点下线", window = false)
    public RespMsg<Void> appDown(@PathVariable String appId, @RequestBody @Valid AppDownRequest request) {
        return rpcAppService.appDown(appId, request.getHost(), request.getUserId(), true);
    }


    @RpcMapping
    @PostMapping("api/commandKey")
    @Permission(value = "CommandKey", window = false)
    public RespMsg<List<ApiCommandKeyResponse>> apiCommandKey(@PathVariable String appId,
                                                              @RequestBody @Valid ApiCommandKeyRequest request) {
        List<ApiCommandKeyResponse> apiCommandKeyResponses = rpcMetadataService.apiCommandKey(appId, ApiType.valueOf(request.getApiType()))
                .getData().stream().map(item -> BeanUtil.copyProperties(item, ApiCommandKeyResponse.class)).collect(Collectors.toList());
        return RespMsg.respSuc(apiCommandKeyResponses);
    }

    @RpcMapping
    @PostMapping("api/degradeConfig")
    @Permission(value = "降级配置", window = false)
    public RespMsg<Void> apiDegradeConfig(@PathVariable String appId,
                                          @RequestParam(defaultValue = "default") String cluster,
                                          @RequestBody @Valid ApiDegradeRequest request) {
        ApiDegradeConfigBo degradeConfigBo = new ApiDegradeConfigBo();
        degradeConfigBo.setName(request.getName());
        degradeConfigBo.setStatus(request.getStatus());
        String userId = request.getUserId();
        ApiType apiType = ApiType.valueOf(request.getApiType());
        return rpcGovernanceService.apiDegradeConfig(appId, apiType, cluster, userId, degradeConfigBo);
    }


    @RpcMapping
    @PostMapping("api/degradeKey")
    @Permission(value = "降级Keys", window = false)
    public RespMsg<List<ApiGovernanceKeyResponse>> apiDegradeKeys(@PathVariable String appId,
                                                                  @RequestParam(defaultValue = DEFAULT_CLUSTER) String cluster,
                                                                  @RequestBody @Valid ApiCommandKeyRequest request) {
        List<ApiGovernanceKeyResponse> apiGovernanceKeyResponses = rpcGovernanceService.apiDegradeKeys(appId, ApiType.valueOf(request.getApiType()), cluster)
                .getData().stream().map(item -> BeanUtil.copyProperties(item, ApiGovernanceKeyResponse.class)).collect(Collectors.toList());
        return RespMsg.respSuc(apiGovernanceKeyResponses);
    }

    @RpcMapping
    @GetMapping("consumer/degradeKeys")
    public RespMsg<ConsumerDegradeBo> consumerDegradeKeys(@PathVariable String appId, @RequestParam(defaultValue = DEFAULT_CLUSTER) String cluster) {
        RespMsg<ConsumerDegradeBo> respMsg = rpcGovernanceService.consumerDegradeKeys(appId, ApiType.CONSUMER, cluster);
        apiMetadataService.completeApiCommandKeyMetadata(appId, ApiType.CONSUMER,
                respMsg.getData().getAppIds().stream().flatMap(appIdDegradeBo -> appIdDegradeBo.getServices().stream()).flatMap(serviceDegradeBo -> serviceDegradeBo.getApis().stream()).toList());
        return respMsg;
    }


    @RpcMapping
    @PostMapping("api/degradeConfigs")
    @Permission(value = "api降级配置列表", window = false)
    public RespMsg<Map<String, ApiDegradeConfigResponse>> apiDegradeConfigs(@PathVariable String appId,
                                                                            @RequestParam(defaultValue = "default") String cluster,
                                                                            @RequestBody @Valid ApiCommandKeyRequest request) {
        Map<String, ApiDegradeConfigResponse> apiDegradeConfigResponses = new HashMap<>();
        Map<String, ApiDegradeConfigBo> data = rpcGovernanceService.apiDegradeConfig(appId, ApiType.valueOf(request.getApiType()), cluster).getData();
        data.forEach((name, config) -> apiDegradeConfigResponses.put(name, Objects.isNull(config) ? null : BeanUtil.copyProperties(config, ApiDegradeConfigResponse.class)));
        return RespMsg.respSuc(apiDegradeConfigResponses);
    }


    @RpcMapping
    @PostMapping("api/limitConfigs")
    @Permission(value = "api限流配置列表", window = false)
    public RespMsg<Map<String, ApiLimitConfigResponse>> apiLimitConfigs(@PathVariable String appId,
                                                                        @RequestParam(defaultValue = "default") String cluster,
                                                                        @RequestBody @Valid ApiCommandKeyRequest request) {
        Map<String, ApiLimitConfigResponse> apiLimitConfigResponses = new HashMap<>();
        rpcGovernanceService.apiLimitConfig(appId, ApiType.valueOf(request.getApiType()), cluster).getData()
                .forEach((name, config) -> apiLimitConfigResponses.put(name, Objects.isNull(config) ? null : BeanUtil.copyProperties(config, ApiLimitConfigResponse.class)));
        return RespMsg.respSuc(apiLimitConfigResponses);
    }

    @RpcMapping
    @PostMapping("api/serviceLimitConfigs")
    @Permission(value = "service级别限流配置列表", window = false)
    public RespMsg<Map<String, ApiLimitConfigResponse>> apiServiceLimitConfigs(@PathVariable String appId,
                                                                               @RequestParam(defaultValue = "default") String cluster,
                                                                               @RequestBody @Valid ApiCommandKeyRequest request) {
        Map<String, ApiLimitConfigResponse> apiLimitConfigResponses = new HashMap<>();
        rpcGovernanceService.serviceLimitConfig(appId, ApiType.valueOf(request.getApiType()), cluster).getData()
                .forEach((name, config) -> apiLimitConfigResponses.put(name, Objects.isNull(config) ? null : BeanUtil.copyProperties(config, ApiLimitConfigResponse.class)));
        return RespMsg.respSuc(apiLimitConfigResponses);
    }


    @RpcMapping
    @PostMapping("app/limitConfig")
    @Permission(value = "app级别限流配置列表", window = false)
    public RespMsg<ApiLimitConfigResponse> appLimitConfig(@PathVariable String appId,
                                                          @RequestParam(defaultValue = "default") String cluster,
                                                          @RequestBody @Valid ApiCommandKeyRequest request) {
        ApiLimitConfigBo data = rpcGovernanceService.apiLimitConfig(appId, ApiType.valueOf(request.getApiType()), cluster, SinkCommandKeyUtils.rebuildKeyInProviderSide("/*/*", "*")).getData();
        ApiLimitConfigResponse apiLimitConfigResponse = BeanUtil.copyProperties(data, ApiLimitConfigResponse.class);
        return RespMsg.respSuc(apiLimitConfigResponse);
    }

    @RpcMapping
    @PostMapping("api/circuitConfigs")
    @Permission(value = "api熔断配置列表", window = false)
    public RespMsg<Map<String, ApiCircuitConfigResponse>> apiCircuitConfigs(@PathVariable String appId,
                                                                            @RequestParam(defaultValue = "default") String cluster,
                                                                            @RequestBody @Valid ApiCommandKeyRequest request) {
        Map<String, ApiCircuitConfigResponse> apiCircuitConfigResponses = new HashMap<>();
        rpcGovernanceService.apiCircuitConfig(appId, ApiType.valueOf(request.getApiType()), cluster).getData()
                .forEach((name, config) -> apiCircuitConfigResponses.put(name, Objects.isNull(config) ? null : BeanUtil.copyProperties(config, ApiCircuitConfigResponse.class)));
        return RespMsg.respSuc(apiCircuitConfigResponses);
    }

    @RpcMapping
    @PostMapping("api/circuitStatus")
    @Permission(value = "api熔断状态", window = false)
    public RespMsg<ApiCircuitStatusResponse> apiCircuitStatus(@PathVariable String appId, @RequestBody @Valid ApiCircuitStatusRequest request) {
        RespMsg<ApiCircuitEventLogBo> respMsg = rpcAppService.appCircuitEvent(appId, request.getAddress(), request.getCommandKey());
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        ApiCircuitStatusResponse response = new ApiCircuitStatusResponse();
        response.setAddress(request.getAddress());
        response.setCommandKey(request.getCommandKey());
        Optional.ofNullable(respMsg.getData()).ifPresent(event -> {
            response.setCommandKey(event.getName());
            response.setAddress(InstanceUtils.ipFromInstance(event.getInstanceId()));
            response.setTriggerTime(event.getCreatedAt().getTime());
            response.setOpen(event.getStatus() == 1);
        });
        return RespMsg.respSuc(response);
    }


    @RpcMapping
    @PostMapping("api/timeoutConfigs")
    @Permission(value = "api超时配置列表", window = false)
    public RespMsg<Map<String, ApiTimeoutConfigResponse>> apiTimeoutConfigs(@PathVariable String appId,
                                                                            @RequestParam(defaultValue = "default") String cluster,
                                                                            @RequestBody @Valid ApiCommandKeyRequest request) {
        Map<String, ApiTimeoutConfigResponse> apiTimeoutConfigResponses = new HashMap<>();
        rpcGovernanceService.apiTimeoutConfig(appId, ApiType.valueOf(request.getApiType()), cluster).getData()
                .forEach((name, config) -> apiTimeoutConfigResponses.put(name, Objects.isNull(config) ? null : BeanUtil.copyProperties(config, ApiTimeoutConfigResponse.class)));
        return RespMsg.respSuc(apiTimeoutConfigResponses);
    }


    @RpcMapping
    @PostMapping("api/authAppIdToken")
    @Permission(value = "api授权token获取", window = false)
    public RespMsg<ApiAuthAppIdTokenResponse> apiAuthAppIdToken(@PathVariable String appId,
                                                                @RequestParam(defaultValue = "default") String cluster,
                                                                @RequestBody @Valid ApiAuthAppIdTokenRequest request) {

        ApiAuthAppConfigBo configBo = new ApiAuthAppConfigBo();
        configBo.setAuthAppId(appId);
        configBo.setName(request.getName());
        RespMsg<ApiAuthAppConfigTokenBo> respMsg = rpcGovernanceService.apiAuthAppIdToken(request.getProviderAppId(), cluster, configBo);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        return RespMsg.respSuc(BeanUtil.copyProperties(respMsg.getData(), ApiAuthAppIdTokenResponse.class));
    }

    @RpcMapping
    @PostMapping("api/metadata")
    @Permission(value = "服务元数据", window = false)
    public RespMsg<List<ApiMetadataResponse>> apiMetadata(@PathVariable String appId, @RequestBody @Valid ApiMetadataRequest request) {
        String host = request.getHost();
        ApiType apiType = ApiType.valueOf(request.getApiType());

        List<ApiDefinitionMetaBo.ServiceDefinitionMetaBo> apiDefinitionMetadatas = new ArrayList<>();
        if (StrUtil.isNotEmpty(host)) {
            apiDefinitionMetadatas = rpcMetadataService.apiDocMetaLatest(buildInstanceId(appId, host), apiType).getData().getServices();
        }
        if (CollUtil.isEmpty(apiDefinitionMetadatas)) {
            apiDefinitionMetadatas = rpcMetadataService.apiDocMetaAppLatest(appId, apiType).getData().getServices();
        }

        List<ApiMetadataResponse> metadataResponses = new ArrayList<>(apiDefinitionMetadatas.size());

        for (ApiDefinitionMetaBo.ServiceDefinitionMetaBo sd : apiDefinitionMetadatas) {
            ApiMetadataResponse apiMetadataResponse = new ApiMetadataResponse();
            BeanUtil.copyProperties(sd, apiMetadataResponse);
            List<ApiMetadataResponse.MethodDefinitionMetadata> methodDefinitionMetadata = BeanUtil.copyToList(sd.getMethods(), ApiMetadataResponse.MethodDefinitionMetadata.class);
            apiMetadataResponse.setMethods(methodDefinitionMetadata);
            metadataResponses.add(apiMetadataResponse);
        }

        return RespMsg.respSuc(metadataResponses);
    }

    @RpcMapping
    @PostMapping("component/status")
    @Permission(value = "组件健康状态", window = false)
    public RespMsg<ComponentStatusResponse> componentStatus(@PathVariable String appId, @RequestBody @Valid ComponentStatusRequest request) {
        List<String> appIds = Optional.ofNullable(request.getAppId()).orElse(new ArrayList<>());
        ComponentType type = ComponentType.valueOfLowStr(request.getItem());
        ComponentStatusResponse response = new ComponentStatusResponse();
        List<ComponentStatusBo> statusCollect = rpcComponentService.statusCheck(appId, type, appIds).getData();
        List<ComponentStatusBo> outOfServiceStatus = statusCollect.stream().filter(statusBo -> statusBo.getStatus().equals(ComponentStatus.OUT_OF_SERVICE)).collect(Collectors.toList());
        if (!outOfServiceStatus.isEmpty()) {
            response.setStatus(FAIL);
            response.setAppid(outOfServiceStatus.stream().map(ComponentStatusBo::getAppId).filter(StrUtil::isNotEmpty).collect(Collectors.toList()));
            response.setMsg(JacksonUtils.serialize(outOfServiceStatus));
        } else {
            response.setStatus(SUCCESS);
            response.setAppid(appIds);
            response.setMsg(JacksonUtils.serialize(statusCollect));
        }
        return RespMsg.respSuc(response);
    }

    @RpcMapping
    @GetMapping("registry/serviceInfo")
    @Permission(value = "服务注册信息", window = false)
    public RespMsg<List<ServiceHealthBo>> registryServiceInfo(@PathVariable String appId) {
        return rpcRegistryService.serviceInstances(appId);
    }

    @RpcMapping
    @GetMapping("app/group")
    @Permission(value = "服务分组信息", window = false)
    public RespMsg<List<AppGroupInfoResponse>> appGroup(@PathVariable String appId) {
        RespMsg<List<AppGroupInfoBo>> listRespMsg = rpcAppService.appGroupInfo(appId);
        List<AppGroupInfoResponse> result = listRespMsg.getData().stream()
                .map(groupInfoBo -> BeanUtil.copyProperties(groupInfoBo, AppGroupInfoResponse.class))
                .collect(Collectors.toList());
        return RespMsg.respSuc(result);
    }

    @RpcMapping
    @GetMapping("app/group/instance")
    @Permission(value = "服务分组实例信息", window = false)
    public RespMsg<List<AppGroupInstanceResponse>> appGroupInstance(@PathVariable String appId, @RequestParam(required = false) List<String> groupId, @RequestParam(defaultValue = "true") boolean passing) {
        List<AppGroupInstanceResponse> appGroupInstanceResponses = new ArrayList<>();
        Map<String, List<InstanceBo>> groupToInstance = rpcAppService.appGroupInstance(appId, groupId, passing).getData();
        for (Map.Entry<String, List<InstanceBo>> entry : groupToInstance.entrySet()) {
            String key = entry.getKey();
            List<InstanceBo> instanceBos = groupToInstance.getOrDefault(key, Collections.emptyList());
            AppGroupInstanceResponse groupInstanceResponse = new AppGroupInstanceResponse();
            groupInstanceResponse.setGroupId(key);
            groupInstanceResponse.setInstances(instanceBos.stream().map(instanceBo -> {
                AppGroupInstanceResponse.Instance in = new AppGroupInstanceResponse.Instance();
                Map<String, String> tags = CommandUtils.toTagsMap(instanceBo.getTags());
                in.setInstanceId(instanceBo.getId());
                in.setTags(tags);
                in.setStatus(InstanceStatus.ONLINE.equals(instanceBo.getStatus()) ? AppStatus.ONLINE : InstanceStatus.OFFLINE.equals(instanceBo.getStatus())
                        ? AppStatus.OFFLINE : AppStatus.ONLINE_CRITICAL);
                in.setAddress(instanceBo.getHost());
                in.setStartTime(Long.valueOf(tags.getOrDefault("startTime", "0")));
                return in;
            }).collect(Collectors.toList()));
            appGroupInstanceResponses.add(groupInstanceResponse);
        }
        return RespMsg.respSuc(appGroupInstanceResponses);
    }

    private boolean upDownConditionVersionCheck(String version) {
        if (StrUtil.isEmpty(version)) {
            return false;
        }
        //1.2.6
        List<String> vp = StrUtil.split(version, StrUtil.DOT);
        String joinVersion = StrUtil.join("", vp); //126xxxx  126-xxxx

        StringBuilder versionNum = new StringBuilder();
        for (int i = 0; i < joinVersion.length(); i++) {
            if (joinVersion.charAt(i) >= 48 && joinVersion.charAt(i) <= 57) {
                versionNum.append(joinVersion.charAt(i));
            } else {
                break;
            }
        }
        String num = versionNum.toString();
        if (StrUtil.isEmpty(num)) {
            return false;
        }
        return Integer.parseInt(num) >= 126;
    }

    public static String resolveVersion(Map<String, String> data) {
        String version = null;
        if (CollUtil.isEmpty(data)) {
            return version;
        }
        out:
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String key = entry.getKey();
            if (!StrUtil.equalsIgnoreCase(key, SOA_RELEASE_VERSION) && key.contains(StrUtil.DOT)) {
                for (String match : GlobalConstants.VERSIONS) {
                    if (StrUtil.containsAnyIgnoreCase(key, match)) {
                        version = key;
                        break out;
                    }
                }
            }
        }
        return version;
    }

    @RpcMapping
    @GetMapping("app/cluster")
    @Permission(value = "服务集群", window = false)
    public RespMsg<List<AppClusterInfoResponse>> appCluster(@PathVariable String appId) {
        EnvClusterInfoBo envClusterInfoBo = rpcConfigService.appNav(appId).getData();
        if (envClusterInfoBo == null) {
            return RespMsg.respSuc(List.of());
        }
        return RespMsg.respSuc(BeanUtil.copyToList(Opt.ofEmptyAble(envClusterInfoBo.getClusters()).orElse(List.of()), AppClusterInfoResponse.class));
    }


    @RpcMapping
    @GetMapping("instance/byRelease")
    @Permission(value = "使用最新配置的实例", window = false)
    public RespMsg<Page<ConfigInstanceResponse>> instanceByRelease(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String namespace, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<cn.huolala.arch.hermes.mesh.api.bos.config.InstanceBo>> respMsg = rpcConfigService.instanceByRelease(appId, cluster, namespace, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        return RespMsg.respSuc(convertConfigInstanceResponse(respMsg.getData()));
    }

    @RpcMapping
    @GetMapping("instance/byNamespaceNotRelease")
    @Permission(value = "使用非最新配置的实例", window = false)
    public RespMsg<Page<ConfigInstanceResponse>> instanceByNamespaceNotRelease(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String namespace, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<cn.huolala.arch.hermes.mesh.api.bos.config.InstanceBo>> respMsg = rpcConfigService.instanceByNamespaceNotRelease(appId, cluster, namespace, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        return RespMsg.respSuc(convertConfigInstanceResponse(respMsg.getData()));
    }

    @RpcMapping
    @GetMapping("instance/byNamespace")
    @Permission(value = "全部实例", window = false)
    public RespMsg<Page<ConfigInstanceResponse>> instanceByNamespace(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String namespace, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<cn.huolala.arch.hermes.mesh.api.bos.config.InstanceBo>> respMsg = rpcConfigService.instanceByNamespace(appId, cluster, namespace, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        return RespMsg.respSuc(convertConfigInstanceResponse(respMsg.getData()));
    }

    @RpcMapping
    @GetMapping(value = "app/config/authToken")
    @Permission(value = "获取配置中心鉴权Token", window = false)
    public RespMsg<String> configAuthToken(@PathVariable String appId) {
        RespMsg<AccessKeyBo> respMsg = rpcConfigService.availableAccessKey(appId);
        AtomicReference<String> authToken = new AtomicReference<>(null);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        Optional.ofNullable(respMsg.getData()).ifPresent(tk -> authToken.set(tk.getSecret()));
        return RespMsg.respDataSuc(authToken.get());
    }


    private static Page<ConfigInstanceResponse> convertConfigInstanceResponse(Page<cn.huolala.arch.hermes.mesh.api.bos.config.InstanceBo> instanceBoPage) {
        return graftPage(instanceBoPage, instanceBo -> {
            ConfigInstanceResponse configInstanceResponse = BeanUtil.copyProperties(instanceBo, ConfigInstanceResponse.class);
            Opt.ofEmptyAble(instanceBo.getConfigs()).ifPresent(instanceConfigBos -> instanceConfigBos.stream().findFirst().ifPresent(instanceConfigBo -> {
                configInstanceResponse.setDataChangeLastModifiedTime(instanceConfigBo.getDataChangeLastModifiedTime());
                configInstanceResponse.setReleaseDeliveryTime(instanceConfigBo.getReleaseDeliveryTime());
            }));
            return configInstanceResponse;
        });
    }

    private static <T, R> Page<R> graftPage(Page<T> page, Function<T, R> mapFunction) {
        Page<R> repage = Page.of(page.getCurrent(), page.getSize(), page.getTotal());
        repage.setRecords(page.getRecords().stream().map(mapFunction).toList());
        return repage;
    }


    @RpcMapping
    @GetMapping("monitor/metrics")
    @Permission(value = "监控指标", window = false)
    public RespMsg<List<LabelTimeValueBo>> monitorMetrics(@RequestParam String querySql, @RequestParam Long startTimeMills, @RequestParam Long endTimeMills, @RequestParam(required = false) String source) {
        return rpcMetricService.monitorMetrics(querySql, startTimeMills, endTimeMills, source);
    }


    @RpcMapping
    @PostMapping("inspection/emergency")
    @Permission(value = "服务应急自检", window = false)
    public RespMsg<InspectionResponse> inspectionEmergency(@RequestBody InspectionRequest request) {
        RespMsg<InspectionReportBo> respMsg = rpcInspectionService.executeInspection(InspectionScene.EMERGENCY, ParamContext.of(request));
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg.getCode(), respMsg.getMsg());
        }
        InspectionReportBo inspectionReportBo = respMsg.getData();
        InspectionResponse inspectionResponse = new InspectionResponse();

        inspectionResponse.setComponents(inspectionReportBo.getComponents().stream().map(componentReportBo -> {
            InspectionResponse.Component component = new InspectionResponse.Component();
            component.setRegion(SystemUtils.getRegion().getAbbreviation());
            component.setComponentName(componentReportBo.getName().getMemo());

            List<InspectionResponse.Cluster> clusters = new ArrayList<>();
            component.setClusters(clusters);

            InspectionResponse.Cluster defaultCluster = new InspectionResponse.Cluster();
            clusters.add(defaultCluster);

            defaultCluster.setClusterName(DEFAULT_CLUSTER);
            defaultCluster.setCheckItems(componentReportBo.getMeterReports().stream().map(meterReportBo -> {
                InspectionResponse.CheckItem checkItem = new InspectionResponse.CheckItem();
                checkItem.setId(meterReportBo.getId());
                checkItem.setItemName(meterReportBo.getName());
                checkItem.setStatus(meterReportBo.getStatus().getMemo());
                checkItem.setDetails(meterReportBo.getDetails());
                checkItem.setExtra(meterReportBo.getExtra());
                return checkItem;
            }).toList());
            return component;
        }).toList());
        inspectionResponse.setComponentsSummary(BeanUtil.copyProperties(inspectionReportBo.getAiInspectionReport(), InspectionResponse.AiInspectionReport.class));
        return RespMsg.respSuc(inspectionResponse);
    }


    @RpcMapping
    @PostMapping("cipher/namespace")
    public RespMsg<NamespaceBo> cipherNamespace(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        return rpcCipherService.cipherNamespace(appId, cluster);
    }

    @PostMapping("cipher/init")
    @RpcMapping
    @Permission("初始化加密功能")
    public RespMsg<Void> initCipher(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        return rpcCipherService.initCipher(appId, cluster);
    }


    @RpcMapping
    @PostMapping("cipher/addItem")
    @Permission(value = "创建加密配置")
    public RespMsg<ItemDBo> postItem(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                     @RequestParam(defaultValue = GlobalConstants.CIPHER_NAMESPACE) String namespace, @RequestBody ItemDBo itemDBo) {
        Opt.ofBlankAble(itemDBo.getDataChangeCreatedBy()).ifPresent(s -> {
            RpcContext.getClientAttachment().putRawAttachment(GlobalConstants.OPERATOR_USER_ID, itemDBo.getDataChangeCreatedBy());
            RpcContext.getClientAttachment().putAttachment(GlobalConstants.OPERATOR_USER_ID, itemDBo.getDataChangeCreatedBy());
        });
        return rpcCipherService.postItem(appId, cluster, namespace, itemDBo);
    }


    @RpcMapping
    @PutMapping("cipher/modifyItem")
    @Permission(value = "修改加密配置")
    public RespMsg<ItemDBo> putItem(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                    @RequestParam(defaultValue = GlobalConstants.CIPHER_NAMESPACE) String namespace, @RequestBody ItemDBo itemDBo) {
        Opt.ofBlankAble(itemDBo.getDataChangeLastModifiedBy()).ifPresent(s -> {
            RpcContext.getClientAttachment().putRawAttachment(GlobalConstants.OPERATOR_USER_ID, itemDBo.getDataChangeLastModifiedBy());
            RpcContext.getClientAttachment().putAttachment(GlobalConstants.OPERATOR_USER_ID, itemDBo.getDataChangeLastModifiedBy());
        });
        return rpcCipherService.putItem(appId, cluster, namespace, itemDBo);
    }


    @RpcMapping
    @PostMapping("cipher/text")
    @Permission(value = "查看加密配置")
    public RespMsg<CipherTextBo> plainText(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                           @RequestParam String userId, @RequestBody CipherTextBo cipherTextBo) {
        Opt.ofBlankAble(userId).ifPresent(s -> {
            RpcContext.getClientAttachment().putRawAttachment(GlobalConstants.OPERATOR_USER_ID, userId);
            RpcContext.getClientAttachment().putAttachment(GlobalConstants.OPERATOR_USER_ID, userId);
        });
        return rpcCipherService.decipherText(appId, cluster, cipherTextBo);
    }


    /*伙伴加密-----------------------------------------------------------------------------------------------------------------------------------------*/

    @RpcMapping
    @PostMapping("cipher/buddy/find")
    @Permission(value = "加密_密钥查找")
    public RespMsg<ItemDBo> buddyFind(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                      @RequestParam String namespace) {
        return rpcCipherService.buddyFind(appId, cluster, namespace);
    }


    @RpcMapping
    @PostMapping("cipher/buddy/init")
    @Permission(value = "加密_密钥初始化")
    public RespMsg<ItemDBo> buddyInit(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                      @RequestParam String namespace) {
        return rpcCipherService.buddyInit(appId, cluster, namespace);
    }


    @RpcMapping
    @PostMapping("cipher/buddy/encrypt")
    @Permission(value = "加密_配置加密")
    public RespMsg<ItemDBo> buddyEncrypt(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                         @RequestParam String namespace, @RequestBody ItemDBo itemDBo) {
        return rpcCipherService.buddyEncrypt(appId, cluster, namespace, itemDBo);
    }

    @RpcMapping
    @PostMapping("cipher/buddy/plaintext")
    @Permission(value = "加密_查看明文")
    public RespMsg<CipherTextBo> buddyPlainText(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                                @RequestParam String userId, @RequestParam String namespace, @RequestBody CipherTextBo cipherTextBo) {
        Opt.ofBlankAble(userId).ifPresent(s -> {
            RpcContext.getClientAttachment().putRawAttachment(GlobalConstants.OPERATOR_USER_ID, userId);
            RpcContext.getClientAttachment().putAttachment(GlobalConstants.OPERATOR_USER_ID, userId);
        });
        return rpcCipherService.buddyPlainText(appId, cluster, namespace, cipherTextBo);
    }

    @PostMapping("cipher/buddy/rotation")
    @RpcMapping
    @Permission("加密_轮转密钥")
    public RespMsg<Void> buddyRotation(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                       @RequestParam String namespace) {
        return rpcCipherService.buddyRotation(appId, cluster, namespace);
    }


    /*------------------------------------------------------------------------------------------------------------------------------------------------*/

    @PostMapping("resourceId/detection")
    @RpcMapping
    @Permission("资源ID使用检测") //检测
    public RespMsg<DetectionBo> resourceDetection(@RequestParam String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                                  @RequestParam String namespace) {
        return rpcResourceIdService.resourceDetection(appId, cluster, namespace, DateUtil.offsetDay(new Date(), -1).toString());
    }


    @RpcMapping
    @GetMapping("governance/clusters")
    @Permission(value = "服务治理集群信息")
    @AppIdPermission
    public RespMsg<List<GovernanceCluster>> governanceClusters(@PathVariable String appId) {
        EnvClusterInfoBo envClusterInfoBo = rpcConfigService.appNav(appId).getData();
        if (envClusterInfoBo == null) {
            return RespMsg.respSuc(List.of());
        }
        List<GovernanceCluster> clusters = envClusterInfoBo.getClusters().stream().map(clusterBo -> {
            String clusterName = clusterBo.getName();
            GovernanceCluster gc = new GovernanceCluster();
            gc.setId(clusterName);

            if (StrUtil.equals(clusterName, DEFAULT_CLUSTER)) {
                gc.setName("全局配置");
            } else if (StrUtil.startWith(clusterName, "zone")) {
                if (StrUtil.contains(clusterName, "-g-")) {
                    gc.setName("服务分组");
                    gc.setOrder(3);
                } else {
                    gc.setName("部署泳道");
                    gc.setOrder(1);
                }
            } else if (StrUtil.startWith(clusterName, "v")) {
                gc.setName("灰度版本");
                gc.setOrder(2);
            } else {
                gc.setName("集群名称");
                gc.setOrder(4);
            }
            return gc;
        }).toList().stream().collect(Collectors.groupingBy(GovernanceCluster::getName)).entrySet().stream().map(entry -> {
            GovernanceCluster governanceCluster = new GovernanceCluster();
            governanceCluster.setName(entry.getKey());
            governanceCluster.setClusters(entry.getValue());
            entry.getValue().stream().findFirst().ifPresent(gc -> governanceCluster.setOrder(gc.getOrder()));
            return governanceCluster;
        }).sorted(Comparator.comparingInt(GovernanceCluster::getOrder)).toList();
        return RespMsg.respSuc(clusters);
    }


    @RpcMapping
    @GetMapping("governance/consumer")
    @Permission(value = "服务治理客户端配置")
    @AppIdPermission
    public RespMsg<GovernanceConsumerKey> governanceConsumerKey(@PathVariable String appId, @RequestParam(required = false) List<String> clusters) {
        if (CollUtil.isNotEmpty(clusters)) {
            CollUtil.removeAny(clusters, DEFAULT_CLUSTER);
        }
        return rpcOpenapiMonitorService.governanceConsumerKey(appId, clusters);
    }


    @RpcMapping
    @GetMapping("governance/provider")
    @Permission(value = "服务治理服务端配置")
    @AppIdPermission
    public RespMsg<GovernanceProviderKey> governanceProviderKey(@PathVariable String appId, @RequestParam(required = false) List<String> clusters) {
        if (CollUtil.isNotEmpty(clusters)) {
            CollUtil.removeAny(clusters, DEFAULT_CLUSTER);
        }
        return rpcOpenapiMonitorService.governanceProviderKey(appId, clusters);
    }


    @RpcMapping
    @GetMapping("governance/config")
    @Permission(value = "服务治理集群配置")
    @AppIdPermission
    public RespMsg<GovernanceClusterConfig> governanceClusterConfig(@PathVariable String appId, @RequestParam ApiType apiType, @RequestParam GovernanceLevel level, @RequestParam String name, @RequestParam List<String> clusters) {
        if (CollUtil.isNotEmpty(clusters)) {
            CollUtil.removeAny(clusters, DEFAULT_CLUSTER);
        }
        return rpcOpenapiMonitorService.governanceClusterConfig(appId, apiType, level, name, clusters);
    }


    @RpcMapping
    @PostMapping("governance/config")
    @Permission(value = "服务治理集群配置")
    @AppIdPermission
    public RespMsg<GovernanceConfig> governanceConfig(@PathVariable String appId, @RequestParam ApiType apiType, @RequestParam String name, @UserId String userId,
                                                      @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestBody GovernanceConfig config, @AreaEnvEntity AreaEnv areaEnv) {
        RespMsg<GovernanceConfig> resp = rpcOpenapiMonitorService.governanceConfig(appId, apiType, name, cluster, userId, config);
        if (resp.isSuccess()) {
            ofNullable(config.getLimit()).ifPresent(apiLimitConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_LIMIT, areaEnv, Map.of("cluster", cluster, "api", name, "value", apiLimitConfig.getTokenBucketCount(), "status", apiLimitConfig.getStatus() == 1 ? "enabled" : "disabled")));
            ofNullable(config.getDegrade()).ifPresent(apiDegradeConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_DEGRADE, areaEnv, Map.of("cluster", cluster, "api", name, "status", apiDegradeConfig.isStatus() ? "enabled" : "disabled")));
            ofNullable(config.getCircuit()).ifPresent(apiCircuitConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_CIRCUIT, areaEnv,
                    Map.of("cluster", cluster, "api", name, "errorThresholdPercentage", apiCircuitConfig.getErrorThresholdPercentage(),
                            "requestVolumeThreshold", apiCircuitConfig.getRequestVolumeThreshold(), "status", apiCircuitConfig.getEnabled() ? "enabled" : "disabled",
                            "permittedNumberOfCallsInHalfOpenState", apiCircuitConfig.getPermittedNumberOfCallsInHalfOpenState())));
            ofNullable(config.getTimeout()).ifPresent(apiTimeoutConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_TIMEOUT, areaEnv,
                    Map.of("cluster", cluster, "api", StrUtil.isEmptyOrUndefined(name) ? "*" : name,
                            "readTimeout", apiTimeoutConfig.getReadTimeout(), "connectionTimeout", apiTimeoutConfig.getConnectionTimeout(), "status", apiTimeoutConfig.isEnabled() ? "enabled" : "disabled")));
            ofNullable(config.getLog()).ifPresent(apiDynamicLogConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_DYNAMIC_LOG, areaEnv, Map.of("cluster", cluster, "api", name,
                    "level", apiDynamicLogConfig.getLogLevel(), "printOnFail", apiDynamicLogConfig.isPrintOnFail(), "status", apiDynamicLogConfig.isEnabled() ? "enabled" : "disabled")));
            ofNullable(config.getAuth()).ifPresent(apiAuthConfig -> webHookService.appOperation(appId, userId, GOVERNANCE_AUTH, areaEnv,
                    Map.of("cluster", cluster, "api", name, "serviceName", apiAuthConfig.getServiceName(), "status", apiAuthConfig.isEnabled() ? "enabled" : "disabled")));
        }
        return resp;
    }


    @RpcMapping
    @GetMapping("app/permission")
    @Permission(value = "appId权限查询")
    @AppIdPermission
    public RespMsg<AppPermissionResponse> appPermission(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                                        @UserId String userId, @AreaEnvEntity AreaEnv areaEnv) {
        AppPermissionResponse response = new AppPermissionResponse();
        ReleaseWindow releaseWindow = loneFacadeService.get(appId, cluster, RegionEnum.abbreviationToCode(areaEnv.getName()), areaEnv.getEnv().toString());
        response.setWindow(releaseWindow.canRelease());

        List<ReleaseWindow.Window> windows = new ArrayList<>();
        Opt.ofEmptyAble(releaseWindow.getRelwindow()).ifPresent(windows::addAll);
        Opt.ofEmptyAble(releaseWindow.getTempRelwindow()).ifPresent(windows::addAll);
        Optional<ReleaseWindow.Window> optional = windows.stream().filter(window -> StrUtil.equals(window.getType(), releaseWindow.getWindowType())).findFirst();
        if (optional.isPresent()) {
            response.setStartTime(optional.get().getStart());
            response.setEndTime(optional.get().getEnd());
        } else if (releaseWindow.getCloseWindow() != null && StrUtil.equals(releaseWindow.getCloseWindow().getType(), releaseWindow.getWindowType())) {
            ofNullable(releaseWindow.getCloseWindow().getStart_time()).ifPresent(date -> response.setStartTime(DateUtils.format(date, PATTERN_HM)));
            ofNullable(releaseWindow.getCloseWindow().getEnd_time()).ifPresent(date -> response.setEndTime(DateUtils.format(date, PATTERN_HM)));
        }


        AppBaseInfo appBaseInfo = cmdbFacadeService.baseInfo(appId);
        String masterUserId = appBaseInfo.getMasterProduct();

        List<String> permissionUsers = StrUtil.split(appBaseInfo.getMasterDev(), StrUtil.COMMA);
        permissionUsers.add(masterUserId);
        permissionUsers.addAll(StrUtil.split(appBaseInfo.getMasterOps(), StrUtil.COMMA));
        //非PRD 测试,QA人员也加上权限
        if (!Env.PRD.equals(areaEnv.getEnv())) {
            permissionUsers.addAll(StrUtil.split(appBaseInfo.getMasterTest(), StrUtil.COMMA));
            permissionUsers.addAll(StrUtil.split(appBaseInfo.getRelatedStaff(), StrUtil.COMMA));
        }
        response.setPermission(permissionUsers.contains(userId) || permissionService.isAdmin(userId));
        return RespMsg.respSuc(response);
    }


    @RpcMapping
    @GetMapping("governance/consumer/stats")
    @Permission(value = "客户端治理指标")
    public RespMsg<GovernanceConsumerComposeStats> governanceConsumerStats(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        RespMsg<GovernanceConsumerKey> respMsg = rpcOpenapiMonitorService.governanceConsumerKey(appId, List.of(cluster));
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        GovernanceConsumerComposeStats stats = new GovernanceConsumerComposeStats();
        stats.setStats(respMsg.getData().getStats());
        stats.setTrafficStats(respMsg.getData().getTrafficStats());
        return RespMsg.respSuc(stats);
    }


    @RpcMapping
    @GetMapping("governance/provider/stats")
    @Permission(value = "服务端治理指标")
    @AppIdPermission
    public RespMsg<GovernanceProviderComposeStats> governanceProviderStats(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster) {
        RespMsg<GovernanceProviderKey> respMsg = rpcOpenapiMonitorService.governanceProviderKey(appId, List.of(cluster));
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        GovernanceProviderComposeStats stats = new GovernanceProviderComposeStats();
        stats.setStats(respMsg.getData().getStats());
        stats.setTrafficStats(respMsg.getData().getTrafficStats());
        return RespMsg.respSuc(stats);
    }


    @RpcMapping
    @GetMapping("governance/limitLogs")
    @AppIdPermission
    public RespMsg<Page<ApiLimitLog>> apiLimitLog(@PathVariable String appId, @RequestParam ApiType apiType, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiLimitLogBo>> respMsg = rpcGovernanceService.apiLimitLogs(appId, apiType, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiLimitLogBo -> {
            ApiLimitLog apiLimitLog = BeanUtil.copyProperties(apiLimitLogBo, ApiLimitLog.class);
            ofNullable(apiLimitLogBo.getApiLimitConfig()).ifPresent(apiLimitConfigBo -> {
                apiLimitLog.setStatus(apiLimitConfigBo.getStatus());
                apiLimitLog.setTokenBucketCount(apiLimitConfigBo.getTokenBucketCount());
            });
            return apiLimitLog;
        }));
    }


    @RpcMapping
    @GetMapping("governance/circuitLogs")
    @AppIdPermission
    public RespMsg<Page<ApiCircuitLog>> apiCircuitLogs(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiCircuitLogBo>> respMsg = rpcGovernanceService.apiCircuitLogs(appId, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiCircuitLogBo -> {
            ApiCircuitLog apiCircuitLog = BeanUtil.copyProperties(apiCircuitLogBo, ApiCircuitLog.class);
            ofNullable(apiCircuitLogBo.getApiCircuitConfig()).ifPresent(apiCircuitConfigBo -> BeanUtil.copyProperties(apiCircuitConfigBo, apiCircuitLog));
            return apiCircuitLog;
        }));
    }


    @RpcMapping
    @GetMapping("governance/degradeLogs")
    @AppIdPermission
    public RespMsg<Page<ApiDegradeLog>> apiDegradeLogs(@PathVariable String appId, @RequestParam ApiType apiType, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiDegradeLogBo>> respMsg = rpcGovernanceService.apiDegradeLogs(appId, apiType, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiDegradeLogBo -> BeanUtil.copyProperties(apiDegradeLogBo, ApiDegradeLog.class)));
    }


    @RpcMapping
    @GetMapping("governance/timeoutLogs")
    @AppIdPermission
    public RespMsg<Page<ApiTimeoutLog>> apiTimeoutLogs(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiTimeoutLogBo>> respMsg = rpcGovernanceService.apiTimeoutLogs(appId, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiTimeoutLogBo -> {
            ApiTimeoutLog apiCircuitLog = BeanUtil.copyProperties(apiTimeoutLogBo, ApiTimeoutLog.class);
            ofNullable(apiTimeoutLogBo.getApiTimeoutConfig()).ifPresent(apiTimeoutConfigBo -> BeanUtil.copyProperties(apiTimeoutConfigBo, apiCircuitLog));
            return apiCircuitLog;
        }));
    }


    @RpcMapping
    @GetMapping("governance/authLogs")
    @AppIdPermission
    public RespMsg<Page<ApiAuthLog>> apiAuthLogs(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiAuthLogBo>> respMsg = rpcGovernanceService.apiAuthLogs(appId, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiTimeoutLogBo -> BeanUtil.copyProperties(apiTimeoutLogBo, ApiAuthLog.class)));
    }

    @RpcMapping
    @PostMapping("governance/limitConfig")
    @Permission(value = "限流配置")
    public RespMsg<Void> apiLimitConfig(@PathVariable String appId, @UserId String userId, @RequestParam ApiType apiType, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster,
                                        @RequestBody ApiLimitConfigBo apiLimitConfig, @AreaEnvEntity AreaEnv areaEnv) {
        RespMsg<Void> resp = rpcGovernanceService.apiLimitConfig(appId, apiType, cluster, userId, apiLimitConfig);
        if (resp.isSuccess()) {
            webHookService.appOperation(appId, userId, GOVERNANCE_LIMIT, areaEnv, Map.of("cluster", cluster, "api", apiLimitConfig.getName(), "value", apiLimitConfig.getTokenBucketCount(), "status", apiLimitConfig.getStatus() == 1 ? "enabled" : "disabled"));
        }
        return resp;
    }


    @RpcMapping
    @PostMapping("governance/degradeConfig")
    @Permission(value = "降级配置")
    @AppIdPermission
    public RespMsg<Void> apiDegradeConfig(@PathVariable String appId, @UserId String userId, @RequestParam ApiType apiType, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestBody ApiDegradeConfigBo apiDegradeConfig, @AreaEnvEntity AreaEnv areaEnv) {
        RespMsg<Void> resp = rpcGovernanceService.apiDegradeConfig(appId, apiType, cluster, userId, apiDegradeConfig);
        if (resp.isSuccess()) {
            webHookService.appOperation(appId, userId, GOVERNANCE_DEGRADE, areaEnv,
                    Map.of("cluster", cluster, "api", apiDegradeConfig.getName(), "status", apiDegradeConfig.isStatus() ? "enabled" : "disabled"));
        }
        return resp;
    }

    @RpcMapping
    @PostMapping("governance/authAppConfig")
    @Permission(value = "授权配置")
    @AppIdPermission
    public RespMsg<Void> apiAuthAppConfig(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @UserId String userId, @RequestBody ApiAuthAppConfigBo apiAuthAppConfig) {
        return rpcGovernanceService.apiAuthAppConfig(appId, cluster, userId, apiAuthAppConfig);
    }

    @RpcMapping
    @DeleteMapping("governance/authAppConfig")
    @Permission(value = "取消授权配置")
    @AppIdPermission
    public RespMsg<Void> apiAuthAppConfigDelete(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @UserId String userId, @RequestBody ApiAuthAppConfigBo apiAuthAppConfig) {
        return rpcGovernanceService.apiAuthAppConfigDelete(appId, cluster, userId, apiAuthAppConfig);
    }


    @RpcMapping
    @GetMapping("governance/authAppConfigLogs")
    @AppIdPermission
    public RespMsg<Page<ApiAuthAppConfigLog>> apiAuthAppConfigLogs(@PathVariable String appId, @RequestParam(defaultValue = GlobalConstants.DEFAULT_CLUSTER) String cluster, @RequestParam String name, @RequestParam int num, @RequestParam int size) {
        RespMsg<Page<ApiAuthAppConfigLogBo>> respMsg = rpcGovernanceService.apiAuthAppConfigLogs(appId, cluster, name, num, size);
        if (!respMsg.isSuccess()) {
            return RespMsg.respErr(respMsg);
        }
        return RespMsg.respSuc(graftPage(respMsg.getData(), apiAuthAppConfigLogBo -> {
            ApiAuthAppConfigLog apiAuthAppConfigLog = BeanUtil.copyProperties(apiAuthAppConfigLogBo, ApiAuthAppConfigLog.class);
            ofNullable(apiAuthAppConfigLogBo.getApiAuthAppConfig()).ifPresent(apiAuthAppConfigBo -> BeanUtil.copyProperties(apiAuthAppConfigBo, apiAuthAppConfigLog));
            return apiAuthAppConfigLog;
        }));
    }

    @GetMapping("ip/info")
    public RespMsg<IpInfo> getIpInfo(@RequestParam String ip) {
        return RespMsg.respSuc(loneFacadeService.ipInfo(ip));
    }


    @RpcMapping
    @PostMapping("mvc/debug")
    @Permission(value = "MVC调试")
    @AppIdPermission
    public RespMsg<HttpResponseBo> mvcDebug(@UserId String userId, @AreaEnvId Long envId, @RequestBody MvcRequest mvcRequest) {
        List<Pair> contextParams = toolService.requestParams(userId, envId).stream().map(requestParam -> Pair.builder().name(requestParam.getName()).value(requestParam.getValue()).build()).toList();
        HttpRequestBo request = mvcRequest.getRequest();
        request.setContext(contextParams);
        RespMsg<HttpResponseBo> respMsg = rpcDebugService.mvcDebug(request);
        request.setContext(List.of());
        toolService.postMvcRequestLog(userId, envId, request, respMsg.getData());
        return respMsg;
    }


    @RpcMapping
    @PostMapping("soa/debug")
    @Permission(value = "SOA调试")
    @AppIdPermission
    public RespMsg<SoaResponseBo> soaDebug(@UserId String userId, @AreaEnvId Long envId, @RequestBody SoaRequest soaRequest) {
        List<Pair> contextParams = toolService.requestParams(userId, envId).stream().map(requestParam -> Pair.builder().name(requestParam.getName()).value(requestParam.getValue()).build()).toList();
        SoaRequestBo request = soaRequest.getRequest();
        request.setContext(contextParams);
        RespMsg<SoaResponseBo> respMsg = rpcDebugService.soaDebug(request);
        request.setContext(List.of());
        toolService.postSoaRequestLog(userId, envId, soaRequest, respMsg.getData());
        return respMsg;
    }

}
