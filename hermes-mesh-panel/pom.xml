<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.huolala.arch.hermes</groupId>
		<artifactId>hll-hermes-mesh</artifactId>
		<version>${revision}</version>
	</parent>
	<artifactId>hermes-mesh-panel</artifactId>


	<dependencies>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-mesh-metric</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-mesh-rpc</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-cmdb</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-mesh-api</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-mesh-common</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-oss</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-consul</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-kali</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-lone</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-sso</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-lark</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mybatis-plus-starter}</version>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-spring</artifactId>
					<groupId>org.mybatis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	    <dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-jsqlparser</artifactId>
			<version>${mybatis-plus-starter}</version>
		</dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <!--结合对象和导出Excel表 https://easyexcel.opensource.alibaba.com/docs/current/-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-component-lark</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>mesh-stream-starter</artifactId>
            <version>${revision}</version>
        </dependency>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-open-model</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-proxy</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-moc</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-accesslog</artifactId>
			<version>${revision}</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jdk8</artifactId>
		</dependency>
    </dependencies>

    <build>
		<finalName>ci-hermes-mesh-panel</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
