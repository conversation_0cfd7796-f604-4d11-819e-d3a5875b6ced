package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.consul.CheckData;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;

import java.io.IOException;
import java.util.Set;

public class CheckDataSetHandler extends JacksonTypeHandler {

    public CheckDataSetHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            getObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return getObjectMapper().readValue(json, new TypeReference<Set<CheckData>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
