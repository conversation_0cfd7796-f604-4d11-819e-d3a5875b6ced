package cn.huolala.arch.hermes.mesh.dao.entity.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.dao.entity.BaseEntity;
import cn.huolala.arch.hermes.mesh.dao.handler.inspection.ComponentReportHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "inspection_report",autoResultMap = true)
public class InspectionReport extends BaseEntity {

    /**
     *  状态
     */
    private InspectionMeterStatus status;

    /**
     * 规则场景
     */
    private InspectionScene scene;

    /**
     * 组件列表
     */
    @TableField(typeHandler = ComponentReportHandler.class)
    private List<ComponentReport> components = new ArrayList<>();

    /**
     * 自检参数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String,Object> paramContext = new HashMap<>();

    /**
     * 总结
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AiInspectionReport aiInspectionReport;

    /**
     * 组件报告
     */
    @Data
    public static class ComponentReport implements Serializable {

        /**
         * 组件名称
         */
        private InspectionComponent name;

        /**
         * 状态
         */
        private InspectionMeterStatus status;

        /**
         * 健康指标
         */
        private List<Map<String,Object>> meterReports = new ArrayList<>();
    }

    @Data
    public static class AiInspectionReport implements Serializable{

        /**
         * 组件状态总览
         */
        private List<ComponentOverview> componentOverview;

        /**
         * 异常与应急操作明细
         */
        private List<ExceptionAndActionDetail> exceptionAndActionDetails;

        /**
         * 自我验证结果
         */
        private SelfValidationResult selfValidationResult;


        @Data
        public static class ComponentOverview {

            private String componentName;

            private String status;

            private String description;

        }

        @Data
        public static class ExceptionAndActionDetail {

            private String componentName;

            private String abnormalMetric;

            private String reason;

            private List<String> emergencyActions;
        }

        @Data
        public static class SelfValidationResult {

            private String missingDangerWarningMetrics;

            private String dataContradictionsExist;

            private String emergencyActionsOperable;

            private String finalConclusion;
        }
    }
}
