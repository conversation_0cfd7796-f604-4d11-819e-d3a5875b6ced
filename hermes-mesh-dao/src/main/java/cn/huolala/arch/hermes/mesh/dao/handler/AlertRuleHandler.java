package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.alert.AlertRule;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import java.io.IOException;

public class AlertRuleHandler extends JacksonTypeHandler {

    public AlertRuleHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, AlertRule.class);
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
