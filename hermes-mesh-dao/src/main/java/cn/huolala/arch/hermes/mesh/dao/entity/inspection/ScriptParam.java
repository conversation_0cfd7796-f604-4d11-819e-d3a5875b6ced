package cn.huolala.arch.hermes.mesh.dao.entity.inspection;


import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.dao.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "script_param",autoResultMap = true)
public class ScriptParam extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 组件
     */
    private InspectionComponent component;

    /**
     * 备注
     */
    private String memo;
}

