package cn.huolala.arch.hermes.mesh.dao.mapper.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.dao.entity.inspection.InspectionRule;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface InspectionRuleMapper extends BaseMapper<InspectionRule> {

    default List<InspectionRule> selectList(InspectionScene scene){
        QueryWrapper<InspectionRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scene",scene);
        queryWrapper.eq("enabled",true);
        queryWrapper.orderByDesc("id");
        return selectList(queryWrapper);
    }


    default List<InspectionRule> selectAllList(InspectionScene scene){
        QueryWrapper<InspectionRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("scene",scene);
        queryWrapper.orderByDesc("_order");
        return selectList(queryWrapper);
    }


}
