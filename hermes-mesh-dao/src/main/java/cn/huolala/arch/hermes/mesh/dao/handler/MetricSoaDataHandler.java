package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.metric.MetricSoaData;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;

import java.io.IOException;
import java.util.Map;

public class MetricSoaDataHandler extends JacksonTypeHandler {

    public MetricSoaDataHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {

            getObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return getObjectMapper().readValue(json, new TypeReference<Map<String, MetricSoaData>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
