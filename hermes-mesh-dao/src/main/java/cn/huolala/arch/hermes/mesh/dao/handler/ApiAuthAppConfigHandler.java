package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.ApiAuthAppConfig;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;

public class ApiAuthAppConfigHandler extends JacksonTypeHandler {

    public ApiAuthAppConfigHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<ApiAuthAppConfig>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
