package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.AppZoneRouterConfig;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;

public class AppZoneRouterConfigHandler extends JacksonTypeHandler {

    public AppZoneRouterConfigHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<AppZoneRouterConfig>() {});
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
