package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.ApiLimitConfig;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;

public class ApiLimitConfigHandler extends JacksonTypeHandler {

    public ApiLimitConfigHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<ApiLimitConfig>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
