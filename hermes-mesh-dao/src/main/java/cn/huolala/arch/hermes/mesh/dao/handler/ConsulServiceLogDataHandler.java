package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.consul.ConsulServiceLog;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class ConsulServiceLogDataHandler extends JacksonTypeHandler {

    public ConsulServiceLogDataHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<ConsulServiceLog.Data>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
