package cn.huolala.arch.hermes.mesh.dao.mapper;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.dao.entity.inspection.ScriptParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface ScriptParamMapper extends BaseMapper<ScriptParam> {


    default List<ScriptParam> selectByComponent(InspectionComponent component) {
        QueryWrapper<ScriptParam> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("component", List.of(InspectionComponent.ALL, component));
        return selectList(queryWrapper);
    }


}
