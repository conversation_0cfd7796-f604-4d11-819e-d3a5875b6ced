package cn.huolala.arch.hermes.mesh.dao.handler.inspection;

import cn.huolala.arch.hermes.mesh.dao.entity.inspection.InspectionRule;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class InspectionRuleHandler extends JacksonTypeHandler {

    public InspectionRuleHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<InspectionRule>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
