package cn.huolala.arch.hermes.mesh.dao.entity.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionType;
import cn.huolala.arch.hermes.mesh.dao.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule")
public class InspectionRule extends BaseEntity {

    /**
     * 规则名称
     * 唯一
     */
    private String name;

    /**
     * 规则执行脚本
     */
    private String script;

    /**
     * 脚本备注
     */
    private String memo;

    /**
     * 规则场景
     */
    private InspectionScene scene;

    /**
     * 所属组件
     * CONSUL,APOLLO,SOA,GOPROXY
     */
    private InspectionComponent component;

    /**
     * 作用类型
     */
    private InspectionType type;

    /**
     * 排序
     */
    @TableField("_order")
    private int order;

    /**
     * 是否启用
     */
    private boolean enabled;

    /**
     * 应急动作
     */
    private String action;
}
