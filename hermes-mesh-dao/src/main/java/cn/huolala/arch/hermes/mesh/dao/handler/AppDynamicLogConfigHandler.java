package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.AppDynamicLogConfig;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;

public class AppDynamicLogConfigHandler extends JacksonTypeHandler {

    public AppDynamicLogConfigHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<AppDynamicLogConfig>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
