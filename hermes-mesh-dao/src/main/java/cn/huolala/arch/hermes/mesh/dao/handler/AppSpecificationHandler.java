package cn.huolala.arch.hermes.mesh.dao.handler;

import cn.huolala.arch.hermes.mesh.dao.entity.specification.AppSpecification;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;

import java.io.IOException;
import java.util.List;

public class AppSpecificationHandler extends JacksonTypeHandler {

    public AppSpecificationHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            getObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return getObjectMapper().readValue(json, new TypeReference<List<AppSpecification.SpecificationTypeResult>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
