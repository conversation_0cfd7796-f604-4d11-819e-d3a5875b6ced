package cn.huolala.arch.hermes.mesh.dao.handler.inspection;

import cn.huolala.arch.hermes.mesh.dao.entity.inspection.InspectionReport;
import cn.huolala.arch.hermes.mesh.dao.entity.specification.AppSpecification;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.List;

public class ComponentReportHandler extends JacksonTypeHandler {

    public ComponentReportHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<InspectionReport.ComponentReport>>() {
            });
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
