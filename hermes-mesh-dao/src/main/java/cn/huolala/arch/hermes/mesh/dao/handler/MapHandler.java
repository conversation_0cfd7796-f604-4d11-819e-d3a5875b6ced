package cn.huolala.arch.hermes.mesh.dao.handler;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.Map;

public class MapHandler extends JacksonTypeHandler {

    public MapHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<Map<String,String>>() {});
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }
}
