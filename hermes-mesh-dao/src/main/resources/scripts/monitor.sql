# Create Database
CREATE DATABASE IF NOT EXISTS SOAAdminMonitor DEFAULT CHARACTER SET = utf8mb4;
Use SOAAdminMonitor;
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app`;
CREATE TABLE `app` (
                        `id` bigint unsigned not null auto_increment comment '主键',
                        `instance_id` varchar(128) not null comment 'instance_id',
                        `app_id` varchar(128) not null comment 'appId',

                        `host` varchar(32) not null comment '应用ip',
                        `port` int not null default 0 comment '应用端口',
                        `context_path` varchar(256)  comment '服务路径',
                        `register` int not null default 1 comment '1：注册，0：不注册',
                        `subscribe` int not null default 1 comment '1：订阅，0：不订阅',
                        `pid` int not null default 0 comment '进程号',
                        `start_time` bigint not null  comment '启动时间',
                        `warm_up_time` int not null default 0 comment '预热时间',
                        `weight` int not null default 0 comment '服务权重',
                        `custom_tags` json  comment '自定义标签',
                        `metadata` json  comment '自定义元数据',

                        `deleted` int not null default 0 comment '1: deleted, 0: normal',
                        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        PRIMARY KEY (`id`),
                        KEY `ix_instance_id` (`instance_id`),
                        KEY `ix_app_id` (`app_id`),
                        KEY `ix_updated_at` (`updated_at`),
                        KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app实例表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_metadata`;
CREATE TABLE `app_metadata` (
                                     `id` bigint unsigned not null auto_increment comment '主键',

                                     `instance_id` varchar(128)  not null comment 'instance_id',
                                     `app_id` varchar(128)  not null comment 'appId',
                                     `app_config` json comment 'app配置信息',
                                     `rpc_config` json comment 'rpc配置信息',
                                     `system_config` json comment '系统配置信息',

                                     `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (`id`),
                                     KEY `ix_instance_id` (`instance_id`),
                                     KEY `ix_app_id` (`app_id`),
                                     KEY `ix_updated_at` (`updated_at`),
                                     KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app配置原数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_metadata`;
CREATE TABLE `api_metadata` (
                                `id` bigint unsigned not null auto_increment comment '主键',

                                `instance_id` varchar(128)  not null comment 'instance_id',
                                `app_id` varchar(128)  not null comment 'appId',

                                `api_type` varchar(32)  not null comment '类型',
                                `metadata` json comment 'metadata',

                                `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `ix_instance_id` (`instance_id`),
                                KEY `ix_app_id` (`app_id`),
                                KEY `ix_updated_at` (`updated_at`),
                                KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api原数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_command_key`;
CREATE TABLE `api_command_key` (
                                `id` bigint unsigned not null auto_increment comment '主键',

                                `app_id` varchar(128)  not null comment 'appId',

                                `api_type` varchar(32)  not null comment '类型',
                                `command_key` json comment 'commandKeys',

                                `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `ix_app_id` (`app_id`),
                                KEY `ix_updated_at` (`updated_at`),
                                KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='apiCommandKey数据表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `service_command_key`;
CREATE TABLE `service_command_key` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128)  not null comment 'appId',

                                   `api_type` varchar(32)  not null comment '类型',
                                   `command_key` json comment 'commandKeys',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='serviceCommandKey数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_limit_log`;
CREATE TABLE `api_limit_log` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128) not null comment 'appId',

                                   `name` varchar(256)  not null comment 'commandKey',
                                   `api_type` varchar(32)  not null comment '类型',
                                   `user_id` varchar(128)  not null comment '用户id',
                                   `api_limit` json comment '限流配置',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_name` (`name`(191)),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api限流配置日志数据表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_degrade_log`;
CREATE TABLE `api_degrade_log` (
                                 `id` bigint unsigned not null auto_increment comment '主键',

                                 `app_id` varchar(128) not null comment 'appId',

                                 `name` varchar(256)  not null comment 'commandKey',
                                 `api_type` varchar(32)  not null comment '类型',
                                 `user_id` varchar(128)  not null comment '用户id',
                                 `status` int not null comment '降级配置',

                                 `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `ix_app_id` (`app_id`),
                                 KEY `ix_name` (`name`(191)),
                                 KEY `ix_updated_at` (`updated_at`),
                                 KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api降级配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_circuit_log`;
CREATE TABLE `app_circuit_log` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128) not null comment 'appId',
                                   `user_id` varchar(128)  not null comment '用户id',
                                   `app_config` json comment '熔断配置信息',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app熔断配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_circuit_log`;
CREATE TABLE `api_circuit_log` (
                                 `id` bigint unsigned not null auto_increment comment '主键',

                                 `app_id` varchar(128) not null comment 'appId',

                                 `name` varchar(256)  not null comment 'commandKey',
                                 `user_id` varchar(128)  not null comment '用户id',
                                 `api_config` json comment '熔断配置信息',

                                 `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `ix_app_id` (`app_id`),
                                 KEY `ix_name` (`name`(191)),
                                 KEY `ix_updated_at` (`updated_at`),
                                 KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api熔断配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_circuit_event_log`;
CREATE TABLE `api_circuit_event_log` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128) not null comment 'appId',
                                   `instance_id` varchar(128)  not null comment 'instanceId',

                                   `name` varchar(256)  not null comment 'commandKey',
                                   `status` int not null comment '熔断状态',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_instance_id` (`instance_id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api熔断配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_event`;
CREATE TABLE `app_event` (
                                `id` bigint unsigned not null auto_increment comment '主键',

                                `instance_id` varchar(128)  not null comment 'instance_id',
                                `app_id` varchar(128)  not null comment 'appId',
                                `type` varchar(128)  not null comment '事件类型',
                                `data` json comment '事件数据',

                                `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `ix_app_id` (`app_id`),
                                KEY `ix_instance_id` (`instance_id`),
                                KEY `ix_updated_at` (`updated_at`),
                                KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app事件数据表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_circuit_event`;
CREATE TABLE `api_circuit_event` (
                             `id` bigint unsigned not null auto_increment comment '主键',

                             `instance_id` varchar(128)  not null comment 'instance_id',
                             `app_id` varchar(128)  not null comment 'appId',
                             `command_key` varchar(256)  not null comment 'commandKey',
                             `status` int not null comment '状态,0打开 1关闭',

                             `deleted` int not null default 0 comment '1: deleted, 0: normal',
                             `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             PRIMARY KEY (`id`),
                             KEY `ix_app_id` (`app_id`),
                             KEY `ix_instance_id` (`instance_id`),
                             KEY `ix_command_key` (`command_key`(191)),
                             KEY `ix_updated_at` (`updated_at`),
                             KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api熔断事件数据表';




# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_registry`;
CREATE TABLE `app_registry` (
                       `id` bigint unsigned not null auto_increment comment '主键',
                       `instance_id` varchar(128) not null comment 'instance_id',
                       `app_id` varchar(128) not null comment 'appId',

                       `app_vo` json  comment '应用数据',

                       `deleted` int not null default 0 comment '1: deleted, 0: normal',
                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                       PRIMARY KEY (`id`),
                       KEY `ix_app_id` (`app_id`),
                       KEY `ix_instance_id` (`instance_id`),
                       KEY `ix_updated_at` (`updated_at`),
                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app实例注册表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_registry_event`;
CREATE TABLE `app_registry_event` (
                                `id` bigint unsigned not null auto_increment comment '主键',
                                `instance_id` varchar(128) not null comment 'instance_id',
                                `app_id` varchar(128) not null comment 'appId',

                                `event_type` varchar(128) not null comment '事件类型',
                                `memo` text comment '描述',

                                `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `ix_app_id` (`app_id`),
                                KEY `ix_instance_id` (`instance_id`),
                                KEY `ix_updated_at` (`updated_at`),
                                KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app实例注册事件表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_life_event`;
CREATE TABLE `app_life_event` (
                                      `id` bigint unsigned not null auto_increment comment '主键',
                                      `instance_id` varchar(128) not null comment 'instance_id',
                                      `app_id` varchar(128) not null comment 'appId',

                                      `event_type` varchar(128) not null comment '事件类型',
                                      `memo` text comment '描述',

                                      `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      PRIMARY KEY (`id`),
                                      KEY `ix_app_id` (`app_id`),
                                      KEY `ix_instance_id` (`instance_id`),
                                      KEY `ix_updated_at` (`updated_at`),
                                      KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app生命周期事件表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_custom_event`;
CREATE TABLE `app_custom_event` (
                                  `id` bigint unsigned not null auto_increment comment '主键',
                                  `instance_id` varchar(128) not null comment 'instance_id',
                                  `app_id` varchar(128) not null comment 'appId',

                                  `data` text comment '数据',

                                  `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  PRIMARY KEY (`id`),
                                  KEY `ix_app_id` (`app_id`),
                                  KEY `ix_instance_id` (`instance_id`),
                                  KEY `ix_updated_at` (`updated_at`),
                                  KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app自定义事件表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_timeout_log`;
CREATE TABLE `api_timeout_log` (
                                 `id` bigint unsigned not null auto_increment comment '主键',

                                 `app_id` varchar(128) not null comment 'appId',

                                 `name` varchar(256)  not null comment 'commandKey',
                                 `user_id` varchar(128)  not null comment '用户id',
                                 `api_config` json comment '限流配置',

                                 `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `ix_app_id` (`app_id`),
                                 KEY `ix_name` (`name`(191)),
                                 KEY `ix_updated_at` (`updated_at`),
                                 KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api超时配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_timeout_log`;
CREATE TABLE `app_timeout_log` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128) not null comment 'appId',
                                   `user_id` varchar(128)  not null comment '用户id',
                                   `app_config` json comment '超时配置',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app超时配置日志数据表';



# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_dynamiclog_log`;
CREATE TABLE `api_dynamiclog_log` (
                                   `id` bigint unsigned not null auto_increment comment '主键',

                                   `app_id` varchar(128) not null comment 'appId',

                                   `name` varchar(256)  not null comment 'commandKey',
                                   `user_id` varchar(128)  not null comment '用户id',
                                   `api_config` json comment '限流配置',
                                   `api_type` varchar(32) NOT NULL COMMENT '类型',

                                   `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_app_id` (`app_id`),
                                   KEY `ix_name` (`name`(191)),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api动态日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_dynamiclog_log`;
CREATE TABLE `app_dynamiclog_log` (
                                      `id` bigint unsigned not null auto_increment comment '主键',

                                      `app_id` varchar(128) not null comment 'appId',

                                      `user_id` varchar(128)  not null comment '用户id',
                                      `app_config` json comment '限流配置',

                                      `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      PRIMARY KEY (`id`),
                                      KEY `ix_app_id` (`app_id`),
                                      KEY `ix_updated_at` (`updated_at`),
                                      KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app动态日志数据表';



# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_request`;
CREATE TABLE `api_request` (
                                    `id` bigint unsigned not null auto_increment comment '主键',
                                    `instance_id` varchar(128) not null comment 'instance_id',
                                    `app_id` varchar(128) not null comment 'appId',

                                    `name` varchar(128) not null comment '名称',
                                    `user_id` varchar(128) not null comment 'userId',
                                    `service_name` varchar(256) not null comment 'serviceName',
                                    `method_name` varchar(256) not null comment 'methodName',
                                    `memo` varchar(512)  comment 'userId',
                                    `parameters` json  comment '参数',
                                    `headers` json  comment '请求头',
                                    `attachments` json  comment 'attachment',

                                    `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    KEY `ix_app_id` (`app_id`),
                                    KEY `instance_id` (`instance_id`),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api请求记录表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `third_party_app_domain`;
CREATE TABLE `third_party_app_domain` (
                               `id` bigint unsigned not null auto_increment comment '主键',

                               `app_id` varchar(128) not null comment 'appId',
                               `app_name` varchar(236) not null comment '名称',
                               `domain` varchar(128) not null comment 'userId',

                               `deleted` int not null default 0 comment '1: deleted, 0: normal',
                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               PRIMARY KEY (`id`),
                               KEY `ix_app_id` (`app_id`),
                               KEY `ix_updated_at` (`updated_at`),
                               KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方域名信息记录';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_version`;
CREATE TABLE `app_version`(
                                        `id`          bigint unsigned auto_increment comment '主键',
                                        `app_id`      varchar(128)  not null comment 'appId',
                                        `instance_id` varchar(128)  not null comment 'instance_id',
                                        `version`     json  null comment '组件版本',
                                        `deleted`     int not null default 0  comment '1: deleted, 0: normal',
                                        `updated_at`  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
                                        `created_at`  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
                                        PRIMARY KEY (`id`),
                                        KEY `ix_app_id` (`app_id`),
                                        KEY `ix_instance_id` (`instance_id`),
                                        KEY `ix_updated_at` (`updated_at`),
                                        KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app version信息表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_destroy_log`;
CREATE TABLE `app_destroy_log`(
                              `id`          bigint unsigned auto_increment comment '主键',
                              `app_id`      varchar(128) not null comment 'appId',
                              `host`        varchar(128) not null comment 'host',
                              `status`      int default 0 not null comment '1: success, 0: false',
                              `memo`        varchar(256) comment '备注',

                              `deleted`     int      default 0                 not null comment '1: deleted, 0: normal',
                              `updated_at`  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
                              `created_at`  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
                              PRIMARY KEY (`id`),
                              KEY `ix_app_id` (`app_id`),
                              KEY `ix_updated_at` (`updated_at`),
                              KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app销毁记录表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_auth_app_log`;
CREATE TABLE `api_auth_app_log` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `app_id` varchar(128) NOT NULL COMMENT 'appId',
                                    `name` varchar(256) NOT NULL COMMENT 'name',
                                    `api_type` varchar(32) NOT NULL COMMENT '类型',
                                    `user_id` varchar(128) NOT NULL COMMENT '用户id',
                                    `api_config` json DEFAULT NULL COMMENT '授权信息',
                                    `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    KEY `ix_app_id` (`app_id`),
                                    KEY `ix_name` (`name`(191)),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api授权配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_auth_log`;
CREATE TABLE `api_auth_log` (
                                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `app_id` varchar(128) NOT NULL COMMENT 'appId',
                                `name` varchar(256) NOT NULL COMMENT 'commandKey',
                                `api_type` varchar(32) NOT NULL COMMENT '类型',
                                `user_id` varchar(128) NOT NULL COMMENT '用户id',
                                `status` int(11) NOT NULL COMMENT '鉴权状态',
                                `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `ix_app_id` (`app_id`),
                                KEY `ix_name` (`name`(191)),
                                KEY `ix_updated_at` (`updated_at`),
                                KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api鉴权配置日志数据表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_auth_secret_key`;
CREATE TABLE `api_auth_secret_key` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `app_id` varchar(128) NOT NULL COMMENT 'appId',
                                       `name` varchar(128) NOT NULL COMMENT 'name',
                                       `secret_key` varchar(256) NOT NULL COMMENT 'secretKey',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_app_id_name` (`app_id`,`name`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='api鉴权secretKey数据表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `api_auth_app_config`;
CREATE TABLE `api_auth_app_config` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `name` varchar(128) NOT NULL COMMENT 'serviceName',
                                       `auth_app_id` varchar(128) NOT NULL COMMENT 'appId',
                                       `effective_at` bigint NOT NULL COMMENT '授权生效时间',
                                       `expired_at` bigint NOT NULL COMMENT '授权结束时间',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_name_auth_app_id` (`name`,`auth_app_id`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权appId配置表';



# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `d_lock`;
CREATE TABLE IF NOT EXISTS `d_lock` (
                      `id` bigint unsigned not null auto_increment comment '主键',
                      `t_id` varchar(128) not null DEFAULT '' comment '事务id',
                      `owner` char(128) NOT NULL COMMENT '锁的持有者',
                      `expire` int(11) NOT NULL COMMENT '过期时间，单位为秒',
                      `deleted` int not null default 0 comment '1: deleted, 0: normal',
                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                       PRIMARY KEY (`id`),
                       UNIQUE KEY `ut_id` (`t_id`),
                       KEY `ix_updated_at` (`updated_at`),
                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分布式锁';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `component_check_log`;
CREATE TABLE `component_check_log` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `checker_app_id` varchar(128) NOT NULL COMMENT '发起自检appId',
                                       `type` varchar(128) NOT NULL COMMENT '自检组件',
                                       `check_app_id` json DEFAULT NULL COMMENT '自检appId',
                                       `status` varchar(128) NOT NULL COMMENT '自检结果',
                                       `detail` json DEFAULT NULL COMMENT '自检结果',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       KEY `ix_type` (`type`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件自检日志表';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_soa_coverage`;
CREATE TABLE `metric_soa_coverage` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `time` bigint(20) NOT NULL COMMENT '时间戳',
                                       `link` int(11) NOT NULL DEFAULT '0' COMMENT '链路0:所有链路 1核心链路',
                                       `num` int(11) NOT NULL DEFAULT '0' COMMENT '数量',
                                       `coverage` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '覆盖率',
                                       `data` json NOT NULL COMMENT '覆盖详情',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_time_link` (`time`,`link`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='soa整体覆盖率指标';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_soa_increase`;
CREATE TABLE `metric_soa_increase` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `time` bigint(20) NOT NULL COMMENT '时间戳，day',
                                       `link` int(11) NOT NULL DEFAULT '0' COMMENT '链路: 0: 所有链路 1: 核心链路服务',
                                       `num` int(11) NOT NULL COMMENT '增加数量',
                                       `data` json NOT NULL COMMENT '增加详情',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_time_link` (`time`,`link`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='soa整体日增指标';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_soa_version_coverage`;
CREATE TABLE `metric_soa_version_coverage` (
                                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `time` bigint(20) NOT NULL COMMENT '时间戳',
                                               `version` varchar(128) NOT NULL COMMENT '版本',
                                               `link` int(11) NOT NULL DEFAULT '0' COMMENT '链路0:所有链路 1核心链路',
                                               `num` int(11) NOT NULL DEFAULT '0' COMMENT '数量',
                                               `coverage` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '覆盖率',
                                               `data` json NOT NULL COMMENT '覆盖详情',
                                               `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `uk_time_link_version` (`time`,`link`,`version`),
                                               KEY `ix_updated_at` (`updated_at`),
                                               KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='soa各版本覆盖率指标';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_soa_version_coverage`;
CREATE TABLE `metric_soa_version_increase` (
                                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `time` bigint(20) NOT NULL COMMENT '时间戳',
                                               `version` varchar(128) NOT NULL COMMENT '版本',
                                               `link` int(11) NOT NULL DEFAULT '0' COMMENT '链路0:所有链路 1核心链路',
                                               `num` int(11) NOT NULL COMMENT '增加数量',
                                               `data` json NOT NULL COMMENT '增加详情',
                                               `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `uk_time_link_version` (`time`,`link`,`version`),
                                               KEY `ix_updated_at` (`updated_at`),
                                               KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='soa各版本日增指标';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `user_operate_log`;
CREATE TABLE `user_operate_log` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `user_id` varchar(125) NOT NULL DEFAULT '' COMMENT  '操作用户ID',
                                    `app_id` varchar(125) NOT NULL DEFAULT '' COMMENT '操作APPID',
                                    `type` varchar(125) NOT NULL COMMENT '操作类型',
                                    `data` text NOT NULL COMMENT '操作内容',
                                    `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    KEY `ix_app_id` (`app_id`),
                                    KEY `ix_type` (`type`),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户操作记录合集';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_user_operate`;
CREATE TABLE `metric_user_operate` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `time` bigint(20) NOT NULL COMMENT '时间戳',
                                       `num` int(11) NOT NULL DEFAULT '0' COMMENT '数量',
                                       `type` varchar(125) NOT NULL COMMENT '操作类型',
                                       `data` json NOT NULL COMMENT '操作具体详情',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_time_type` (`time`,`type`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户操作指标';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `app_az_router_log`;
CREATE TABLE `app_az_router_log` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `app_id` varchar(128) NOT NULL COMMENT 'appId',
                                    `user_id` varchar(128) NOT NULL COMMENT '用户id',
                                    `app_config` json DEFAULT NULL COMMENT 'app路由规则',
                                    `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    KEY `ix_app_id` (`app_id`),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='app多泳道路由配置';
# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_check_data_snapshot`;
CREATE TABLE `consul_check_data_snapshot` (
                                     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `time` bigint(20) NOT NULL COMMENT '时间戳',
                                     `type` varchar(128) NOT NULL COMMENT '类型MONITOR,SOA,OTHER,ALL',
                                     `status`  int(11) NOT NULL DEFAULT '0' COMMENT '1: passing, 0: critical',
                                     `size` varchar(128) NOT NULL COMMENT 'check数量大小',
                                     `data` json DEFAULT NULL COMMENT 'check数据',
                                     `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_time_type_status` (`time`,`type`,`status`),
                                     KEY `ix_updated_at` (`updated_at`),
                                     KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul check数据快照';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_check_data_clear_log`;
CREATE TABLE `consul_check_data_clear_log` (
                                              `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `time` bigint(20) NOT NULL COMMENT '时间戳',
                                              `type` varchar(128) NOT NULL COMMENT '类型MONITOR,SOA,OTHER,ALL',
                                              `execute_type` varchar(64) NOT NULL COMMENT '类型TASK,MANUAL',
                                              `user_id` varchar(64) NOT NULL COMMENT '执行者',
                                              `size` varchar(128) NOT NULL COMMENT '清理check数量大小',
                                              `data` json DEFAULT NULL COMMENT '清理check数据',
                                              `fail_data` json DEFAULT NULL COMMENT '清理失败的check数据',
                                              `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_time_type` (`time`,`type`),
                                              KEY `ix_updated_at` (`updated_at`),
                                              KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul check数据清理日志';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `metric_direct_connect_server`;
CREATE TABLE `metric_direct_connect_server` (
                                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `time` bigint(20) NOT NULL COMMENT '时间戳',
                                               `type` varchar(128) NOT NULL COMMENT '类型KV REGISTRY',
                                               `service_num` int(11) NOT NULL COMMENT 'service数量',
                                               `check_num` int(11) NOT NULL COMMENT 'check数量',
                                               `service_data` json DEFAULT NULL COMMENT 'service具体数据',
                                               `check_data` json DEFAULT NULL COMMENT 'check具体数据',
                                               `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `uk_time_type` (`time`,`type`),
                                               KEY `ix_updated_at` (`updated_at`),
                                               KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul 直连server监控数据';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_timer_config`;
CREATE TABLE `consul_timer_config` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `name` varchar(128) NOT NULL COMMENT '任务名字',
                                       `desc` varchar(128) NOT NULL COMMENT '任务描述',
                                       `enabled` int(11) NOT NULL DEFAULT '0' COMMENT '是否生效 1：生效 0：失效',
                                       `alert` int(11) NOT NULL DEFAULT '0' COMMENT '是否通知 1：通知 0：静默',
                                       `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_name` (`name`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul 定时任务配置';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `distributed_lock`;
CREATE TABLE `distributed_lock` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `t_id` varchar(128) NOT NULL DEFAULT '' COMMENT '事务id',
                                    `owner` char(128) NOT NULL COMMENT '锁的持有者',
                                    `expire` int(11) NOT NULL COMMENT '过期时间，单位为秒',
                                    `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_t_id` (`t_id`),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分布式锁';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_service_log`;
CREATE TABLE `consul_service_log` (
                                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '操作人',
                                    `size` int(11) NOT NULL DEFAULT '0' COMMENT '数据大小',
                                    `data` json DEFAULT NULL COMMENT 'service具体数据',
                                    `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`),
                                    KEY `ix_updated_at` (`updated_at`),
                                    KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul service操作日志';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_node_log`;
CREATE TABLE `consul_node_log` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '操作人',
                                   `node_name` varchar(256) DEFAULT NULL COMMENT '节点名字',
                                   `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul节点操作日志';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `consul_navigation`;
CREATE TABLE `consul_navigation` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `name` varchar(128) NOT NULL DEFAULT '' COMMENT '导航名字',
                                   `link` varchar(256) DEFAULT NULL COMMENT '导航链接',
                                   `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='consul快速导航';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `inspection_rule`;
CREATE TABLE `inspection_rule` (
                                     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `name` varchar(128) NOT NULL DEFAULT '' COMMENT '子项名称',

                                     `component` varchar(64) DEFAULT NULL COMMENT '自检组件',
                                     `type` varchar(64) NOT NULL DEFAULT '' COMMENT '指标类型',
                                     `script` text NOT NULL COMMENT '自检脚本',
                                     `memo` text NOT NULL COMMENT '自检描述',
                                     `scene` varchar(64) DEFAULT NULL COMMENT '自检场景',
                                     `_order` int not null comment '顺序',
                                     `enabled` int not null default 0 comment '1: 禁用, 0: 正常',

                                     `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_name` (`name`),
                                     KEY `ix_updated_at` (`updated_at`),
                                     KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自检规则表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `inspection_report`;
CREATE TABLE `inspection_report` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

                                   `status` varchar(128) NOT NULL DEFAULT '' COMMENT '自检状态',
                                   `scene` varchar(64) DEFAULT NULL COMMENT '自检场景',
                                   `components` json DEFAULT NULL COMMENT '组件详情',
                                   `param_context` json DEFAULT NULL COMMENT '自检参数',
                                   `fail_rules` json DEFAULT NULL COMMENT '失败的规则',
                                   `fail_rules_extra` json DEFAULT NULL COMMENT '失败的规则扩展支持',

                                   `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '1: deleted, 0: normal',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`),
                                   KEY `ix_scene` (`scene`),
                                   KEY `ix_updated_at` (`updated_at`),
                                   KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自检报告表';

# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `autopilot_task`;
CREATE TABLE `autopilot_task` (
                                       `id` bigint unsigned not null auto_increment comment '主键',

                                       `name` varchar(128) not null comment 'name',
                                       `cron` varchar(64)  not null comment 'cron规则',
                                       `memo` text NOT NULL COMMENT '自检描述',
                                       `enabled` int not null default 0 comment '1: 禁用, 0: 正常',
                                       `script` text NOT NULL COMMENT '执行脚本',

                                       `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`),
                                       KEY `ix_name` (`name`),
                                       KEY `ix_updated_at` (`updated_at`),
                                       KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动驾驶任务表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `autopilot_api_config`;
CREATE TABLE `autopilot_api_config` (
                                      `id` bigint unsigned not null auto_increment comment '主键',

                                      `app_id` varchar(128) not null comment 'appId',
                                      `api_type` varchar(32)  not null comment '类型',
                                      `name` varchar(256)  not null comment 'commandKey',
                                      `cluster` varchar(64)  not null default 'default' comment 'cluster',
                                      `governance_config` json comment '智能服务治理推荐配置',

                                      `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      PRIMARY KEY (`id`),
                                      KEY `ix_app_id` (`app_id`),
                                      KEY `ix_updated_at` (`updated_at`),
                                      KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ck服务治理配置推荐归档表';


# ----------------------------------------------------------------------------------------------------------------------
DROP TABLE IF EXISTS `script_param`;
CREATE TABLE `script_param` (
                                 `id` bigint unsigned not null auto_increment comment '主键',

                                 `name` varchar(128) not null comment '参数名称',
                                 `value` text not null comment '参数值',
                                 `component` varchar(64) DEFAULT NULL COMMENT '自检组件',
                                 `memo` varchar(256)  comment '描述',

                                 `deleted` int not null default 0 comment '1: deleted, 0: normal',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_name` (`name`),
                                KEY `ix_component` (`component`),
                                 KEY `ix_updated_at` (`updated_at`),
                                 KEY `ix_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本参数信息表';