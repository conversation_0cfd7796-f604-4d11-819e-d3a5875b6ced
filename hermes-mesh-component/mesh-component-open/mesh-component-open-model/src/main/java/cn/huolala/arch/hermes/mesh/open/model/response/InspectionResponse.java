package cn.huolala.arch.hermes.mesh.open.model.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class InspectionResponse extends BaseResponse{

    /**
     * 团队
     */
    private final String team = "SOA";

    /**
     *  组件列表
     */
    private List<Component> components;

    /**
     * Ai 组件汇总
     */
    private AiInspectionReport componentsSummary;


    @Data
    public static class Component{

        /**
         * 组件名称
         */
        @JsonProperty(value = "component_name")
        private String componentName;

        /**
         * 区域
         */
        private String region;

        /**
         *集群列表
         */
        private List<Cluster> clusters;
    }

    @Data
    public static class Cluster{

        /**
         * 集群名称
         */
        @JsonProperty(value = "cluster_name")
        private String clusterName;


        /**
         * 检查子项
         */
        @JsonProperty(value = "check_items")
        private List<CheckItem> checkItems;

    }


    @Data
    public static class CheckItem {

        /**
         * 子项ID
         */
        private Long id;

        /**
         * 子项名称
         */
        @JsonProperty(value = "item_name")
        private String itemName;

        /**
         * 状态
         */
        private String status;

        /**
         * 详情
         */
        private String details;

        /**
         * 扩展数据
         */
        private Map<String,Object> extra;
    }

    @Data
    public static class AiInspectionReport {

        /**
         * 组件状态总览
         */
        private List<ComponentOverview> componentOverview;

        /**
         * 异常与应急操作明细
         */
        private List<ExceptionAndActionDetail> exceptionAndActionDetails;

        /**
         * 自我验证结果
         */
        private SelfValidationResult selfValidationResult;


        @Data
        public static class ComponentOverview {

            private String componentName;

            private String status;

            private String description;

        }

        @Data
        public static class ExceptionAndActionDetail {

            private String componentName;

            private String abnormalMetric;

            private String reason;

            private List<String> emergencyActions;
        }


        @Data
        public static class SelfValidationResult {

            private String missingDangerWarningMetrics;

            private String dataContradictionsExist;

            private String emergencyActionsOperable;

            private String finalConclusion;
        }
    }



}
