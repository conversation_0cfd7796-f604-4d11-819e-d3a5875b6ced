package cn.huolala.arch.hermes.mesh.component.sso.service;

import cn.huolala.arch.hermes.mesh.component.sso.config.SsoProperties;
import cn.huolala.arch.hermes.mesh.component.sso.model.AuthRequest;
import cn.huolala.arch.hermes.mesh.component.sso.model.AuthUser;
import cn.huolala.arch.hermes.mesh.component.sso.model.SsoResponse;
import cn.huolala.arch.hermes.mesh.component.sso.rpc.RpcSsoService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.json.JSONUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import retrofit2.Response;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DefaultSsoFacadeService implements SsoFacadeService {

    @Resource
    private SsoProperties ssoProperties;

    @Resource
    private RpcSsoService rpcSsoService;

    protected LoadingCache<String, AuthUser> loadingCache;

    private static final String SSO_LOGIN_URL = "/login";

    private static final String SSO_LAYOUT_URL = "/logout";

    private static final int DEFAULT_EXPIRE_CACHE = 24 * 60 * 60;

    public DefaultSsoFacadeService(){
        this.loadingCache = Caffeine.newBuilder()
                .expireAfterAccess(DEFAULT_EXPIRE_CACHE, TimeUnit.SECONDS)
                .build(cacheLoader());
    }

    @Override
    public AuthUser verify(String identifier) {
        return loadingCache.get(identifier);
    }

    @Override
    public String loginUrl() {
        return buildUrl(SSO_LOGIN_URL);
    }

    @Override
    public String logoutUrl(String identifier) {
        loadingCache.invalidate(identifier);
        return buildUrl(SSO_LAYOUT_URL);
    }

    private String buildUrl(String path) {
        long currentSeconds = DateUtil.currentSeconds();
        String sign = buildUrlSign(currentSeconds);
        return StrBuilder.create(ssoProperties.getAddress())
                .append(path)
                .append("?")
                .append("callback")
                .append("=")
                .append(ssoProperties.getCallback())
                .append("&")
                .append("appid")
                .append("=")
                .append(ssoProperties.getAppId())
                .append("&")
                .append("_t")
                .append("=")
                .append(currentSeconds)
                .append("&")
                .append("_sign")
                .append("=")
                .append(sign)
                .toString();
    }

    private String buildUrlSign(long currentSeconds) {
        return DigestUtils.md5DigestAsHex(StrBuilder.create()
                .append(ssoProperties.getSecret())
                .append("_t")
                .append(currentSeconds)
                .append("appid")
                .append(ssoProperties.getAppId())
                .append("callback")
                .append(ssoProperties.getCallback())
                .append(ssoProperties.getSecret())
                .toString()
                .getBytes())
                .toUpperCase();
    }

    private String buildVerifySign(long currentSeconds,String identifier) {
        return DigestUtils.md5DigestAsHex(StrBuilder.create()
                .append(ssoProperties.getSecret())
                .append("_t")
                .append(currentSeconds)
                .append("appid")
                .append(ssoProperties.getAppId())
                .append("identifier")
                .append(identifier)
                .append(ssoProperties.getSecret())
                .toString()
                .getBytes())
                .toUpperCase();
    }


    private CacheLoader<String,AuthUser> cacheLoader() {
        return new CacheLoader<String,AuthUser>() {
            @Override
            public AuthUser load(String identifier){
                long currentSeconds = DateUtil.currentSeconds();
                AuthRequest request = new AuthRequest();
                request.setAppid(ssoProperties.getAppId());
                request.setIdentifier(identifier);
                request.set_t(currentSeconds);
                request.set_sign(buildVerifySign(currentSeconds,identifier));
                try {
                    Response<SsoResponse<AuthUser>> response = rpcSsoService.verify(JSONUtil.parseObj(request));
                    Assert.notNull(response.body());
                    return response.body().getData();
                }catch (Exception e){
                    log.error("verify user identifier error, name:{},",identifier,e);
                    return null;
                }
            }
        };
    }
}
