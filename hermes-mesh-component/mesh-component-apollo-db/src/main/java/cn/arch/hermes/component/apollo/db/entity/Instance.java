package cn.arch.hermes.component.apollo.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


@Data
@TableName(value = "Instance")
public class Instance extends BaseEntity{

  @TableField("AppId")
  private String appId;

  @TableField("ClusterName")
  private String clusterName;

  @TableField("NamespaceName")
  private String namespaceName;

  @TableField("Ip")
  private String ip;

  @TableField("ReleaseKey")
  private String releaseKey;

  @TableField("ReleaseDeliveryTime")
  private Date releaseDeliveryTime;

}
