package cn.huolala.arch.hermes.mesh.component.cmdb.service.impl;

import cn.huolala.arch.hermes.mesh.component.cmdb.config.CMDBProperties;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.UserApp;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.request.UserAppCmdbRequest;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.response.CmdbResponse;
import cn.huolala.arch.hermes.mesh.component.cmdb.rpc.RpcCmdbService;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.UserAppInfoService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.CacheLoader;
import retrofit2.Response;

import java.util.Map;

public class DefaultUserAppInfoService extends AbstractCacheService<UserApp> implements UserAppInfoService {

    public DefaultUserAppInfoService(CMDBProperties cmdbProperties, RpcCmdbService rpcCmdbService) {
        super(cmdbProperties,rpcCmdbService);
    }

    @Override
    public UserApp userApps(String username) {
        return loadingCache.get(username);
    }


    @Override
    protected int expire() {
        return cmdbProperties.getUserAppInfoExpire();
    }


    @Override
    protected CacheLoader<String, UserApp> cacheLoader() {
        return new CacheLoader<>() {
            @Override
            public UserApp load( String username) {
                UserAppCmdbRequest request = new UserAppCmdbRequest();
                request.setEname(StrUtil.removePrefix(cmdbProperties.getKey(), "app-"));
                request.setAppkey(cmdbProperties.getKey());
                request.setTime(DateUtil.current());
                request.setSearchtext(username);
                request.setRegion(cmdbProperties.getRegion());
                request.setOffset(0);
                request.setSteps(10000);
                Map<String, Object> paramsMap =serialize(request,new TypeReference<>() {});
                request.setSign(sign(paramsMap, cmdbProperties.getSecret()));
                Response<CmdbResponse<UserApp>> response = rpcCmdbService.userApps(request);
                if (response.isSuccessful() && response.body() != null && response.body().success()) {
                    return response.body().getData();
                }
                return new UserApp();
            }
        };
    }
}
