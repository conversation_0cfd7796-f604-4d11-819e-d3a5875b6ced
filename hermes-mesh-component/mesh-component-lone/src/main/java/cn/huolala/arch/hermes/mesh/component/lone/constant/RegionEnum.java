package cn.huolala.arch.hermes.mesh.component.lone.constant;

import java.util.Locale;
import java.util.Objects;

public enum RegionEnum {
    CHINA(1, "ch"),
    SINGAPORE(2, "sg"),
    INDIA(3, "in"),
    LATIN_AMERICA(4, "br"),
    NORTH_AMERICA(5, "ous");

    private final int code;
    private final String abbreviation;

    RegionEnum(int code, String abbreviation) {
        this.code = code;
        this.abbreviation = abbreviation;
    }

    public int getCode() {
        return code;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public static String codeToAbbreviation(int code) {
        for (RegionEnum value : RegionEnum.values()) {
            if (value.getCode() == code) {
                return value.abbreviation;
            }
        }
        //default region is ch
        return CHINA.abbreviation;
    }

    public static int abbreviationToCode(String abbreviation) {
        String ab = abbreviation.toLowerCase(Locale.ROOT);
        //admin环境设置为CN
        ab = Objects.equals("cn", ab) ? "ch" : ab;
        for (RegionEnum value : RegionEnum.values()) {
            if (Objects.equals(value.getAbbreviation(), ab)) {
                return value.code;
            }
        }
        //default region is ch
        return CHINA.code;
    }

    public static RegionEnum codeToRegion(int code) {
        for (RegionEnum value : RegionEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        //default region is ch
        return CHINA;
    }
}
