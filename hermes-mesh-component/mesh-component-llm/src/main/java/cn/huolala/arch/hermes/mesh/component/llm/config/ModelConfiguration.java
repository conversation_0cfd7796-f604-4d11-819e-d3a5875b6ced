package cn.huolala.arch.hermes.mesh.component.llm.config;

import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ModelConfiguration {

    @Value("${spring.ai.qwen.api-key}")
    private String qwenApiKey;

    @Value("${spring.ai.qwen.endpoint}")
    private String qwenEndpoint;

    @Value("${spring.ai.qwen.model}")
    private String qwenModel;

    @Value("${spring.ai.deepseek.api-key}")
    private String deepseekApiKey;

    @Value("${spring.ai.deepseek.endpoint}")
    private String deepseekEndpoint;

    @Value("${spring.ai.deepseek.model}")
    private String deepseekModel;

    @Bean(name = "deepseekChatModel")
    public ChatModel deepseekChatModel() {
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(deepseekEndpoint)
                .apiKey(deepseekApiKey)
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(deepseekModel)
                .temperature(0.7)
                .build();
        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();
    }

    @Bean(name = "qwenChatModel")
    public ChatModel qwenChatModel() {
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(qwenEndpoint)
                .apiKey(qwenApiKey)
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(qwenModel)
                .temperature(0.7)
                .build();
        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();
    }
}
