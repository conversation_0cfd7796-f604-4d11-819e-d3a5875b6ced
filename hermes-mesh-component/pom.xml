<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hll-hermes-mesh</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>hermes-mesh-component</artifactId>
    <packaging>pom</packaging>


    <modules>
        <module>mesh-component-llm</module>
        <module>mesh-component-cmdb</module>
        <module>mesh-component-consul</module>
        <module>mesh-component-lalaplat</module>
        <module>mesh-component-lark</module>
        <module>mesh-component-apollo</module>
        <module>mesh-component-apollo-db</module>
        <module>mesh-component-oss</module>
        <module>mesh-component-sso</module>
        <module>mesh-component-lock</module>
        <module>mesh-component-open</module>
        <module>mesh-component-proxy</module>
        <module>mesh-component-lone</module>
        <module>mesh-component-moc</module>
        <module>mesh-component-kali</module>
        <module>mesh-component-accesslog</module>
    </modules>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>


</project>
