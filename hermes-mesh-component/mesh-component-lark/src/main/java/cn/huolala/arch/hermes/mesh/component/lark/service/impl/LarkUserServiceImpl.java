package cn.huolala.arch.hermes.mesh.component.lark.service.impl;

import cn.huolala.arch.hermes.mesh.component.lark.config.LarkProperties;
import cn.huolala.arch.hermes.mesh.component.lark.model.BaseRespMsg;
import cn.huolala.arch.hermes.mesh.component.lark.model.LarkUserBaseInfo;
import cn.huolala.arch.hermes.mesh.component.lark.model.LarkUserInfo;
import cn.huolala.arch.hermes.mesh.component.lark.model.RespData;
import cn.huolala.arch.hermes.mesh.component.lark.model.user.UserBaseInfoReq;
import cn.huolala.arch.hermes.mesh.component.lark.model.user.UserBaseInfoResp;
import cn.huolala.arch.hermes.mesh.component.lark.model.user.UserInfoResp;
import cn.huolala.arch.hermes.mesh.component.lark.rpc.RpcLarkService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkAccessTokenService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkUserService;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.authen.v1.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import retrofit2.Response;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class LarkUserServiceImpl extends AbstractCacheService<LarkUserInfo> implements LarkUserService {

    private static final Logger logger = LoggerFactory.getLogger(LarkGroupServiceImpl.class);

    private static final String HLL_EMAIL_SUFFIX = "@huolala.cn";

    private final LarkAccessTokenService larkAccessTokenService;

    private final Client larkOpenApiClient;


    private final LoadingCache<String, LarkUserInfo> OPENID_TO_USERINFO = Caffeine.newBuilder()
            .expireAfterWrite(larkProperties.getUserLarkInfoExpire(), TimeUnit.SECONDS)
            .build(this::getUserInfoByOpenId);

    public LarkUserServiceImpl(LarkProperties larkProperties, Client larkOpenApiClient, RpcLarkService rpcLarkService, LarkAccessTokenService larkAccessTokenService) {
        super(larkProperties, rpcLarkService);
        this.larkOpenApiClient = larkOpenApiClient;
        this.larkAccessTokenService = larkAccessTokenService;
    }


    @Override
    public String getOpenIdByEName(String eName) {
        UserBaseInfoReq userBaseInfoReq = new UserBaseInfoReq();
        userBaseInfoReq.setEmails(ListUtil.toList(eName + HLL_EMAIL_SUFFIX));
        LarkUserBaseInfo userBaseInfo = getUserBaseInfo(userBaseInfoReq);
        if (Objects.nonNull(userBaseInfo)) {
            //the userId here is openId
            return userBaseInfo.getUserId();
        }
        return StrUtil.EMPTY;
    }


    @Override
    public String getOpenIdByPhone(String phone) {
        UserBaseInfoReq userBaseInfoReq = new UserBaseInfoReq();
        userBaseInfoReq.setEmails(ListUtil.toList(phone));
        LarkUserBaseInfo userBaseInfo = getUserBaseInfo(userBaseInfoReq);
        if (Objects.nonNull(userBaseInfo)) {
            //the userId here is openId
            return userBaseInfo.getUserId();
        }
        return StrUtil.EMPTY;
    }


    @Override
    public LarkUserInfo userInfoByOpenId(String openId) {
        if (StrUtil.isEmpty(openId)) {
            return null;
        }
        return OPENID_TO_USERINFO.get(openId);
    }

    private LarkUserInfo getUserInfoByOpenId(String openId) {
        Response<RespData<UserInfoResp>> resp = rpcLarkService.userInfoByOpenId(openId, larkAccessTokenService.tenantAccessTokenHeader());
        if (resp.isSuccessful() && Objects.nonNull(resp.body()) && resp.body().isSuccess()) {
            RespData<UserInfoResp> userInfoRespRespData = resp.body();
            UserInfoResp userInfoResp = userInfoRespRespData.getData();
            if (Objects.nonNull(userInfoResp)) {
                return userInfoResp.getUser();
            }
        }
        return null;
    }

    @Override
    public LarkUserInfo userInfoByName(String enName) {
        return this.loadingCache.get(enName);
    }

    @Override
    public CreateRefreshAccessTokenRespBody refreshAccessToken(String refreshToken) {
        try {
            CreateRefreshAccessTokenResp resp = this.larkOpenApiClient.authen().refreshAccessToken()
                    .create(CreateRefreshAccessTokenReq.newBuilder().createRefreshAccessTokenReqBody(CreateRefreshAccessTokenReqBody.newBuilder()
                                    .refreshToken(refreshToken)
                                    .grantType("refresh_token")
                                    .build()).build(),
                            RequestOptions.newBuilder().appAccessToken(larkAccessTokenService.appAccessToken()).build());
            if (BaseRespMsg.isSuccess(resp.getCode())) {
                return resp.getData();
            } else {
                logger.error("refreshAccessToken error: {}", JSONUtil.toJsonStr(resp));
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public CreateAccessTokenRespBody accessTokenByCode(String code) {
        try {
            CreateAccessTokenResp resp = this.larkOpenApiClient.authen().accessToken()
                    .create(CreateAccessTokenReq.newBuilder().createAccessTokenReqBody(CreateAccessTokenReqBody.newBuilder()
                                    .code(code).grantType("authorization_code").build())
                            .build(), RequestOptions.newBuilder().appAccessToken(larkAccessTokenService.appAccessToken()).build());

            if (BaseRespMsg.isSuccess(resp.getCode())) {
                logger.info("accessTokenByCode success, userId: {}", resp.getData().getUserId());
                return resp.getData();
            } else {
                logger.error("accessTokenByCode error: {}", JSONUtil.toJsonStr(resp));
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private LarkUserBaseInfo getUserBaseInfo(UserBaseInfoReq userBaseInfoReq) {
        Response<RespData<UserBaseInfoResp>> respMsgResponse = rpcLarkService.userBaseInfoByEmail(
                userBaseInfoReq, larkAccessTokenService.tenantAccessTokenHeader());
        if (respMsgResponse.isSuccessful() && Objects.nonNull(respMsgResponse.body()) && respMsgResponse.body().isSuccess()) {
            UserBaseInfoResp data = respMsgResponse.body().getData();
            if (Objects.nonNull(data) && data.getUserList().size() == 1) {
                return data.getUserList().get(0);
            }
        }
        return null;
    }

    @Override
    protected int expire() {
        return larkProperties.getUserLarkInfoExpire();
    }

    @Override
    protected CacheLoader<String, LarkUserInfo> cacheLoader() {
        return new CacheLoader<>() {
            @Override
            public LarkUserInfo load(String eName) throws Exception {
                return userInfoByOpenId(getOpenIdByEName(eName));
            }
        };
    }
}
