package cn.huolala.arch.hermes.mesh.component.lark.notify.aspect;

import cn.huolala.arch.hermes.mesh.component.lark.notify.converter.*;
import cn.huolala.arch.hermes.mesh.component.lark.notify.notifier.DefaultNotifier;
import cn.huolala.arch.hermes.mesh.component.lark.notify.notifier.Notifier;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkFacadeService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.HashMap;
import java.util.Map;


/**
 * 自定义的LarkNotify通知方法
 */
public class LarkNotifyHolder implements InitializingBean, ApplicationContextAware {

    private static final Map<Class<? extends Notifier>, Notifier> NOTIFIER_MAP = new HashMap<>();

    private static final Map<Class<? extends Converter>, Converter<? extends Object>> CONVERTER_MAP = new HashMap<>();

    private ApplicationContext applicationContext;


    private final LarkFacadeService larkFacadeService;

    public LarkNotifyHolder(LarkFacadeService larkFacadeService) {
        this.larkFacadeService = larkFacadeService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        collectNotifier();
        collectConverter();
    }

    private void collectConverter() {
        String[] beanNames = applicationContext.getBeanNamesForType(Converter.class);
        CONVERTER_MAP.put(DefaultConverter.class, new DefaultConverter());
        CONVERTER_MAP.put(HtmlConverter.class, new HtmlConverter(larkFacadeService));
        CONVERTER_MAP.put(ATUserConverter.class, new ATUserConverter());
        CONVERTER_MAP.put(StwichConverter.class, new StwichConverter());
        CONVERTER_MAP.put(DateConverter.class, new DateConverter());
        for (String beanName : beanNames) {
            Converter<?> bean = (Converter<?>) applicationContext.getBean(beanName);
            CONVERTER_MAP.put(bean.getClass(), bean);
        }
    }

    private void collectNotifier() {
        String[] beanNames = applicationContext.getBeanNamesForType(Notifier.class);
        for (String beanName : beanNames) {
            Notifier bean = (Notifier) applicationContext.getBean(beanName);
            NOTIFIER_MAP.put(bean.getClass(), bean);
        }
        NOTIFIER_MAP.put(DefaultNotifier.class, new DefaultNotifier(larkFacadeService));
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static Notifier getNotifier(Class<?> notifierClass) {
        return NOTIFIER_MAP.get(notifierClass);
    }

    public static Converter<?> getConverter(Class<?> converterClass) {
        return CONVERTER_MAP.get(converterClass);
    }
}
