package cn.huolala.arch.hermes.mesh.component.lark.service.impl;

import cn.huolala.arch.hermes.mesh.component.lark.config.LarkProperties;
import cn.huolala.arch.hermes.mesh.component.lark.model.LarkUserInfo;
import cn.huolala.arch.hermes.mesh.component.lark.model.RespData;
import cn.huolala.arch.hermes.mesh.component.lark.model.common.PageItemsResp;
import cn.huolala.arch.hermes.mesh.component.lark.model.department.DepartmentInfo;
import cn.huolala.arch.hermes.mesh.component.lark.rpc.RpcLarkService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkAccessTokenService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkDepartmentService;
import cn.hutool.json.JSONUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import retrofit2.Response;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

public class LarkDepartmentServiceImpl extends AbstractCacheService<List<LarkUserInfo>> implements LarkDepartmentService {
    private static final Logger logger = LoggerFactory.getLogger(LarkGroupServiceImpl.class);

    LarkAccessTokenService larkAccessTokenService;

    public LarkDepartmentServiceImpl(LarkProperties larkProperties, RpcLarkService rpcLarkService, LarkAccessTokenService larkAccessTokenService) {
        super(larkProperties, rpcLarkService);
        this.larkAccessTokenService = larkAccessTokenService;
    }

    @Override
    public List<LarkUserInfo> departmentUsers(String departmentId) {
        List<LarkUserInfo> userInfos = new ArrayList<>();
        Map<String, String> queryParams = new HashMap<>();
        final AtomicReference<String> pageToken = new AtomicReference<>();
        queryParams.put("page_size", "100");
        queryParams.put("department_id", departmentId);
        while (true) {
            Optional.ofNullable(pageToken.get()).ifPresent(page_token -> queryParams.put("page_token", page_token));
            Response<RespData<PageItemsResp<LarkUserInfo>>> respDataResponse = rpcLarkService.departmentUsers(queryParams, larkAccessTokenService.tenantAccessTokenHeader());
            if (respDataResponse.isSuccessful() && Objects.nonNull(respDataResponse.body()) && respDataResponse.body().isSuccess()) {
                PageItemsResp<LarkUserInfo> data = respDataResponse.body().getData();
                Optional.ofNullable(data.getItems()).ifPresent(userInfos::addAll);
                pageToken.set(data.getPageToken());
                if (!data.getHasMore()) {
                    break;
                }
            } else {
                logger.info("department users request fail: {}", JSONUtil.toJsonStr(respDataResponse));
                break;
            }
        }
        return userInfos;
    }

    @Override
    public List<DepartmentInfo> departmentInfoList(String departmentId) {
        List<DepartmentInfo> departmentInfos = new ArrayList<>();
        final AtomicReference<String> pageToken = new AtomicReference<>();
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("page_size", "50");
        queryParams.put("user_id_type", "open_id");
        queryParams.put("fetch_child", "true");
        queryParams.put("department_id_type", "open_department_id");
        queryParams.put("parent_department_id", departmentId);
        while (true) {
            Optional.ofNullable(pageToken.get()).ifPresent(page_token -> queryParams.put("page_token", page_token));
            Response<RespData<PageItemsResp<DepartmentInfo>>> response = rpcLarkService.departmentInfoList(queryParams, larkAccessTokenService.tenantAccessTokenHeader());
            if (response.isSuccessful() && Objects.nonNull(response.body()) && response.body().isSuccess()) {
                PageItemsResp<DepartmentInfo> data = response.body().getData();
                departmentInfos.addAll(data.getItems());
                pageToken.set(data.getPageToken());
                if (!data.getHasMore()) {
                    break;
                }
            } else {
                logger.info("department info list request fail: {}", JSONUtil.toJsonStr(response));
                break;
            }
        }
        return departmentInfos;
    }

    @Override
    protected int expire() {
        return larkProperties.getDepartmentUsersExpire();
    }

    @Override
    protected CacheLoader<String, List<LarkUserInfo>> cacheLoader() {
        return new CacheLoader<String, List<LarkUserInfo>>() {
            @Override
            public List<LarkUserInfo> load( String departmentId) throws Exception {
                return departmentUsers(departmentId);
            }
        };
    }
}
