package cn.huolala.arch.hermes.mesh.component.lark.service.impl;

import cn.huolala.arch.hermes.mesh.component.lark.config.LarkProperties;
import cn.huolala.arch.hermes.mesh.component.lark.model.RespData;
import cn.huolala.arch.hermes.mesh.component.lark.model.document.SpreadSheetsCellData;
import cn.huolala.arch.hermes.mesh.component.lark.model.document.SpreadSheetsMetaData;
import cn.huolala.arch.hermes.mesh.component.lark.rpc.RpcLarkService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkAccessTokenService;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkSpreadSheetService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class LarkSpreadSheetServiceImpl extends AbstractCacheService<Map<String, List<List<String>>>> implements LarkSpreadSheetService {

    private final LarkAccessTokenService larkAccessTokenService;

    private final LoadingCache<String, SpreadSheetsMetaData> sheetsMetadataCache;


    public LarkSpreadSheetServiceImpl(LarkProperties larkProperties, RpcLarkService rpcLarkService, LarkAccessTokenService larkAccessTokenService) {
        super(larkProperties, rpcLarkService);
        this.larkAccessTokenService = larkAccessTokenService;
        this.sheetsMetadataCache = Caffeine.newBuilder().expireAfterWrite(expire(), TimeUnit.SECONDS).build(initMetaDataCache());
    }

    @Override
    public SpreadSheetsMetaData getMetadata(String spreadsheetToken) {
        Response<RespData<SpreadSheetsMetaData>> respDataResponse = rpcLarkService.spreadSheetsMetaData(spreadsheetToken, larkAccessTokenService.tenantAccessTokenHeader());
        if (respDataResponse.isSuccessful()) {
            RespData<SpreadSheetsMetaData> body = respDataResponse.body();
            assert body != null;
            if (body.isSuccess()) {
                return body.getData();
            }
        }
        return null;
    }

    @Override
    public SpreadSheetsMetaData getMetadataCache(String spreadsheetToken) {
        return this.sheetsMetadataCache.get(spreadsheetToken, token -> this.getMetadata(spreadsheetToken));
    }


    @Override
    public Map<String, List<List<String>>> getMultiCellValues(String spreadsheetToken, String... range) {
        Map<String, List<List<String>>> values = new HashMap<>();
        SheetContentCacheKeyModel keyModel = new SheetContentCacheKeyModel();
        keyModel.setColumn(range);
        keyModel.setSheetToken(spreadsheetToken);
        try {
            Response<RespData<SpreadSheetsCellData>> respDataResponse = rpcLarkService.spreadSheetsBatchCellData(keyModel.getSheetToken(),
                    larkAccessTokenService.tenantAccessTokenHeader(), StrUtil.join(",", Arrays.stream(keyModel.getColumn()).toArray()));
            if (respDataResponse.isSuccessful()) {
                RespData<SpreadSheetsCellData> body = respDataResponse.body();
                assert body != null;
                SpreadSheetsCellData data = body.getData();
                Optional.ofNullable(data.getValueRanges()).ifPresent(valueRanges -> valueRanges.forEach(valueRange -> values.put(valueRange.getRange(), valueRange.getValues())));
            }
        } catch (Exception e) {
            log.error("获取表格数据失败：" + keyModel, e);
        }
        return values;
    }

    @Override
    public Map<String, List<List<String>>> getMultiCellValuesCache(String spreadsheetToken, String... range) {
        SheetContentCacheKeyModel keyModel = new SheetContentCacheKeyModel();
        keyModel.setColumn(range);
        keyModel.setSheetToken(spreadsheetToken);
        return this.loadingCache.get(JSONUtil.toJsonStr(keyModel));
    }

    @Override
    public Map<String, List<List<String>>> getMultiColumnAllValuesCache(String spreadsheetToken, String sheetTitle, int beginRow, String... column) {
        SpreadSheetsMetaData metadata = this.getMetadataCache(spreadsheetToken);
        if (Objects.nonNull(metadata)) {
            Optional<SpreadSheetsMetaData.Meta> data = metadata.getSheets().stream().filter(meta -> meta.getTitle().equals(sheetTitle)).findFirst();
            if (data.isPresent()) {
                SpreadSheetsMetaData.Meta meta = data.get();
                String sheetId = meta.getSheetId();
                int rowCount = meta.getGridProperties().getRowCount();
                List<String> selectColumn = new ArrayList<>();
                for (String col : column) {
                    selectColumn.add(sheetId + "!" + col + beginRow + ":" + col + rowCount);
                }
                return this.getMultiCellValuesCache(spreadsheetToken, selectColumn.toArray(new String[]{}));
            }
        }
        return Collections.emptyMap();
    }


    @Override
    protected int expire() {
        return larkProperties.getDefaultExpire();
    }

    @Override
    protected CacheLoader<String, Map<String, List<List<String>>>> cacheLoader() {
        return new CacheLoader<>() {
            @Override
            public Map<String, List<List<String>>> load( String key) throws Exception {
                SheetContentCacheKeyModel keyModel = JSONUtil.toBean(key, SheetContentCacheKeyModel.class);
                return getMultiCellValues(keyModel.getSheetToken(), keyModel.getColumn());
            }
        };
    }

    private CacheLoader<String, SpreadSheetsMetaData> initMetaDataCache() {
        return new CacheLoader<>() {
            @Override
            public SpreadSheetsMetaData load(String spreadsheetToken) throws Exception {
                return getMetadata(spreadsheetToken);
            }
        };
    }


    @Data
    public static class SheetContentCacheKeyModel {

        private String sheetToken;

        private String[] column;
    }

}
