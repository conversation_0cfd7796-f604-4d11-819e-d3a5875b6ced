// Custom Theming for NG-ZORRO
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";
@import '../node_modules/ng-zorro-antd/code-editor/style/entry.less';

// Override less variables to here
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

// @primary-color: #1890ff;
/* You can add global styles to this file, and also import other style files */

/*@layout-body-background: #F2F6FA;
@primary-color: rebeccapurple;*/

@layout-body-background: #F4F7FE;
@primary-color: #f16622;
@segmented-label-hover-color: #f16622;

.ant-layout-header {
  height: 48px;
  padding: 0 50px;
  color: #ffffff;
  line-height: 48px;
  /* background: #001529; */
}

.ant-layout-content {
  margin: 12px;
}

.shadow-bg {
  background-color: #E4EBF0 !important;
}

.white-bg {
  background-color: white !important;
}

.shadow-card {
  background-color: #E4EBF0 !important;
  transition: box-shadow .3s, border-color .3s;
}

.shadow-card .ant-card-head-title {
  padding: 6px !important;
}

.shadow-card .ant-card-head {
  min-height: unset !important;
}

.shadow-card .ant-card-body {
  padding: 6px !important;
}

.shadow-div-card {
  background-color: #E4EBF0 !important;
  padding: 6px !important;
}

.action-card {
  background: white;
  padding: 0 12px 12px 12px;
  transition: box-shadow .3s, border-color .3s;
}

.action-card .ant-card-cover {
  margin-right: unset;
  margin-left: unset;
}


.ant-card-head {
  padding: 0 !important;
}


.margin-12 {
  margin: 12px;
}

.margin-bottom-12 {
  margin-bottom: 12px;
}

.margin-bottom-6 {
  margin-bottom: 6px;
}

.margin-top-12 {
  margin-top: 12px;
}

.margin-left-12 {
  margin-left: 12px;
}

.margin-right-12 {
  margin-right: 12px;
}

.padding-12 {
  padding: 12px;
}

.padding-6 {
  padding: 6px;
}

.cursor-pointer {
  cursor: pointer;
}

.side-menu {
  min-height: calc(100vh - 24px);
  border-right: 0;
}


.cascader-select {
  width: auto !important;

  div {
    div {
      background-color: inherit !important;
      border: 0 !important;
    }
  }
}


img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}


.ant-input-group-addon:last-child {
  left: 0 !important;
}

.ant-drawer-body {
  padding: 12px !important;
}

.ant-modal-body {
  padding: 12px !important;
}

tr.ant-table-expanded-row > td {
  background: #ffffff !important;
}

.description-content .ant-descriptions-row:last-child .ant-descriptions-item-content {
  background-color: #E4EBF0 !important;
}

.chart-legend .legend-labels {
  width: auto !important;

}

.red-descriptions {
  line-height: 1.6;
  font-size: 14px;
  color: #666;
  padding: 12px;
  border: 1px solid rgb(220 224 230);
  border-left: 4px solid #f16622;
  border-radius: 0 2px 2px 0;
}

.green-descriptions {
  line-height: 1.6;
  font-size: 14px;
  color: #666;
  padding: 12px;
  border: 1px solid rgb(220 224 230);
  border-left: 4px solid green;
  border-radius: 0 2px 2px 0;
}

.below-chart .chart-legend {
  display: block !important;
}

.below-chart .ngx-charts {
  float: inherit !important;
}

.below-chart .ant-timeline-item {
  padding-bottom: 6px !important;
}

.table-add-icon {
  position: absolute;
  right: 16px;
}


.app-nav-appId .ant-select-arrow {
  color: rgba(255, 255, 255, 0.65) !important;
}


.ant-card-extra {
  padding: 0 !important;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.governance-nav .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu,
.runtime-nav .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu,
.doc-nav .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item, .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu {
  padding: 0 12px !important;
}


.doc-nav .ant-menu {
  background: #E4EBF0 !important;
}

.governance-dynamicLog .ant-descriptions-bordered .ant-descriptions-item-label {
  width: 170px;
}

.governance-timeout .ant-descriptions-bordered .ant-descriptions-item-label {
  width: 160px;
}

.governance-limit .ant-descriptions-bordered .ant-descriptions-item-label {
  width: 140px;
}

.doc nz-table-inner-default {
  background-color: #FAFAFA;
}

.doc tr.ant-table-expanded-row > td {
  background-color: #FAFAFA !important;
}

.governance nz-table-inner-default {
  background-color: #FAFAFA;
}

.ant-table-content .ant-table-row:last-child .ant-collapse:last-child .ant-collapse-item:last-child {
  margin-bottom: 0 !important;
}

.chart-height-120 {
  height: 120px;
  margin-top: -12px;
}

.chart-height-150 {
  height: 150px;
}

.chart-height-300 {
  height: 300px;
}

.config nz-table-inner-default {
  background-color: #FAFAFA;
}

.shadow-ant-table .ant-table {
  background-color: #E4EBF0 !important;
}

.text-vertical-center{
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-collapse-content > .ant-collapse-content-box {
  padding: 0 8px;
}

nz-collapse-panel .ant-table-pagination.ant-pagination {
  margin: 8px 0;
}

.aiInspectionReport .ant-descriptions-bordered .ant-descriptions-item-label, .ant-descriptions-bordered .ant-descriptions-item-content {
  padding: 4px 8px !important;
}

.meterReport {
  background-color: #ffffff !important;
}


