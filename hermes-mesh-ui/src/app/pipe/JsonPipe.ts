import {Injectable, Pipe, PipeTransform} from "@angular/core";
import {isObject, isString} from "lodash";

@Pipe({name: 'coverJsonObject'})
@Injectable()
export class CoverJsonObjectPipe implements PipeTransform {

  constructor() {
  }

  transform(value: string): any {
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  }
}


@Pipe({name: 'coverJsonString'})
@Injectable()
export class CoverJsonStringPipe implements PipeTransform {

  constructor() {
  }

  transform(value: any): any {
    if (!isObject(value) || isString(value)){
       return value;
    }
    try {
      return JSON.stringify(value, null, 2);
    } catch (e) {
      return value;
    }
  }
}
