import {NgModule, SecurityContext} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';

import {AppRoutingModule} from './app-routing.module';
import {AppComponent} from './app.component';
import {NZ_I18N, zh_CN} from 'ng-zorro-antd/i18n';
import {APP_BASE_HREF, CommonModule, HashLocationStrategy, LocationStrategy, registerLocaleData} from '@angular/common';
import zh from '@angular/common/locales/zh';
import {FormsModule} from '@angular/forms';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {NzLayoutModule} from 'ng-zorro-antd/layout';
import {NzMenuModule} from 'ng-zorro-antd/menu';
import {NzGridModule} from "ng-zorro-antd/grid";
import {NzToolTipModule} from "ng-zorro-antd/tooltip";
import {NzSpinModule} from "ng-zorro-antd/spin";
import {NzBackTopModule} from "ng-zorro-antd/back-top";
import {NzSelectModule} from "ng-zorro-antd/select";
import {NzAvatarModule} from "ng-zorro-antd/avatar";
import {NzPopconfirmModule} from "ng-zorro-antd/popconfirm";
import {ComponentModule} from "./component/component.module";
import {NzCascaderModule} from "ng-zorro-antd/cascader";
import {EllipsisModule} from "ngx-ellipsis";
import {EditorModule} from "@tinymce/tinymce-angular";
import {PipeModule} from "./pipe/pipe.module";
import {NzMessageModule} from "ng-zorro-antd/message";
import {CookieModule} from "ngx-cookie";
import {Interceptor} from "./interceptor/Interceptor";
import {NzNotificationModule} from "ng-zorro-antd/notification";
import {NgxEchartsModule} from "ngx-echarts";
import {IconsProviderModule} from "./icons-provider.module";
import {NzTypographyModule} from "ng-zorro-antd/typography";
import {NzBadgeModule} from "ng-zorro-antd/badge";
import {NzDropDownModule} from "ng-zorro-antd/dropdown";
import {MarkdownModule, MARKED_OPTIONS} from "ngx-markdown";


registerLocaleData(zh);

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    CommonModule,
    AppRoutingModule,
    FormsModule,
    HttpClientModule,
    BrowserAnimationsModule,
    NzLayoutModule,
    NzMenuModule,
    NzGridModule,
    NzToolTipModule,
    NzSpinModule,
    NzBackTopModule,
    NzSelectModule,
    NzAvatarModule,
    NzPopconfirmModule,
    NzMessageModule,
    ComponentModule,
    NzCascaderModule,
    EllipsisModule,
    EditorModule,
    PipeModule,
    IconsProviderModule,
    NzNotificationModule,
    CookieModule.withOptions(),
    NgxEchartsModule.forRoot({echarts: () => import('echarts')}),
    NzTypographyModule,
    NzBadgeModule,
    NzDropDownModule,
    MarkdownModule.forRoot({
      sanitize: SecurityContext.NONE,
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true
        }
      }
    })
  ],
  providers: [
    {provide: NZ_I18N, useValue: zh_CN},
    { provide: LocationStrategy, useClass: HashLocationStrategy},
    { provide: HTTP_INTERCEPTORS, useClass: Interceptor, multi: true },
    // @ts-ignore
    { provide: APP_BASE_HREF, useValue: window.__POWERED_BY_QIANKUN__ ? '/soa' : '/' }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
