import {Component, OnInit} from '@angular/core';
import {Router} from "@angular/router";
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {SpinningService} from "../../service/spinning.service";
import {ComponentService} from "../component.service";
import {StrUtils} from "../../utils/StrUtils";
import {markAsDirty} from "../../utils/FunctionUtils";

@Component({
  selector: 'component-request-param',
  templateUrl: './request-param.component.html',
  styleUrls: ['./request-param.component.less']
})
export class RequestParamComponent implements OnInit{

  constructor(private formBuilder : UntypedFormBuilder,
              private spinningService : SpinningService,
              private componentService : ComponentService) { }

  ngOnInit() {
    this.initForm();
  }

  viewModel : any[] = [];
  requestParamModel : any = {};
  drawerVisible  : boolean = false;
  modelVisible  : boolean = false;
  validateForm !: UntypedFormGroup;


  open() : void{
    this.viewModel = [];
    this.requestParams();
    this.drawerVisible = true;
  }

  close() : void{
    this.drawerVisible = false;
  }

  initForm() : void{
    this.validateForm = this.formBuilder.group({
      name: [null,[ Validators.required]],
      value: [null,[ Validators.required]],
      memo: [null],
    });
  }


  requestParams() : void{
    this.componentService.requestParams().subscribe(resp =>{
      if (resp.success){
        this.viewModel = resp.data;
      }
    })
  }


  delete(id : number) : void{
    this.componentService.deleteRequestParams(id).subscribe(resp =>{
      if (resp.success){
        this.requestParams();
      }
    })
  }


  doSubmit(): void{
    if (this.validateForm.valid) {
      Object.assign(this.requestParamModel, this.validateForm.value);
      this.spinningService.open();
      if (StrUtils.isNotEmpty(this.requestParamModel.id)) {
        this.componentService.putRequestParams(this.requestParamModel).subscribe(resp => {
          if (resp.success) {
            this.requestParams();
            this.initForm();
            this.modalClose();
          }
        })
        markAsDirty(this.validateForm)
        return;
      }
      this.componentService.postRequestParams(this.requestParamModel).subscribe(resp => {
        if (resp.success) {
          this.requestParams();
          this.initForm();
          this.modalClose();
        }
      })
    }
    markAsDirty(this.validateForm)
  }


  addParam(){
    this.modelVisible = true;
  }

  editParam(paramModel : any){
    this.modelVisible = true;
    this.requestParamModel = paramModel;
    this.validateForm.patchValue(paramModel,{onlySelf:true});
  }

  modalClose(){
    this.requestParamModel ={};
    this.modelVisible = false;
  }

}
