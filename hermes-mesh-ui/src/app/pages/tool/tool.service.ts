import {Injectable} from '@angular/core';
import {Observable} from "rxjs";
import {HttpClient, HttpEvent, HttpRequest} from "@angular/common/http";
import {RespMsg} from 'src/app/model/RespMsg';

@Injectable({
  providedIn: 'root'
})
export class ToolService {

  constructor(private httpClient : HttpClient) { }

  mvcFolders() : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/mvc/folders")
  }

  mvcDebug(request : any) : Observable<RespMsg>{
    return this.httpClient.post<RespMsg>("tool/mvc/debug",request)
  }

  getMvcRequest(id: number) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/mvc/request",{params:{"id":id}})
  }

  postMvcRequest(request : any) : Observable<RespMsg>{
    return this.httpClient.post<RespMsg>("tool/mvc/request",request)
  }

  deleteMvcRequest(id: number) : Observable<RespMsg>{
    return this.httpClient.delete<RespMsg>("tool/mvc/request",{params:{"id":id}})
  }

  mvcRequestLogs(num: number, size: number = 15) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/mvc/request/logs",{params: {"num": num, "size": size}})
  }

  soaFolders() : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/soa/folders")
  }

  soaDebug(request : any) : Observable<RespMsg>{
    return this.httpClient.post<RespMsg>("tool/soa/debug",request)
  }

  getSoaRequest(id: number) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/soa/request",{params:{"id":id}})
  }

  postSoaRequest(request : any) : Observable<RespMsg>{
    return this.httpClient.post<RespMsg>("tool/soa/request",request)
  }

  deleteSoaRequest(id: number) : Observable<RespMsg>{
    return this.httpClient.delete<RespMsg>("tool/soa/request",{params:{"id":id}})
  }

  soaRequestLogs(num: number, size: number) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/soa/request/logs",{params: {"num": num, "size": size}})
  }
  soaAppRequestLogs(appId: string,name : string,num: number, size: number) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/soa/app/request/logs",{params: {"appId":appId,"name":name,"num": num, "size": size}})
  }

  grayCondition(appId : string) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/gary/condition",{params:{"appId":appId}})
  }

  appZoneRouterConfigUpdate(appId: string, azRouterEnable: boolean,azRouterCrossEnable:boolean){
    return this.httpClient.post<RespMsg>("tool/zone/routerConfig",{"azRouterEnable": azRouterEnable,"azRouterCrossEnable": azRouterCrossEnable},{params: {"appId": appId}})
  }

  appZoneRouterConfig(appId : string){
    return this.httpClient.get<RespMsg>("tool/zone/routerConfig",{params: {"appId": appId}})
  }

  appZoneRouterConfigLog(appId : string,num: number, size: number = 15){
    return this.httpClient.get<RespMsg>("tool/zone/routerLog",{params: {"appId": appId,"num":num,"size":size}})
  }

  appTimeoutConfigFlat(appId : string){
    return this.httpClient.get<RespMsg>("tool/timeout/config",{params: {"appId": appId}})
  }

  excelUpload(formData : any) : Observable<HttpEvent<RespMsg>>{
    const req = new HttpRequest('POST', "tool/timeout/file", formData, {
      reportProgress: true,
      withCredentials: true
    });
    return this.httpClient.request<RespMsg>(req);
  }

  inspectionReport(scene : string,id : number){
    return this.httpClient.get<RespMsg>("tool/inspection/report",{params: {"scene": scene,"id": id}})
  }

  executeInspection(scene : string){
    return this.httpClient.post<RespMsg>("tool/inspection/execute",{},{params: {"scene": scene}})
  }

  inspectionRules(scene : string){
    return this.httpClient.get<RespMsg>("tool/inspection/rules",{params: {"scene": scene}})
  }

  postInspectionRule(inspectionRule : any){
    return this.httpClient.post<RespMsg>("tool/inspection/rule",inspectionRule)
  }

  deleteInspectionRule(id : number){
    return this.httpClient.delete<RespMsg>("tool/inspection/rule",{params:{"id":id}})
  }

  executeInspectionRule(inspectionRule : any){
    return this.httpClient.post<RespMsg>("tool/inspection/rule/execute",inspectionRule)
  }

  inspectionReportHistory(scene: string,num: number, size: number) : Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/inspection/report/history",{params: {"scene":scene,"num": num, "size": size}})
  }

  /**
   * 脚本参数列表
   */
  inspectionScriptParams():Observable<RespMsg>{
    return this.httpClient.get<RespMsg>("tool/inspection/script/params");
  }

  /**
   * 删除脚本参数
   */
  deleteInspectionScriptParam(id : number):Observable<RespMsg>{
    return this.httpClient.delete<RespMsg>("tool/inspection/script/param",{params:{"id":id}});
  }

  /**
   * 新增脚本参数
   */
  postInspectionScriptParam(scriptParam : any):Observable<RespMsg>{
    return this.httpClient.post<RespMsg>("tool/inspection/script/param",scriptParam);
  }

  /**
   * 修改脚本参数
   */
  putInspectionScriptParam(scriptParam : any):Observable<RespMsg>{
    return this.httpClient.put<RespMsg>("tool/inspection/script/param",scriptParam);
  }

}
