<nz-row nzGutter="12">
  <nz-col nzSpan="18">
    <nz-tabset  [nzTabBarExtraContent]="resetTemplate">
      <nz-tab nzTitle="MVC MAN">
        <form nz-form [formGroup]="validateForm">

          <nz-form-item>
            <nz-form-label nzFlex="60px">名称</nz-form-label>
            <nz-form-control nzFlex="auto" nzErrorTip="请输入名称">
              <nz-input-group nzSearch [nzAddOnBefore]="beforeFolderTemplate"   >
                <input nz-input placeholder="名称"  [(ngModel)]="requestModel.name" [ngModelOptions]="{standalone: true}"/>
              </nz-input-group>
              <ng-template #beforeFolderTemplate>
                <nz-select  [(ngModel)]="requestModel.folderId" style="min-width: 200px" nzPlaceHolder="目录列表" [ngModelOptions]="{standalone: true}">
                  <nz-option [nzLabel]="folder.name" [nzValue]="folder.id"  *ngFor="let folder of folderModel"></nz-option>
                </nz-select>
              </ng-template>
            </nz-form-control>
          </nz-form-item>


          <nz-form-item>
            <nz-form-label nzFlex="60px">AppId</nz-form-label>
            <nz-form-control nzFlex="auto">
              <nz-input-group nzSearch>
                <nz-select nzShowSearch [(ngModel)]="requestModel.appId" nzAllowClear nzPlaceHolder="请输入选择AppId" [ngModelOptions]="{standalone: true}">
                  <nz-option [nzLabel]="appId" [nzValue]="appId" *ngFor="let appId of appIdModel"></nz-option>
                </nz-select>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>


          <nz-form-item>
            <nz-form-label nzFlex="60px">实例</nz-form-label>
            <nz-form-control nzFlex="auto">
              <nz-input-group nzSearch>
                <nz-select nzShowSearch [(ngModel)]="requestModel.instanceId" style="min-width: 200px" nzAllowClear nzPlaceHolder="请输入选择实例"  (ngModelChange)="instanceModelChange($event)" [(nzOpen)]="instanceIdOpen" (nzOpenChange)="instanceOpenChange()" [ngModelOptions]="{standalone: true}">
                  <ng-container *ngFor="let app of instanceModel">
                    <nz-option [nzLabel]="app.id" [nzValue]="app.id" [nzCustomContent]="true">
                      <span nz-typography>{{app.id}}</span>
                      <span nz-typography>灰度版本:{{app.releaseVersion ? (app.releaseVersion) :'default' }}</span>
                      <span nz-typography>部署泳道:{{app.zone ? (app.zone) :'default' }}</span>
                      <span nz-typography>服务分组:{{app.group ? (app.group) :'unknown' }}</span>
                      <span nz-typography>启动时间:{{app.startTime ? (app.startTime |  date: 'yyy-MM-dd HH:mm:ss') :'unknown' }}</span>
                    </nz-option>
                  </ng-container>
                </nz-select>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>


          <nz-form-item>
            <nz-form-label nzFlex="60px" nzFor="url" nzRequired>URL</nz-form-label>
            <nz-form-control nzFlex="auto" nzErrorTip="请输入URL">
              <nz-input-group nzSearch>
                <input nz-input placeholder="请求URL"  formControlName="url"/>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>


          <nz-form-item>
            <nz-form-label nzFlex="60px" nzRequired nzFor="method">方法</nz-form-label>
            <nz-form-control nzFlex="auto"  nzErrorTip="请选择请求方法">
              <nz-select formControlName="method">
                <nz-option nzLabel="GET" nzValue="GET"></nz-option>
                <nz-option nzLabel="POST" nzValue="POST"></nz-option>
                <nz-option nzLabel="PUT" nzValue="PUT"></nz-option>
                <nz-option nzLabel="DELETE" nzValue="DELETE"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label nzFlex="60px" nzRequired nzFor="content">文本</nz-form-label>
            <nz-form-control nzFlex="auto" nzErrorTip="请选择文本协议">
              <nz-select formControlName="content">
                <nz-option nzLabel="none" nzValue="NONE"></nz-option>
                <nz-option nzLabel="raw" nzValue="RAW"></nz-option>
                <nz-option nzLabel="application/json" nzValue="APPLICATION_JSON"></nz-option>
                <nz-option nzLabel="x-www-form-urlencoded" nzValue="APPLICATION_X_WWW_FORM_URLENCODED"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>


          <nz-form-item>
            <nz-form-label nzFlex="60px" nzFor="name">备注</nz-form-label>
            <nz-form-control nzFlex="auto">
              <textarea rows="4"  nz-input formControlName="memo" placeholder="调试备注"></textarea>
            </nz-form-control>
          </nz-form-item>

        </form>

        <nz-tabset >
          <nz-tab nzTitle="Parameters">
            <component-kv-pair [(viewModel)]="requestModel.request.parameters"></component-kv-pair>
          </nz-tab>
          <nz-tab nzTitle="Headers">
            <component-kv-pair [(viewModel)]="requestModel.request.headers"></component-kv-pair>
          </nz-tab>
          <nz-tab nzTitle="Body">
            <textarea rows="8" nz-input  [(ngModel)]="requestModel.request.body"    placeholder="row|json|form-data" ></textarea>
          </nz-tab>
          <nz-tab [nzTitle]="takeOutTitle">
            <component-kv-pair [(viewModel)]="requestModel.request.takeOut"></component-kv-pair>
          </nz-tab>
          <ng-template #takeOutTitle>TakeOut
            <a nz-tooltip [nzTooltipTitle]="takeOutTemplate">
              <i class="anticon-question-circle" nz-icon nzType="question-circle"></i>
            </a>
            <ng-template #takeOutTemplate>
              <p>出参名:将根据表达式获取的返回值赋予出参名</p>
              <p>表达式:</p>
              <p>body:将从响应体中获取返回值，如body.code</p>
              <p>header:将从响应头中获取返回值，如header.User-Agent</p>
            </ng-template>
          </ng-template>

          <nz-tab [nzTitle]="assertTitle">
            <textarea rows="8" nz-input  [(ngModel)]="requestModel.request.assertExp" placeholder="表达式,如'${code}==0 || ${token}==token" nz-tooltip  nzTooltipTrigger="focus" [nzTooltipTitle]="assertTooltip" ></textarea>
          </nz-tab>
          <ng-template #assertTitle>Assert
            <a nz-tooltip [nzTooltipTitle]="titleTemplate">
              <i class="anticon-question-circle" nz-icon nzType="question-circle"></i>
            </a>
            <ng-template #titleTemplate>
              <p>将出参的参数作为输入条件进行断言表达式执行,支持所有数学表达式方式执行,如:</p>
              <p>${{"{"}}code{{"}"}}==1&&${{"{"}}success{{"}"}}</p>
              <p>(${{"{"}}code{{"}"}}>=1&&${{"{"}}code{{"}"}}<=10&&${{"{"}}code{{"}"}}!=5)||${{"{"}}success{{"}"}}</p>
            </ng-template>
          </ng-template>
          <ng-template #assertTooltip>
            <ng-container *ngFor="let p of requestModel.request.takeOut">
              <p>
                {{p.name}} : {{p.value}}
              </p>
            </ng-container>
          </ng-template>

          <nz-tab nzTitle="BusinessCode">
            <textarea rows="8" nz-input  [(ngModel)]="requestModel.request.businessExp" placeholder="业务取值表达式"></textarea>
          </nz-tab>
        </nz-tabset>

        <nz-row nzGutter="12" nzJustify="end" class="margin-top-12">
          <nz-col>
            <button type="button"  class="ant-btn ant-btn-primary" (click)="saveRequest()">保存</button>
          </nz-col>
          <nz-col>
            <button type="button"  class="ant-btn ant-btn-danger"  (click)="submitForm()" >调试</button>
          </nz-col>
        </nz-row>

      </nz-tab>
    </nz-tabset>
    <ng-template #resetTemplate>
      <a nz-tooltip nzTooltipTitle="重置输入信息" (click)="ngOnInit()"><i nz-icon nzType="rollback" nzTheme="outline"></i></a>
      <nz-divider nzType="vertical"></nz-divider>
      <a nz-tooltip nzTooltipTitle="参数变量" (click)="requestParamComponent.open()" ><span nz-icon nzType="database" nzTheme="outline"></span></a>
      <nz-divider nzType="vertical"></nz-divider>
      <a nz-tooltip nzTooltipTitle="函数列表" (click)="commonFunctionComponent.open()" ><span nz-icon nzType="appstore" nzTheme="outline"></span></a>
    </ng-template>
  </nz-col>


  <nz-col nzSpan="6" class="shadow-bg">
    <ng-container *ngIf="!showRequestLog;else historyTemplate">
      <nz-card class="shadow-card" [nzBorderless]="true" nzTitle="目录列表" [nzExtra]="folderExtraTemplate">
          <nz-input-group nzSearch >
            <input type="text" nzSize="large" placeholder="请输入名称" [(ngModel)]="searchKey"  nz-input (keyup)="searchKeyFn()"/>
          </nz-input-group>
          <ul nz-menu nzMode="inline">
              <li nz-submenu [nzTitle]="folder.name" *ngFor="let folder of folderModel" [nzOpen]="folder.open" >
                <ul *ngIf="folder.files.length > 0">
                  <li nz-menu-item [nzSelected]="file.id == requestId" *ngFor="let file of folder.files">
                    <nz-row nzJustify="space-around" style="width: 100%">
                      <nz-col nzFlex="auto">
                        <a (click)="replay(file.id)" nz-tooltip nzTooltipTitle="查看详情">{{file.name}}</a>
                      </nz-col>
                      <nz-col nzFlex="14px">
                        <a nz-tooltip nzTooltipTitle="删除"  nz-popconfirm nzPopconfirmTitle="确认是否删除？" nzPopconfirmPlacement="bottom" (nzOnConfirm)="delete(file.id)">
                          <i nz-icon nzType="delete" nzTheme="outline"></i>
                        </a>
                      </nz-col>
                    </nz-row>
                  </li>
                </ul>
              </li>
          </ul>
      </nz-card>
    </ng-container>

    <ng-template #historyTemplate>
      <nz-card class="shadow-card" [nzBorderless]="true" nzTitle="调试日志" [nzExtra]="historyExtraTemplate">

        <nz-list nzItemLayout="horizontal">
          <nz-list-item *ngFor="let requestLog of requestLogModel.records" class="cursor-pointer margin-bottom-12"  nz-tooltip nzTooltipTitle="回放日志"
                        [ngStyle]="{ background: '#f7f7f7', border: '0px', 'border-left': requestLog.response.statusCode == 200 ?'4px solid blue':'4px solid red'}" (click)="replayLog(requestLog)">
            <nz-list-item-meta [nzDescription]="descriptionTemplate" class="margin-left-12">
              <ng-template #descriptionTemplate>
                <p>{{requestLog.response.statusCode}},{{requestLog.userId}},{{requestLog.createdAt | date: 'yyy-MM-dd HH:mm:ss'}}</p>
              </ng-template>
              <nz-list-item-meta-title>
                 {{requestLog.response.url}}
              </nz-list-item-meta-title>
            </nz-list-item-meta>
          </nz-list-item>
          <nz-list-empty *ngIf="requestLogModel.records?.length === 0"></nz-list-empty>
        </nz-list>

        <nz-row nzJustify="end">
          <nz-col>
            <nz-pagination [nzPageIndex]="requestLogModel.current" [nzPageSize]="requestLogModel.size"
                           [nzTotal]="requestLogModel.total"
                           [nzShowTotal]="logTotalTemplate" (nzPageSizeChange)="putPageSize($event)"
                           (nzPageIndexChange)="requestLogs($event)" nzSimple>
            </nz-pagination>
          </nz-col>
        </nz-row>
        <ng-template #logTotalTemplate let-range="range" let-total>
          总共{{ total }} 条
        </ng-template>
      </nz-card>
    </ng-template>

    <ng-template #folderExtraTemplate>
      <a (click)="openFolder()"  nz-tooltip nzTooltipTitle="目录管理"><i nz-icon nzType="folder" nzTheme="outline"></i></a>
      <nz-divider nzType="vertical"></nz-divider>
      <a nz-tooltip nzTooltipTitle="历史记录" (click)="requestLogs(0)"><span nz-icon nzType="history" nzTheme="outline"></span></a>
    </ng-template>

    <ng-template #historyExtraTemplate>
      <a nz-tooltip nzTooltipTitle="返回目录管理" (click)="showRequestLog = false"><span nz-icon nzType="fast-backward" nzTheme="outline"></span></a>
    </ng-template>


  </nz-col>
</nz-row>
<component-request-param #requestParamComponent></component-request-param>
<component-common-function #commonFunctionComponent></component-common-function>
<component-http-response #httpResponseComponent></component-http-response>
<component-common-folder #componentFolderComponent path="/tool/mvc/folder" (refreshEvent)="folders()"></component-common-folder>
