import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {InspectionComponent} from "./inspection.component";
import {EmergencyComponent} from "./emergency/emergency.component";

const routes: Routes = [
  {path:'',component:InspectionComponent,children:[
      {path:'emergency',component:EmergencyComponent},
      {path: '', redirectTo: 'emergency', pathMatch: 'full'}
    ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InspectionRoutingModule { }
