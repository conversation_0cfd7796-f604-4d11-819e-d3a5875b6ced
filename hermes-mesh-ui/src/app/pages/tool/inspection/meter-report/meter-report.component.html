<!--报告-->
<nz-descriptions nzLayout="horizontal" nzBordered [nzColumn]="{ xxl: 4, xl: 4, lg: 4, md: 4, sm: 4, xs: 4 }" class="meterReport">
  <nz-descriptions-item nzTitle="ID">{{ meterReportModel.id }}</nz-descriptions-item>
  <nz-descriptions-item nzTitle="名称">{{ meterReportModel.name }}</nz-descriptions-item>
  <nz-descriptions-item nzTitle="组件">{{ meterReportModel.component }}</nz-descriptions-item>
  <nz-descriptions-item nzTitle="类型">{{ meterReportModel.type == 'EVALUATION' ? '评定项' : '参考项' }}
  </nz-descriptions-item>

  <nz-descriptions-item nzTitle="备注" nzSpan="4">{{ meterReportModel.memo }}</nz-descriptions-item>
  <nz-descriptions-item nzTitle="报告详情" nzSpan="4">{{ meterReportModel.details }}</nz-descriptions-item>
  <nz-descriptions-item nzTitle="应急措施" nzSpan="4">
    <div [innerHTML]="meterReportModel.action"></div>
  </nz-descriptions-item>
  <nz-descriptions-item nzTitle="附加信息" nzSpan="4" *ngIf="meterReportModel.extra" >
   <!-- <div class="long-text-container">
      <ngx-json-viewer [json]="meterReportModel.extra | coverJsonObject" [expanded]="true"></ngx-json-viewer>
    </div>-->

    <nz-table nzShowPagination=false [nzTemplateMode]="true">
      <thead>
      <tr>
        <th nzWidth="120px">Key</th>
        <th>Value</th>
      </tr>
      </thead>
      <tbody>
        @for (key of keys(meterReportModel.extra|| {}); track key) {
          <tr>
            <td nzLeft>
              <span nz-typography><strong>{{ key }}</strong></span>
            </td>
            <td>{{ meterReportModel.extra[key] | coverJsonString}}</td>
          </tr>
        }
      </tbody>
    </nz-table>

  </nz-descriptions-item>
</nz-descriptions>
<nz-table #itemModelTable [nzData]="meterReportModel.items" *ngIf="meterReportModel.items?.length > 0">
  <thead>
  <tr>
    <th>名称</th>
    <th>状态</th>
    <th>详情</th>
  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let itemModel of itemModelTable.data">
    <td>{{ itemModel.name }}</td>
    <td>{{ itemModel.status }}</td>
    <td>{{ itemModel.details }}</td>
  </tr>
  </tbody>
</nz-table>

