import {Component, Input, input} from '@angular/core';
import {keys} from "lodash";

@Component({
  selector: 'app-tool-inspection-meter-report',
  templateUrl: './meter-report.component.html',
  styleUrl: './meter-report.component.less'
})
export class MeterReportComponent {


  @Input() meterReportModel !: any ;
  reportModelVisible  : boolean = false;


  open(meterModel : any){
    this.meterReportModel = meterModel;
    this.reportModelVisible = true;
  }


  reportClose() : void{
    this.reportModelVisible = false;
    this.meterReportModel = {};
  }

  protected readonly keys = keys;
}
