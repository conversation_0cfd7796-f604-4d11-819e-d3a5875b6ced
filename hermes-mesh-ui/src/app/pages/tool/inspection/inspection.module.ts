import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {InspectionRoutingModule} from './inspection-routing.module';
import {InspectionComponent} from "./inspection.component";
import {EmergencyComponent} from "./emergency/emergency.component";
import {NzResultModule} from "ng-zorro-antd/result";
import {NzButtonModule} from "ng-zorro-antd/button";
import {NzDividerModule} from "ng-zorro-antd/divider";
import {NzToolTipModule} from "ng-zorro-antd/tooltip";
import {InspectionRuleComponent} from "./inspection-rule/inspection-rule.component";
import {NzTableModule} from "ng-zorro-antd/table";
import {NzPopconfirmModule} from "ng-zorro-antd/popconfirm";
import {NzModalModule} from "ng-zorro-antd/modal";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NzFormModule} from "ng-zorro-antd/form";
import {NzInputModule} from "ng-zorro-antd/input";
import {NzLayoutModule} from "ng-zorro-antd/layout";
import {NzMenuModule} from "ng-zorro-antd/menu";
import {NzTabsModule} from "ng-zorro-antd/tabs";
import {NzSelectModule} from "ng-zorro-antd/select";
import {ComponentModule} from "../../../component/component.module";
import {IconsProviderModule} from "../../../icons-provider.module";
import {NzCardModule} from "ng-zorro-antd/card";
import {NzEmptyModule} from "ng-zorro-antd/empty";
import {NzTypographyModule} from "ng-zorro-antd/typography";
import {NzCollapseModule} from "ng-zorro-antd/collapse";
import {NzSegmentedModule} from "ng-zorro-antd/segmented";
import {NzListModule} from "ng-zorro-antd/list";
import {NzPaginationModule} from "ng-zorro-antd/pagination";
import {NzCascaderModule} from "ng-zorro-antd/cascader";
import {NumberCardModule, PieChartModule} from "@swimlane/ngx-charts";
import {NgxGraphModule} from "@swimlane/ngx-graph";
import {NzAlertModule} from "ng-zorro-antd/alert";
import {NgxEchartsModule} from "ngx-echarts";
import {NzDescriptionsModule} from "ng-zorro-antd/descriptions";
import {NzInputNumberModule} from "ng-zorro-antd/input-number";
import {NzSwitchModule} from "ng-zorro-antd/switch";
import {NzWaveModule} from "ng-zorro-antd/core/wave";
import {NzMentionModule} from "ng-zorro-antd/mention";
import {NzDrawerComponent, NzDrawerContentDirective} from "ng-zorro-antd/drawer";
import {NzCodeEditorComponent} from "ng-zorro-antd/code-editor";
import {NgxJsonViewerModule} from "ngx-json-viewer";
import {PipeModule} from "../../../pipe/pipe.module";
import {MeterReportComponent} from "./meter-report/meter-report.component";
import {NzTagComponent} from "ng-zorro-antd/tag";
import {NzUploadComponent} from "ng-zorro-antd/upload";
import {MarkdownComponent} from "ngx-markdown";
import {ReportHistoryComponent} from "./report-history/report-history.component";
import {ScriptParamComponent} from "./script-param/script-param.component";

@NgModule({
  declarations: [
    InspectionComponent,
    EmergencyComponent,
    InspectionRuleComponent,
    MeterReportComponent,
    ReportHistoryComponent,
    ScriptParamComponent,
  ],
  imports: [
    CommonModule,
    InspectionRoutingModule,
    NzLayoutModule,
    NzMenuModule,
    NzFormModule,
    NzTabsModule,
    NzInputModule,
    NzSelectModule,
    NzToolTipModule,
    FormsModule,
    ReactiveFormsModule,
    ComponentModule,
    IconsProviderModule,
    NzCardModule,
    NzEmptyModule,
    NzPopconfirmModule,
    NzTypographyModule,
    NzDividerModule,
    NzCollapseModule,
    NzSegmentedModule,
    NzTableModule,
    NzListModule,
    NzPaginationModule,
    NzCascaderModule,
    NumberCardModule,
    PieChartModule,
    NgxGraphModule,
    NzModalModule,
    NzAlertModule,
    NzResultModule,
    NgxEchartsModule,
    NzButtonModule,
    NzDescriptionsModule,
    NzInputNumberModule,
    NzSwitchModule,
    NzWaveModule,
    NzMentionModule,
    NzDrawerComponent,
    NzDrawerContentDirective,
    NzCodeEditorComponent,
    NgxJsonViewerModule,
    PipeModule,
    NzTagComponent,
    NzUploadComponent,
    MarkdownComponent,
  ]
})
export class InspectionModule { }
