<nz-drawer [nzBodyStyle]="{ height: 'calc(100% - 55px)'}" nzWidth="calc(50% - 118px)" nzTitle="请求参数" [nzVisible]="drawerVisible" (nzOnClose)="close()">
  <ng-container *nzDrawerContent>
    <nz-table #basicTable  [nzBordered]="false" [nzData]="viewModel" nzShowSizeChanger >
      <thead>
      <tr>
        <th>KEY</th>
        <th>VALUE</th>
        <th>组件</th>
        <th>备注</th>
        <th>添加时间</th>
        <th nzWidth="120px">操作<a class="table-add-icon" nz-tooltip nzTooltipTitle="添加" (click)="addParam()"><i nz-icon nzType="plus" nzTheme="outline"></i></a></th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let param of basicTable.data">
        <td>{{ param.name }}</td>
        <td>{{ param.value }}</td>
        <td>{{ param.component }}</td>
        <td>{{ param.memo }}</td>
        <td>{{ param.createdAt | date: 'yyy-MM-dd HH:mm:ss'}}</td>
        <td>
          <a (click)="editParam(param)">编辑</a>
          <nz-divider nzType="vertical"></nz-divider>
          <a  nz-popconfirm nzPopconfirmTitle="确认是否删除？所属的内容将全部删除"  nzPopconfirmPlacement="top" (nzOnConfirm)="delete(param.id)">删除</a>
        </td>
      </tr>
      </tbody>
    </nz-table>
  </ng-container>
</nz-drawer>




<nz-modal [(nzVisible)]="modelVisible" nzTitle="新增" (nzOnCancel)="modalClose()" nzMaskClosable="false" (nzOnOk)="doSubmit()">
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="validateForm">
      <nz-form-item>
        <nz-form-label nzFlex="60px" nzRequired nzFor="name">Name</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入变量名称">
          <input type="text" nz-input formControlName="name" placeholder="变量名称" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="60px" nzRequired nzFor="value">Value</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入变量值">
          <input type="text" nz-input formControlName="value" placeholder="变量值" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="60px" nzRequired nzFor="component">组件</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请选择组件类型">
          <nz-select formControlName="component">
            <nz-option nzValue="ALL" nzLabel="ALL"></nz-option>
            <nz-option nzValue="CONSUL" nzLabel="CONSUL"></nz-option>
            <nz-option nzValue="APOLLO" nzLabel="APOLLO"></nz-option>
            <nz-option nzValue="SOA" nzLabel="SOA"></nz-option>
            <nz-option nzValue="JAF" nzLabel="JAF"></nz-option>
            <nz-option nzValue="UUID" nzLabel="UUID"></nz-option>
            <nz-option nzValue="XXLJOB" nzLabel="XXLJOB"></nz-option>
            <nz-option nzValue="MESHLB" nzLabel="MESHLB"></nz-option>
            <nz-option nzValue="GOPROXY" nzLabel="GOPROXY"></nz-option>
            <nz-option nzValue="DATAMESH" nzLabel="DATAMESH"></nz-option>
            <nz-option nzValue="KONG" nzLabel="KONG"></nz-option>
            <nz-option nzValue="GATEWARY" nzLabel="GATEWARY"></nz-option>
            <nz-option nzValue="DAL" nzLabel="DAL"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="60px"  nzFor="memo">Memo</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入变量备注">
          <textarea rows="6" nz-input formControlName="memo"   placeholder="变量备注" ></textarea>
        </nz-form-control>
      </nz-form-item>

    </form>
  </ng-container>
</nz-modal>
