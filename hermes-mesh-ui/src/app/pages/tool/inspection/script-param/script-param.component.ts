import {Component, OnInit} from '@angular/core';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {SpinningService} from "../../../../service/spinning.service";
import {StrUtils} from "../../../../utils/StrUtils";
import {markAsDirty} from "../../../../utils/FunctionUtils";
import {ToolService} from "../../tool.service";

@Component({
  selector: 'app-tool-inspection-script-param',
  templateUrl: './script-param.component.html',
  styleUrl: './script-param.component.less'
})
export class ScriptParamComponent implements OnInit{


  constructor(private formBuilder : UntypedFormBuilder,
              private spinningService : SpinningService,
              private toolService : ToolService) { }

  ngOnInit() {
    this.initForm();
  }

  viewModel : any[] = [];
  scriptParamModel : any = {};
  drawerVisible  : boolean = false;
  modelVisible  : boolean = false;
  validateForm !: UntypedFormGroup;


  open() : void{
    this.viewModel = [];
    this.scriptParams();
    this.drawerVisible = true;
  }

  close() : void{
    this.drawerVisible = false;
  }

  initForm() : void{
    this.validateForm = this.formBuilder.group({
      name: [null,[ Validators.required]],
      value: [null,[ Validators.required]],
      component: ['ALL',[ Validators.required]],
      memo: [null],
    });
  }


  scriptParams() : void{
    this.toolService.inspectionScriptParams().subscribe(resp =>{
      if (resp.success){
        this.viewModel = resp.data;
      }
    })
  }


  delete(id : number) : void{
    this.toolService.deleteInspectionScriptParam(id).subscribe(resp =>{
      if (resp.success){
        this.scriptParams();
      }
    })
  }


  doSubmit(): void{
    if (this.validateForm.valid) {
      Object.assign(this.scriptParamModel, this.validateForm.value);
      this.spinningService.open();
      if (StrUtils.isNotEmpty(this.scriptParamModel.id)) {
        this.toolService.putInspectionScriptParam(this.scriptParamModel).subscribe(resp => {
          if (resp.success) {
            this.scriptParams();
            this.initForm();
            this.modalClose();
          }
        })
        markAsDirty(this.validateForm)
        return;
      }
      this.toolService.postInspectionScriptParam(this.scriptParamModel).subscribe(resp => {
        if (resp.success) {
          this.scriptParams();
          this.initForm();
          this.modalClose();
        }
      })
    }
    markAsDirty(this.validateForm)
  }


  addParam(){
    this.modelVisible = true;
  }

  editParam(paramModel : any){
    this.modelVisible = true;
    this.scriptParamModel = paramModel;
    this.validateForm.patchValue(paramModel,{onlySelf:true});
  }

  modalClose(){
    this.scriptParamModel ={};
    this.modelVisible = false;
  }


}
