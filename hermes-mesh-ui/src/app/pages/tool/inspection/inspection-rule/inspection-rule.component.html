<nz-drawer [nzBodyStyle]="{ height: 'calc(100% - 55px)'}" nzWidth="calc(70% - 118px)" nzTitle="自检规则" [nzVisible]="drawerVisible" (nzOnClose)="close()">
  <ng-container *nzDrawerContent>
    <nz-table #basicTable  [nzBordered]="false" [nzData]="inspectionRuleModel" nzShowSizeChanger >
      <thead>
      <tr>
        <th>ID</th>
        <th>序号</th>
        <th>名称</th>
        <th>组件</th>
        <th>类型</th>
        <th>作用</th>
        <th>启用</th>
        <th>时间</th>
        <th nzWidth="160px">操作<a class="table-add-icon" nz-tooltip nzTooltipTitle="添加" (click)="addInspectionRule()"><i nz-icon nzType="plus" nzTheme="outline"></i></a></th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let ruleModel of basicTable.data">
        <td>{{ ruleModel.id }}</td>
        <td>{{ ruleModel.order }}</td>
        <td>{{ ruleModel.name }}</td>
        <td>{{ ruleModel.component }}</td>
        <td>{{ ruleModel.type == 'EVALUATION'?'评定项':'参考项' }}</td>
        <td>{{ ruleModel.memo }}</td>
        <td>
          <nz-switch nz-tooltip [nzTooltipTitle]="ruleModel.enabled ? '启用中':'未启用'"
                     [ngModel]="ruleModel.enabled" [nzControl]="true"  nzCheckedChildren="开" nzUnCheckedChildren="关" disabled></nz-switch>
        </td>
        <td>{{ ruleModel.updatedAt | date: 'yyy-MM-dd HH:mm:ss'}}</td>
        <td>
          <a (click)="editInspectionRule(ruleModel)">编辑</a>
          <nz-divider nzType="vertical"></nz-divider>
          <a  nz-popconfirm nzPopconfirmTitle="确认是否删除脚本？"  nzPopconfirmPlacement="top" (nzOnConfirm)="deleteInspectionRule(ruleModel.id)">删除</a>
          <nz-divider nzType="vertical"></nz-divider>
          <a  nz-popconfirm nzPopconfirmTitle="调试脚本"  nzPopconfirmPlacement="top" (nzOnConfirm)="executeInspectionRule(ruleModel)">调试</a>
        </td>
      </tr>
      </tbody>
    </nz-table>
  </ng-container>
</nz-drawer>



<!--新增规则-->
<nz-modal [(nzVisible)]="modelVisible"  nzWidth="calc(70% - 118px)" [nzTitle]="ruleModel.id == null ? '新增规则':'更新规则'" (nzOnCancel)="modalClose()" nzMaskClosable="false" (nzOnOk)="submitInspectionRule()">
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="validateForm">
      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="name">规则名称</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入规则名称">
          <input type="text" nz-input formControlName="name" placeholder="规则名称" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="component">组件类型</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请选择组件类型">
          <nz-select formControlName="component">
            <nz-option nzValue="CONSUL" nzLabel="CONSUL"></nz-option>
            <nz-option nzValue="APOLLO" nzLabel="APOLLO"></nz-option>
            <nz-option nzValue="SOA" nzLabel="SOA"></nz-option>
            <nz-option nzValue="JAF" nzLabel="JAF"></nz-option>
            <nz-option nzValue="UUID" nzLabel="UUID"></nz-option>
            <nz-option nzValue="XXLJOB" nzLabel="XXLJOB"></nz-option>
            <nz-option nzValue="MESHLB" nzLabel="MESHLB"></nz-option>
            <nz-option nzValue="GOPROXY" nzLabel="GOPROXY"></nz-option>
            <nz-option nzValue="DATAMESH" nzLabel="DATAMESH"></nz-option>
            <nz-option nzValue="KONG" nzLabel="KONG"></nz-option>
            <nz-option nzValue="GATEWARY" nzLabel="GATEWARY"></nz-option>
            <nz-option nzValue="DAL" nzLabel="DAL"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="type">作用类型</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请选择作用类型">
          <nz-select formControlName="type">
            <nz-option nzValue="EVALUATION" nzLabel="评定项"></nz-option>
            <nz-option nzValue="ASSESSMENT" nzLabel="参考性"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>


      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="memo">组件描述</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入组件描述">
          <input type="text" nz-input formControlName="memo" placeholder="组件描述" />
        </nz-form-control>
      </nz-form-item>


      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="order">排序序号</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入排序序号 越小越靠前不能小于0">
          <input type="number" nz-input formControlName="order" placeholder="排序序号" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="enabled">是否启用</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请选择状态">
          <nz-switch formControlName="enabled" nzCheckedChildren="启用" nzUnCheckedChildren="禁用"></nz-switch>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzFlex="90px" nzRequired nzFor="action">应急操作</nz-form-label>
        <nz-form-control nzFlex="auto" nzErrorTip="请输入应急操作流程">
          <textarea rows="4" nz-input  formControlName="action"></textarea>
        </nz-form-control>
      </nz-form-item>
    </form>
    <nz-code-editor class="editor" [(ngModel)]="ruleModel.script" [nzEditorOption]="{ language: 'java'}"></nz-code-editor>
  </ng-container>
</nz-modal>


<nz-modal [(nzVisible)]="reportModelVisible" nzTitle="规则报告" nzWidth="60%" (nzOnCancel)="reportModelVisible=false" [nzFooter]="null" [nzMaskClosable]="false">
  <ng-container *nzModalContent>
    <nz-result [nzStatus]="meterReportModel.status == 'HEALTHY' ? 'success': meterReportModel.status == 'DANGER' ? 'error': 'warning'" style="padding: 0 !important;"></nz-result>
    <app-tool-inspection-meter-report [meterReportModel]="meterReportModel"></app-tool-inspection-meter-report>
  </ng-container>
</nz-modal>
