import {Component, Input, OnInit} from '@angular/core';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {ToolService} from "../../tool.service";
import {StrUtils} from "../../../../utils/StrUtils";
import {markAsDirty} from "../../../../utils/FunctionUtils";
import {SpinningService} from "../../../../service/spinning.service";
import {NzMessageService} from "ng-zorro-antd/message";
import {assign} from "lodash";

@Component({
  selector: 'app-tool-inspection-rule',
  templateUrl: './inspection-rule.component.html',
  styleUrl: './inspection-rule.component.less'
})
export class InspectionRuleComponent implements OnInit {

  constructor(private toolService: ToolService,
              private spinningService: SpinningService,
              private nzMessageService: NzMessageService,
              private formBuilder: UntypedFormBuilder) {

  }

  ngOnInit(): void {
    this.initForm();
  }


  ruleModel: any = {};
  inspectionRuleModel: any[] = [];
  drawerVisible: boolean = false;
  modelVisible: boolean = false;
  validateForm !: UntypedFormGroup;
  @Input() scene !: string;
  meterReportModel: any = {};
  reportModelVisible: boolean = false;


  open(): void {
    this.inspectionRules();
    this.drawerVisible = true;
  }

  close(): void {
    this.drawerVisible = false;
  }

  modalClose() {
    this.modelVisible = false;
    this.ruleModel = {script: ""};
    this.initForm();
  }

  addInspectionRule() {
    this.modelVisible = true;
  }

  editInspectionRule(rule: any) {
    this.ruleModel = rule;
    this.validateForm.patchValue(this.ruleModel, {onlySelf: true});
    this.modelVisible = true;
  }

  deleteInspectionRule(id: number) {
    this.toolService.deleteInspectionRule(id).subscribe(resp => {
      if (resp.success) {
        this.inspectionRules();
      }
    })
  }

  submitInspectionRule() {
    if (StrUtils.isEmpty(this.ruleModel.script)) {
      this.nzMessageService.error("请输入执行脚本");
      return;
    }
    this.ruleModel.scene = this.scene;
    if (this.validateForm.valid) {
      let result = assign(this.ruleModel, this.validateForm.value);
      this.spinningService.open();
      this.toolService.postInspectionRule(result).subscribe(resp => {
        if (resp.success) {
          this.initForm();
          this.modalClose();
          this.inspectionRules();
        }
      })
      return;
    }
    markAsDirty(this.validateForm)
  }


  initForm(): void {
    this.validateForm = this.formBuilder.group({
      name: [null, [Validators.required]],
      component: [null, [Validators.required]],
      type: [null, [Validators.required]],
      memo: [null, [Validators.required]],
      order: [1, [Validators.min(1)]],
      enabled: [true, [Validators.required]],
      action: [null]
    });
  }

  inspectionRules() {
    this.toolService.inspectionRules(this.scene).subscribe(resp => {
      if (resp.success) {
        this.inspectionRuleModel = resp.data;
      }
    })
  }


  executeInspectionRule(ruleModel: any) {
    this.toolService.executeInspectionRule(ruleModel).subscribe(resp => {
      if (resp.success) {
        this.meterReportModel = resp.data;
        this.reportModelVisible = true;
      }
    })
  }

}
