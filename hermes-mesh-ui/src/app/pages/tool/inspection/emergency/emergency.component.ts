import {Component, OnInit, ViewChild} from '@angular/core';
import {ToolService} from "../../tool.service";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {StrUtils} from "../../../../utils/StrUtils";
import {filter, flatMap, forEach, groupBy, keys, lowerCase, size, sortBy} from "lodash";
import {AppRouterService} from "../../../../app-router.service";
import {LocalStorageService} from "ngx-localstorage";
import {presetColors} from "ng-zorro-antd/core/color";
import {SearchUtils} from "../../../../utils/SearchUtils";
import {NzMentionComponent} from "ng-zorro-antd/mention";

@Component({
  selector: 'app-tool-inspection-emergency',
  templateUrl: './emergency.component.html',
  styleUrl: './emergency.component.less'
})
export class EmergencyComponent  extends AppRouterService implements OnInit {

  constructor(router: Router,
              private toolService: ToolService,
              private activatedRoute: ActivatedRoute,
              localStorageService: LocalStorageService) {
    super(router, localStorageService)
    this.activatedRoute.params.subscribe((params: Params) => {
      if (StrUtils.isNotEmpty(params['id'])) {
        this.id = params['id'];
      }
    })
  }

  id: number = 0;
  tags : any = [];
  reportModel: any = {};
  scene: string = "EMERGENCY";
  componentTypes : any[] = [];
  stateModel: any[] = [];
  meterReports : any[] = [];
  meterReportsStore : any[] = [];
  dangerAndWarningMeterReports : any = [];
  healthMeterReports : any = [];
  searchKey : string  ="";
  readonly tagColors = presetColors;
  reportHistoryModel : any = {};
  @ViewChild('mentions') mentionChild!: NzMentionComponent;
  suggestions: any[] = ["ID","组件名称", "指标名称", "智检结果", "智检描述", "质检状态"];
  suggestionMap: any = {"ID": "id", "组件名称": "component", "指标名称": "name", "智检结果": "details", "智检描述": "memo",  "质检状态": "status"};
  protected readonly keys = keys;

  protected readonly size = size;
  protected readonly lowerCase = lowerCase;

  ngOnInit(): void {
    this.inspectionReport();
    this.reportHistory();
  }


  inspectionReport() {
    this.toolService.inspectionReport(this.scene, this.id).subscribe(resp => {
      if (resp.success && resp.data != null) {
        this.onReportView(resp.data);
      }
    })
  }


  onReportView(respModel: any) {
    let model = respModel;
    model.healthy = 0;
    model.warning = 0;
    model.danger = 0;
    let option: any[] = [];
    forEach(model.components, component => {
      component.status = lowerCase(component.status)
      let statusDictionary = groupBy(component.meterReports, meterReport => {
        return meterReport.status;
      });
      component.healthy = size(statusDictionary['HEALTHY'])
      component.warning = size(statusDictionary['WARNING'])
      component.danger = size(statusDictionary['DANGER'])

      model.healthy += component.healthy;
      model.warning +=component.warning;
      model.danger += component.danger;
      option = [...option, component.name]
    })
    model.total = model.healthy + model.warning + model.danger;
    this.reportModel = model;
    this.componentTypes = option;
    this.meterReportsStore = flatMap(model.components, component => {
      return component.meterReports;
    });
    this.meterReportsStore = sortBy(this.meterReportsStore, meterReport => {
      if (meterReport.status == 'DANGER'){
         return 0;
      }else if (meterReport.status == 'WARNING'){
        return 1;
      }
      return  2;
    });

    this.dangerAndWarningMeterReports = filter(this.meterReportsStore, meterReport => {return meterReport.status === 'DANGER' || meterReport.status === 'WARNING'})
    this.healthMeterReports = filter(this.meterReportsStore, meterReport => {return meterReport.status === 'HEALTHY'})
    this.meterReports = this.meterReportsStore;

    let stateModel: any[] = [];
    stateModel.push({"name": "异常指标数", "value": this.reportModel.danger})
    stateModel.push({"name": "正常指标数", "value": this.reportModel.healthy})
    stateModel.push({"name": "告警指标数", "value": this.reportModel.warning})
    stateModel.push({"name": "总智检指标数", "value": this.reportModel.total})
    this.stateModel = stateModel;

    this.navigate(['tool', 'inspection', 'emergency', {"id": this.reportModel.id}]);
  }

  executeInspection() {
    this.toolService.executeInspection(this.scene).subscribe(resp => {
      if (resp.success) {
        this.onReportView(resp.data)
      }
    })
  }

  doSearch() {
    let mentions = this.mentionChild.getMentions();
    if (mentions.length == 0) {
      mentions = this.suggestions;
    }
    this.meterReports = SearchUtils.searchSuggestions(this.searchKey, mentions, this.suggestionMap, this.filterTags());
  }


  private filterTags() {
    if (size(this.tags) == 0) {
      return this.meterReportsStore;
    } else {
      return filter(this.meterReportsStore, meterReport => {
        return this.tags.indexOf(meterReport.component) > -1
      })
    }
  }

  handleTagChange(checked: boolean, tag: any) {
    if (checked) {
      this.tags.push(tag);
    } else {
      // @ts-ignore
      this.tags = this.tags.filter(t => t !== tag);
    }
    this.meterReports = this.filterTags();
  }


  reportHistory(num : number = 0){
    this.toolService.inspectionReportHistory(this.scene,num,20).subscribe(resp =>{
      if (resp.success){
        this.reportHistoryModel = resp.data;
      }
    })
  }
}
