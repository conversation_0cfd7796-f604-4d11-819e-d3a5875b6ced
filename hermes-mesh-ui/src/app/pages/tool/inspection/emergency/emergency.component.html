<nz-row nzGutter="8">
  <nz-col nzSpan="24">
    <nz-card class="shadow-card" [nzBorderless]="true">

      <nz-row>
        <nz-col nzSpan="14">
          <nz-result
            [nzStatus]="reportModel.status == 'HEALTHY' ? 'success': reportModel.status == 'DANGER' ? 'error': 'warning'"
            style="padding: 0 !important;" [nzSubTitle]="subTitleTemplate">
            <ng-template #subTitleTemplate>
              <div style="text-align: center">
                <p>
                  <span nz-typography><strong>智检ID:{{ reportModel.id }}</strong></span>
                </p>
                <p>
              <span
                nz-typography><strong>智检时间:{{ reportModel.createdAt | date: 'yyy-MM-dd HH:mm:ss' }}</strong></span>
                </p>
              </div>
            </ng-template>
          </nz-result>

          <div style="height: 150px">
            <ngx-charts-number-card [results]="stateModel" [cardColor]="'#232837'"></ngx-charts-number-card>
          </div>

          <nz-descriptions nzBordered [nzColumn]="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"  class="aiInspectionReport">
            <nz-descriptions-item nzSpan=4 nzTitle="组件状态总览">
              <nz-table #componentOverviewTable [nzData]="reportModel.aiInspectionReport?.componentOverview" nzShowPagination=false>
                <thead>
                <tr>
                  <th nzWidth="120px">组件</th>
                  <th>状态</th>
                  <th>描述</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let data of componentOverviewTable.data">
                  <td nzLeft>
                    <span nz-typography><strong>{{ data.componentName }}</strong></span>
                  </td>
                  <td>
                    <i nz-icon
                       [nzType]="data.status == 'HEALTHY' ? 'check-circle': data.status == 'DANGER' ? 'close-circle':'warning'"
                       [nzTheme]="'twotone'"
                       [nzTwotoneColor]="data.status == 'HEALTHY' ? '#52c41a': data.status == 'DANGER' ? '#eb2f96':'#9c7fa5'"></i>
                  </td>
                  <td>{{ data.description }}</td>
                </tr>
                </tbody>
              </nz-table>
            </nz-descriptions-item>
            <nz-descriptions-item nzSpan=4 nzTitle="异常与应急操作">
              <nz-table #exceptionAndActionDetailsTable [nzData]="reportModel.aiInspectionReport?.exceptionAndActionDetails" nzShowPagination=false>
                <thead>
                <tr>
                  <th nzWidth="120px">组件</th>
                  <th>异常指标</th>
                  <th>理由</th>
                  <th>应急操作</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let data of exceptionAndActionDetailsTable.data">
                  <td nzLeft>
                    <span nz-typography><strong>{{ data.componentName }}</strong></span>
                  </td>
                  <td>{{ data.abnormalMetric }}</td>
                  <td>{{ data.reason }}</td>
                  <td>
                    <ngx-json-viewer [json]="data.emergencyActions  | coverJsonObject" [expanded]="true"></ngx-json-viewer>
                  </td>
                </tr>
                </tbody>
              </nz-table>
            </nz-descriptions-item>
          </nz-descriptions>
          <nz-table nzShowPagination=false [nzTemplateMode]="true" style="margin-right: 8px">
            <thead>
            <tr>
              <th>验证项</th>
              <th>结果说明</th>
            </tr>
            </thead>
            <tbody>
              <tr>
                <td nzLeft>
                  <span nz-typography><strong>是否遗漏DANGER/WARNING指标</strong></span>
                </td>
                <td>{{ reportModel.aiInspectionReport.selfValidationResult.missingDangerWarningMetrics}}</td>
              </tr>
              <tr>
                <td nzLeft>
                  <span nz-typography><strong>是否存在结论与数据矛盾</strong></span>
                </td>
                <td>{{ reportModel.aiInspectionReport.selfValidationResult.dataContradictionsExist}}</td>
              </tr>
              <tr>
                <td nzLeft>
                  <span nz-typography><strong>应急操作可操作性检查</strong></span>
                </td>
                <td>{{ reportModel.aiInspectionReport.selfValidationResult.emergencyActionsOperable}}</td>
              </tr>
              <tr>
                <td nzLeft>
                  <span nz-typography><strong>最终验证结论</strong></span>
                </td>
                <td>{{ reportModel.aiInspectionReport.selfValidationResult.finalConclusion}}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>

        <nz-col nzSpan="10">
          <blockquote class="block-quote">
            <nz-row nzJustify="space-between">
              <nz-col>
                <span nz-typography><strong>指标数量:{{ meterReports.length }}</strong></span>
                @for (component of componentTypes; track component; let i = $index) {
                  <nz-tag [nzColor]="tags.indexOf(component) > -1 ? tagColors[i] : ''" [nzBordered]="false"
                          nzMode="checkable" [nzChecked]="tags.indexOf(component) > -1"
                          (nzCheckedChange)="handleTagChange($event, component)">{{ component }}
                  </nz-tag>
                }
              </nz-col>
              <nz-col>
                <a nz-tooltip nzTooltipTitle="配置脚本所需的参数信息" (click)="scriptParamComponent.open()" class="cursor-pointer">参数配置</a>
                <nz-divider nzType="vertical"></nz-divider>
                <a nz-tooltip nzTooltipTitle="主动执行智检" (click)="executeInspection()" class="cursor-pointer">执行自检</a>
                <nz-divider nzType="vertical"></nz-divider>
                <a nz-tooltip nzTooltipTitle="脚本配置" (click)="inspectionRuleComponent.open()" class="cursor-pointer">脚本配置</a>
                <nz-divider nzType="vertical"></nz-divider>
                <a nz-tooltip nzTooltipTitle="查看报告执行记录" (click)="reportHistoryComponent.open()" class="cursor-pointer">报告记录</a>
              </nz-col>
            </nz-row>
          </blockquote>
          <nz-mention #mentions [nzSuggestions]="suggestions">
            <nz-input-group nzCompact nzSearch [nzAddOnAfter]="instanceSuffixIconButton">
              <input type="text" nzMentionTrigger [(ngModel)]="searchKey" placeholder="" nz-input (keyup)="doSearch()" />
            </nz-input-group>
          </nz-mention>
          <ng-template #instanceSuffixIconButton>
            <button nz-button nzType="primary" nzSearch (click)="doSearch()"><span nz-icon nzType="search"></span>
            </button>
          </ng-template>
          <nz-collapse [nzBordered]="false" class="meterReport">
            @for (meterReport of meterReports; track meterReport) {
              <nz-collapse-panel [nzHeader]="headerTemplate" [nzActive]="meterReport.active" [ngStyle]="{'margin-bottom': '12px',
           border: '0px', 'border-left': meterReport.status == 'HEALTHY' ? '4px solid #52c41a' : meterReport.status == 'DANGER' ? '4px solid #eb2f96': '4px solid #9c7fa5'}"
                                 [nzExtra]="extraTemplate">
                <app-tool-inspection-meter-report [meterReportModel]="meterReport"></app-tool-inspection-meter-report>
              </nz-collapse-panel>

              <ng-template #headerTemplate>
                <span nz-typography><strong nz-tooltip nzTooltipTitle="智检结果">{{ meterReport.details }}</strong></span>
              </ng-template>
              <ng-template #extraTemplate>
                <span nz-typography><strong nz-tooltip nzTooltipTitle="智检指标">{{ meterReport.name }}</strong></span>
                <nz-divider nzType="vertical"></nz-divider>
                <i nz-icon
                   [nzType]="meterReport.status == 'HEALTHY' ? 'check-circle': meterReport.status == 'DANGER' ? 'close-circle':'warning'"
                   [nzTheme]="'twotone'"
                   [nzTwotoneColor]="meterReport.status == 'HEALTHY' ? '#52c41a': meterReport.status == 'DANGER' ? '#eb2f96':'#9c7fa5'"></i>
              </ng-template>
            }
          </nz-collapse>
          <!--TODO 分页插件支持-->

        </nz-col>
      </nz-row>
    </nz-card>
  </nz-col>

</nz-row>
<app-tool-inspection-rule #inspectionRuleComponent scene="EMERGENCY"></app-tool-inspection-rule>
<app-tool-inspection-report-history #reportHistoryComponent scene="EMERGENCY" (reportView)="onReportView($event)"></app-tool-inspection-report-history>
<app-tool-inspection-script-param #scriptParamComponent></app-tool-inspection-script-param>
