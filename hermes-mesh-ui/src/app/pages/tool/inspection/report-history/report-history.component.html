<nz-drawer [nzBodyStyle]="{ height: 'calc(100% - 55px)'}" nzWidth="calc(50% - 118px)" nzTitle="报告历史" [nzVisible]="drawerVisible" (nzOnClose)="close()">
  <ng-container *nzDrawerContent>

    <nz-table #modelTable [nzData]="reportHistoryModel.records" [nzShowTotal]="totalTemplate" [nzTotal]="reportHistoryModel.total" nzShowSizeChanger
              [nzPageSize]="reportHistoryModel.size" [nzPageIndex]="reportHistoryModel.current" [nzFrontPagination]="false" (nzPageIndexChange)="reportHistory($event)" (nzPageSizeChange)="onPageSizeChange($event)">
      <thead>
      <tr>
        <th>ID</th>
        <th>状态</th>
        <th>时间</th>
        <th nzWidth="60px">操作</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let reportModel of modelTable.data">
        <td>{{reportModel.id}}</td>
        <td>
          <i nz-tooltip [nzTooltipTitle]="lowerCase(reportModel.status)" nz-icon [nzType]="reportModel.status == 'HEALTHY' ? 'check-circle':'close-circle'" [nzTheme]="'twotone'"
             [nzTwotoneColor]="reportModel.status == 'HEALTHY' ? '#52c41a': reportModel.status == 'DANGER' ? 'red':'#eb2f96'"></i>
        </td>
        <td>
          {{ reportModel.createdAt | date: 'yyy-MM-dd HH:mm:ss'}}
        </td>
        <td>
          <a (click)="reportDetails(reportModel)">详情</a>
        </td>
      </tr>
      </tbody>
    </nz-table>
    <ng-template #totalTemplate let-range="range" let-total>
      共{{ total }} 条
    </ng-template>


  </ng-container>
</nz-drawer>



