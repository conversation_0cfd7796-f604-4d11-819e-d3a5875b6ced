import {Component, EventEmitter, Input, Output} from '@angular/core';
import {ToolService} from "../../tool.service";
import {AppBaseService} from "../../../../app-base.service";
import {LocalStorageService} from "ngx-localstorage";
import {lowerCase} from "lodash";

@Component({
  selector: 'app-tool-inspection-report-history',
  templateUrl: './report-history.component.html',
  styleUrl: './report-history.component.less'
})
export class ReportHistoryComponent extends AppBaseService{

  constructor (private toolService : ToolService,
               localStorageService: LocalStorageService){
    super(localStorageService)
  }


  drawerVisible  : boolean = false;
  reportHistoryModel : any = {};
  @Input() scene: string = "EMERGENCY";
  protected readonly lowerCase = lowerCase;
  @Output() reportView = new EventEmitter<any>();


  open() {
    this.reportHistory(0)
    this.drawerVisible = true;
  }

  reportHistory(num : number = 0){
    this.toolService.inspectionReportHistory(this.scene,num,this.getPageSize()).subscribe(resp =>{
      if (resp.success){
        this.reportHistoryModel = resp.data;
      }
    })
  }

  onPageSizeChange(num : number){
    this.putPageSize(num);
    this.reportHistory(0);
  }

  close(){
    this.drawerVisible = false;
  }



  reportDetails(reportModel: any) {
    this.reportView.emit(reportModel);
    this.close();
  }
}
