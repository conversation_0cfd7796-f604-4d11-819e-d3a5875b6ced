import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {DataComponent} from "./data.component";
import {ConfigComponent} from "./config/config.component";

const routes: Routes = [{
  path: '', component: DataComponent, children: [
    {path: 'access', loadChildren: () => import('./access/access.module').then(m => m.AccessModule)},
    {path: 'commandKey', loadChildren: () => import('./command-key/command-key.module').then(m => m.CommandKeyModule)},
    {path: 'config', component: ConfigComponent},
    {path: '', redirectTo: 'access', pathMatch: 'full'},
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DataRoutingModule {
}
