{"name": "hermes-mesh-ui", "version": "0.2.8", "scripts": {"ng": "ng", "start": "ng serve --port 8000 --proxy-config proxy.config.json", "build": "ng build --aot --output-hashing=all", "watch": "ng build --watch --configuration development", "serve:qiankun": "ng serve --port 8000 --disable-host-check  --proxy-config proxy.config.json   --live-reload false", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.2", "@angular/common": "^17.3.2", "@angular/compiler": "^17.3.2", "@angular/core": "^17.3.2", "@angular/forms": "^17.3.2", "@angular/platform-browser": "^17.3.2", "@angular/platform-browser-dynamic": "^17.3.2", "@angular/router": "^17.3.2", "@ant-design/icons-angular": "^17.0.0", "@antv/g2plot": "^2.4.31", "@swimlane/ngx-charts": "^20.5.0", "@swimlane/ngx-graph": "^8.3.0", "@tinymce/tinymce-angular": "^5.0.0", "echarts": "^5.4.2", "jquery": "^3.6.0", "lodash": "^4.17.21", "marked": "^15.0.12", "moment": "^2.29.4", "monaco-editor": "^0.50.0", "ng-zorro-antd": "17.3.0", "ngx-clipboard": "^16.0.0", "ngx-colors": "^3.5.2", "ngx-cookie": "^6.0.1", "ngx-echarts": "v15.0.3", "ngx-ellipsis": "^4.1.3", "ngx-json-viewer": "^3.2.1", "ngx-localstorage": "^6.0.0", "ngx-markdown": "^20.0.0", "rxjs": "~7.5.0", "tslib": "^2.3.0", "webpack": "^5.73.0", "zone.js": "~0.14.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.1", "@angular-devkit/build-angular": "^17.3.2", "@angular/cli": "~17.3.2", "@angular/compiler-cli": "^17.3.2", "@types/d3-scale": "^4.0.3", "@types/d3-selection": "^3.0.4", "@types/d3-shape": "^3.1.1", "@types/echarts": "^4.9.17", "@types/jasmine": "~4.0.0", "@types/lodash": "^4.14.194", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.4.3"}, "overrides": {"@angular/common": "^17.3.2", "@angular/core": "^17.3.2", "@angular/animations": "^17.3.2", "@angular/compiler": "^17.3.2", "@angular/platform-browser": "^17.3.2"}}