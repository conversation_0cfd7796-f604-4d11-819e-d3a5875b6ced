{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"hermes-mesh-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "../hermes-mesh-panel/src/main/resources/static", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "customWebpackConfig": {"path": "./custom-webpack.config.js"}, "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}, {"glob": "**/*", "input": "./node_modules/monaco-editor/min/vs", "output": "/assets/vs/"}], "styles": ["src/styles.less", "node_modules/prismjs/themes/prism-okaidia.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-csharp.min.js", "node_modules/prismjs/components/prism-css.min.js"], "allowedCommonJsDependencies": ["rfdc", "lodash", "size-sensor", "d3-regression", "fmin", "pdfast", "clone-deep", "dagre", "webcola", "moment"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "512kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"buildTarget": "hermes-mesh-ui:build:production"}, "development": {"buildTarget": "hermes-mesh-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "hermes-mesh-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": []}}}}}, "cli": {"analytics": false}}