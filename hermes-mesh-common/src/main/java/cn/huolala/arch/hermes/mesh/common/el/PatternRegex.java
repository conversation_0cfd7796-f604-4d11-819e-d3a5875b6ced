package cn.huolala.arch.hermes.mesh.common.el;

import cn.hutool.core.collection.CollectionUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PatternRegex {

    private PatternRegex() {
    }

    private static List<String> regex(String input, Pattern pattern) {
        List<String> results = new ArrayList<>();
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();
            String name = input.substring(start + 2, end - 1);
            results.add(name);
        }
        return results;
    }


    public static String regexFirst(String input, Pattern pattern) {
        List<String> regex = regex(input, pattern);
        if (CollectionUtil.isNotEmpty(regex)) {
            return regex.get(0);
        }
        return null;
    }


    public static Map<String, String> regexMap(String input, Pattern pattern) {
        Map<String, String> regexMap = new HashMap<>();
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            regexMap.put(matcher.group(2), matcher.group(1));
        }
        return regexMap;
    }


    /**
     * @param input 输入
     * @return isMatcher
     */
    public static boolean isMatcher(String input, Pattern pattern) {
        return pattern.matcher(input).find();
    }
}
