package cn.huolala.arch.hermes.mesh.common.el;

import cn.huolala.arch.hermes.mesh.common.model.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语法解析规则
 * 假定响应体如：
 * {
 *     "msg":"成功",
 *     "code":1,
 *     "data":{
 *         "data":[
 *             {
 *                 "id":"5d81e38fd06b281c95f16c89",
 *                 "deleted":0,
 *                 "createTime":"2019-09-26T08:30:38.585+0000",
 *                 "updateTime":"2019-09-26T08:30:38.585+0000",
 *                 "name":"测试3",
 *                 "url":"127.0.0.1:8090/httpLink/links.query",
 *                 "protocol":"HTTP",
 *                 "method":"GET",
 *                 "content":"APPLICATION_JSON",
 *                 "charset":"UTF8",
 *                 "params":[
 *                     "searchKey=测试&pageNum=2"
 *                 ]
 *             },
 *             {
 *                 "id":"5d849b68d06b2835f8ee1b4a",
 *                 "deleted":0,
 *                 "createTime":"2019-09-24T01:59:19.449+0000",
 *                 "updateTime":"2019-09-24T01:59:19.449+0000",
 *                 "name":"获取场景信息",
 *                 "url":"127.0.0.1:8090/httpScene/scenes.query",
 *                 "protocol":"HTTP",
 *                 "method":"GET",
 *                 "content":"APPLICATION_JSON",
 *                 "charset":"UTF8",
 *             }
 *         ],
 *         "count":2,
 *         "pageSize":10,
 *         "pageNum":0
 *     },
 *     "success":true
 * }
 *  1、当前json体 一级root 共有4个字段  code，msg，data，success
 *  取一级root字段
 *  code
 *  msg
 *  data
 *  success
 *
 *  2、取二级root字段 当前仅有data对象
 *  data.data
 */
public class ElExpression {

    private ElExpression(){}

    /**
     * 表达式头部标志
     */
    private static final String HEADER = "header.";

    /**
     * 表达式body标志
     */
    private static final String BODY = "body.";


    public static Map<String, Object> expression(List<Pair> pairs, JSON responseJson, List<Pair> responseHeader) {
        Map<String, Object> epMap = new HashMap<>(pairs.size());
        pairs.forEach(pair -> {
            String name = pair.getName();
            String oV = pair.getValue();
            String nV = expression(oV, responseJson,responseHeader);
            if (StrUtil.isAllNotEmpty(name,nV)){
                epMap.put(name,nV);
            }
        });
        return epMap;
    }


    public static String expression(String expression,JSON responseJson,List<Pair> responseHeader){
        if (StrUtil.isEmpty(expression))return null;
        if (expression.startsWith(HEADER)){
            return doExpression(expression.replaceFirst(HEADER,""),responseHeader);
        }
        if (expression.startsWith(BODY)){
            return doExpression(expression.replaceFirst(BODY,""),responseJson);
        }
        return null;
    }


    private static String doExpression(String expression,JSON responseJson){
        try {
            Object result = JSONUtil.getByPath(responseJson, expression);
            if (result == null){
                return null;
            }
            return String.valueOf(result);
        }catch (Exception e){
            return null;
        }
    }


    private static String doExpression(String name, List<Pair> pairs){
        for (Pair pair : pairs){
            if (StrUtil.equalsIgnoreCase(name, pair.getName())){
                return pair.getValue();
            }
        }
        return null;
    }
}
