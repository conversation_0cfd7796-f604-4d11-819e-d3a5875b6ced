package cn.huolala.arch.hermes.mesh.common.enums;


public enum AlertEventType {
    RATE_LIMIT_CONFIG_CHANGE("限流配置变更", AlertLevel.INFO, false, true, false),
    CIRCUIT_CONFIG_CHANGE("熔断配置变更", AlertLevel.INFO, false, true, false),
    DEGRADE_CONFIG_CHANGE("降级配置变更", AlertLevel.INFO, false, true, false),
    AUTH_CONFIG_CHANGE("鉴权配置变更", AlertLevel.INFO, false, true, false),
    TIMEOUT_CONFIG_CHANGE("超时配置变更", AlertLevel.INFO, false, true, false),
    LOG_CONFIG_CHANGE("日志级别配置变更", AlertLevel.INFO, false, true, false),
    ZONE_CONFIG_CHANGE("泳道路由配置变更", AlertLevel.INFO, false, true, false),
    CIRCUIT_STATUS_EVENT("熔断状态事件", AlertLevel.INFO, false, true, false),
    UP_DOWN_EVENT("上下线事件", AlertLevel.INFO, false, true, false),
    OPERATION_UP_DOWN_EVENT("操作上下线", AlertLevel.WARN, false, true, false),
    RECONNECTION_EVENT("服务重连事件", AlertLevel.WARN, false, true, false),
    INSPECTION_WARNING("智检告警异常", AlertLevel.WARN, false, true, false),
    // TODO 预警提示不允许自定义，都打开
    RATE_LIMIT_EARLY_WARNING("限流预警提示", AlertLevel.INFO, false, false, false),
    TIME_OUT_EARLY_WARNING("超时预警提示", AlertLevel.INFO, false, false, false),
    VERSION_UPGRADE("版本升级通知", AlertLevel.INFO, false, false, false),
    TASK_EXECUTE("定时任务执行", AlertLevel.INFO, false, false, false),
    APP_WARM_UP_ABNORMAL("Java服务预热异常通知", AlertLevel.INFO, false, false, true);


    /**
     * 标题
     */
    private final String title;

    private final AlertLevel defaultLevel;

    private final boolean defaultAck;

    /**
     * 可配置
     */
    private final boolean custom;


    /**
     * 固定通知，自己配置通知人和组
     */
    private final boolean fixed;

    AlertEventType(String title, AlertLevel defaultLevel, boolean defaultAck, boolean custom, boolean fixed) {
        this.title = title;
        this.defaultLevel = defaultLevel;
        this.defaultAck = defaultAck;
        this.custom = custom;
        this.fixed = fixed;
    }

    public AlertLevel getDefaultLevel() {
        return defaultLevel;
    }

    public String getTitle() {
        return title;
    }

    public boolean getDefaultAck() {
        return defaultAck;
    }

    public boolean isCustom() {
        return custom;
    }

    public boolean isFixed() {
        return fixed;
    }
}
