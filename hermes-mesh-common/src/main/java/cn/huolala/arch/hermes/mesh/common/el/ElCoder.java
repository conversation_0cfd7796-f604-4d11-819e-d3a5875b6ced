package cn.huolala.arch.hermes.mesh.common.el;

import cn.huolala.arch.hermes.mesh.common.el.function.Function;
import cn.huolala.arch.hermes.mesh.common.model.Pair;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class ElCoder {

    private ElCoder(){}

    /**
     * 模式匹配
     * ${name} 取值表达式模式
     * ${sys.random(1,10)} 函数表达式模式
     */
    public static final Pattern PATTERN = Pattern.compile("(\\$\\{(\\w+|,|'|\"|\\.|-|\\(|\\)|[\\u4e00-\\u9fa5])*})",Pattern.CASE_INSENSITIVE);
    public static final Pattern PATTERN$$ = Pattern.compile("(\\$\\$\\{(\\w+|,|'|\"|\\.|-|\\(|\\)|[\\u4e00-\\u9fa5])*})",Pattern.CASE_INSENSITIVE);


    /**
     * 将输入的kv对中v解码 如name =${code}
     * @param pairs kv对
     * @param contextParam 上下文参数
     * @return 解码后的kv对 name=1
     */
    public static List<Pair> decoder(List<Pair> pairs, Map<String, Object> contextParam) {
        List<Pair> dp = new ArrayList<>(pairs.size());
        pairs.forEach(pair -> {
            Pair p = new Pair();
            String value = decoder(pair.getValue(),contextParam);
            p.setName(pair.getName());
            p.setValue(value);
            dp.add(p);
        });
        return dp;
    }



    /**
     * 将输入字符串解码
     * @param input this is a ${fruit}
     * @param contextParam  {fruit = apple}
     * @return this is a apple
     */
    public static String decoder(String input, Map<String, Object> contextParam){
        return decoder(input, contextParam,false,PATTERN);
    }


    /**
     * 将输入字符串解码
     * @param input this is a ${fruit}
     * @param contextParam  {fruit = apple}
     * @return this is a apple
     */
    public static String decoder(String input, Map<String, Object> contextParam, Pattern pattern){
        return decoder(input, contextParam,false,pattern);
    }




    /**
     * 将输入字符串解码 是否移除
     * @param input this is ${fruit}
     * @param contextParam {fruit = apple}
     * @param remove true | false
     * @return  this is apple
     */
    public static String decoder(String input, Map<String, Object> contextParam,boolean remove,Pattern pattern){
        if (StrUtil.isEmpty(input)){
            return input;
        }
        boolean matcher = pattern.matcher(input).find();
        if (!matcher){
            return input;
        }
        final String[] result = new String[]{input};
        Map<String, String> matcherNameMap = PatternRegex.regexMap(input,pattern);
        matcherNameMap.forEach((k,v) ->{
            /*
             * 如果是系统函数调用
             */
            String nv = null;
            if (Function.isFunctionMatcher(v)){
                try {
                    nv = String.valueOf(Function.doInvoke(k));
                    result[0] = result[0].replace(v,String.valueOf(nv));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else {
                nv = Convert.toStr(contextParam.get(k));
                if (remove){
                    contextParam.remove(k);
                }
                nv = decoder(nv,contextParam,remove,pattern);
            }
            if (nv != null){
                result[0] = result[0].replace(v, nv);
            }
        });
        //不支持嵌套模式，容易出现死循环
      /*  if (Function.isFunctionMatcher(result[0])){
            result[0] = decoder(result[0],contextParam,remove);
        }*/
        return result[0];
    }



    /**
     * 将输入的表达式解释执行
     * @return 返回结果
     */
    public static boolean decoder(String expression){
        boolean isSuccess;
        try {
            isSuccess = (Boolean) Function.nativeInvoker(expression);
        } catch (Exception e) {
            isSuccess = false;
        }
        return isSuccess;
    }

}
