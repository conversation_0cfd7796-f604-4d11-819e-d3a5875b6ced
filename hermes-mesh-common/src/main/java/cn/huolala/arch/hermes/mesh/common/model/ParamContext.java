package cn.huolala.arch.hermes.mesh.common.model;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ParamContext extends HashMap<String, Object> {

    private static final String START_TIME = "startTime";
    private static final String END_TIME = "endTime";

    public static ParamContext of() {
        return new ParamContext();
    }

    public static ParamContext of(ParamContext context) {
        ParamContext paramContext = new ParamContext();
        paramContext.putAll(context);
        return paramContext;
    }

    public static ParamContext of(Map<String, Object> context) {
        ParamContext paramContext = new ParamContext();
        Opt.ofNullable(context).ifPresent(paramContext::putAll);
        return paramContext;
    }

    /**
     * 获取开始时间
     */
    public Long getStartTimeOrBefore1Hour() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetHour(new Date(), -1).getTime();
        }
        return startTime;
    }

    /**
     * 获取开始时间
     */
    public Long getStartTimeOrBefore5Min() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetMinute(new Date(), -5).getTime();
        }
        return startTime;
    }

    /**
     * 获取60S前的时间
     */
    public Long getBefore60Seconds() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetMinute(new Date(), -1).getTime();
        }
        return startTime;
    }

    /**
     * 获取30S前的时间
     */
    public Long getBefore30Seconds() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetSecond(new Date(), -30).getTime();
        }
        return startTime;
    }

    /**
     * 获取1day前的时间
     */
    public Long getBefore1DaySeconds() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetDay(new Date(), -1).getTime();
        }
        return startTime;
    }

    /**
     * 获取5min前的时间
     */
    public Long getBefore5MinSeconds() {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetMinute(new Date(), -5).getTime();
        }
        return startTime;
    }

    /**
     * 获取Nmin前的时间
     */
    public Long getBeforeNMinSeconds(int n) {
        Long startTime = (Long) get(START_TIME);
        if (startTime == null) {
            startTime = DateUtil.offsetMinute(new Date(), -n).getTime();
        }
        return startTime;
    }

    /**
     * 获取结束时间
     */
    public Long getEndTimeOrNow() {
        return (Long) getOrDefault(END_TIME, DateUtil.current());
    }


}
