package cn.huolala.arch.hermes.mesh.common.enums;

import lombok.Getter;

public enum InspectionComponent {
    ALL("all"),
    CONSUL("consul"),
    APOLLO("apollo"),
    SOA("soa"),
    JAF("jaf"),
    UUID("uuid"),
    XXLJOB("xxljob"),
    MESHLB("meshlb"),
    GOPROXY("goproxy"),
    DATAMESH("datamesh"),
    KONG("kong"),
    GATEWAY("gateway"),
    DAL("dal");

    @Getter
    final String memo;

    InspectionComponent(String memo) {
        this.memo = memo;
    }
}
