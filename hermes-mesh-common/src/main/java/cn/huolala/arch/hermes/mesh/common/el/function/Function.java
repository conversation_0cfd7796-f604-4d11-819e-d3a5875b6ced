package cn.huolala.arch.hermes.mesh.common.el.function;

import cn.huolala.arch.hermes.mesh.common.el.ElCoder;
import cn.huolala.arch.hermes.mesh.common.el.PatternRegex;
import cn.huolala.arch.hermes.mesh.common.el.function.annotation.Explain;
import cn.huolala.arch.hermes.mesh.common.el.function.annotation.MethodExplain;
import cn.huolala.arch.hermes.mesh.common.el.function.core.CommonFunction;
import cn.huolala.arch.hermes.mesh.common.el.function.core.EncryptFunction;
import cn.huolala.arch.hermes.mesh.common.el.function.core.RandomFunction;
import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import org.nutz.el.El;
import org.nutz.lang.Lang;
import org.nutz.lang.util.Context;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 *  函数
 */
public class Function {

    /**
     * 函数说明列表
     */
    private static final List<Describe> functionDescribe = new ArrayList<>();

    /**
     * 函数模式匹配
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{\\w+\\.\\w+\\(.*\\)}");


    /**
     * 函数调用map集合
     */
    public static final ConcurrentHashMap<String, Context> FUNCTION_CONTEXT = new ConcurrentHashMap<>();


    static {
        doRegister(new CommonFunction());
        doRegister(new RandomFunction());
        doRegister(new EncryptFunction());
    }


    /**
     * el 表达式   function.method()
     */
    public static Object doInvoke(String input){
        List<String> typeMethods = StrUtil.splitTrim(input, ".");
        String prefix = typeMethods.get(0);
        Context context = FUNCTION_CONTEXT.get(prefix);
        if (context == null){
            throw new UnsupportedOperationException(StrUtil.format("不支持函数调用类型：{}",prefix));
        }
        return El.eval(context,input);
    }


    /**
     * 原生调用
     * 利用 el 原生支持
     */
    public static Object nativeInvoker(String input)throws Exception {
        return El.eval(input);
    }


    public static Object invoke(String input) throws Exception {
        if (!isFunctionMatcher(input)){
            throw new FunctionMatcherException(StrUtil.format("函数格式错误：{}",input));
        }
        String method = PatternRegex.regexFirst(input, ElCoder.PATTERN);
        if(StrUtil.isEmpty(method)){
            throw new FunctionMatcherException(StrUtil.format("不支持方法调用：{}",input));
        }
        return doInvoke(method);
    }


    private static void doRegister(cn.huolala.arch.hermes.mesh.common.el.function.core.Function function) {
        String prefix = getFunctionPrefix(function);
        Context context = Lang.context();
        context.set(prefix, function.getClass());
        FUNCTION_CONTEXT.put(prefix,context);

        Describe describe = new Describe();
        describe.setName(getFunctionName(function));
        describe.setMethods(getMethods(function));
        functionDescribe.add(describe);
    }


    private static List<Describe.MethodDescribe> getMethods(cn.huolala.arch.hermes.mesh.common.el.function.core.Function function) {
        List<Describe.MethodDescribe> methodDescribes = new ArrayList<>();
        Method[] methods = function.getClass().getMethods();
        for (Method method : methods){
            MethodExplain annotation = method.getAnnotation(MethodExplain.class);
            if (annotation != null){
                Describe.MethodDescribe describe = new Describe.MethodDescribe();
                describe.setName(annotation.name());
                describe.setDemo(annotation.demo());
                methodDescribes.add(describe);
            }
        }
        return methodDescribes;
    }


    private static String getFunctionName(cn.huolala.arch.hermes.mesh.common.el.function.core.Function function){
        Explain annotation = AnnotationUtil.getAnnotation(function.getClass(),Explain.class);
        Assert.notNull(annotation);
        return annotation.name();
    }


    private static String getFunctionPrefix(cn.huolala.arch.hermes.mesh.common.el.function.core.Function function){
        Explain annotation = AnnotationUtil.getAnnotation(function.getClass(),Explain.class);
        Assert.notNull(annotation);
        return annotation.prefix();
    }


    public static List<Describe> functionDescribes(){
        return functionDescribe;
    }

    /**
     * 输入内容是否匹配函数表达式
     */
    public static boolean isFunctionMatcher(String input){
        return PATTERN.matcher(input).find();
    }

}
