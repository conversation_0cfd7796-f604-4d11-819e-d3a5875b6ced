package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionRuleBo extends Bo {

    /**
     * 规则ID
     */
    private Long id;

    /**
     * 规则名称
     * 唯一
     */
    private String name;

    /**
     * 规则执行脚本
     */
    private String script;

    /**
     * 脚本备注
     */
    private String memo;

    /**
     * 规则场景
     */
    private InspectionScene scene;

    /**
     * 所属组件
     * CONSUL,APOLLO,SOA,GOPROXY
     */
    private InspectionComponent component;

    /**
     * 作用类型
     */
    private InspectionType type;

    /**
     * 排序
     */
    private int order;

    /**
     * 是否启用
     */
    private boolean enabled;

    /**
     * 应急动作
     */
    private String action;

    /**
     * 更新时间
     */
    protected Date updatedAt = new Date();
}
