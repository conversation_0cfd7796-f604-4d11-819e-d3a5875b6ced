package cn.huolala.arch.hermes.mesh.api;

import cn.huolala.arch.hermes.mesh.api.bos.inspection.InspectionReportBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.InspectionRuleBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.MeterReportBo;
import cn.huolala.arch.hermes.mesh.api.bos.inspection.ScriptParamBo;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionScene;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.model.ParamContext;
import cn.lalaframework.soa.annotation.SOAService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

@SOAService(value = "/rpcInspectionService", appId = "ci-hermes-sentinel-svc")
public interface RpcInspectionService {

    /**
     * 查找自检报告
     */
    RespMsg<InspectionReportBo> inspectionReport(InspectionScene scene,Long id);

    /**
     * 执行自检
     */
    RespMsg<InspectionReportBo> executeInspection(InspectionScene scene, ParamContext context);

    /**
     * 自检规则
     */
    RespMsg<List<InspectionRuleBo>> inspectionRules(InspectionScene scene);

    /**
     * 更新规则
     */
    RespMsg<InspectionRuleBo>  updateInspectionRule(InspectionRuleBo ruleBo);

    /**
     * 执行规则自检
     */
    RespMsg<MeterReportBo> executeInspectionRule(InspectionRuleBo ruleBo, ParamContext context);


    /**
     * 自检报告历史
     */
    RespMsg<Page<InspectionReportBo>> inspectionReportHistory(int num, int size,InspectionScene scene);

    /**
     * 删除自检规则
     */
    void deleteInspectionRule(Long id);

    /**
     * 请求参数列表
     */
    List<ScriptParamBo> scriptParams();

    /**
     * 创建请求参数
     */
    void postScriptParam(ScriptParamBo paramBo);

    /**
     * 更新请求参数
     */
    void putScriptParam(ScriptParamBo paramBo);

    /**
     * 删除请求参数
     */
    void deleteScriptParam(Long id);

}
