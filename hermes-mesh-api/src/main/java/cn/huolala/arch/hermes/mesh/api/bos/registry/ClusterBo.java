package cn.huolala.arch.hermes.mesh.api.bos.registry;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import lombok.Data;

import java.util.List;


@Data
public class ClusterBo extends Bo {

    /**
     * server规模
     */
    private int servers;

    /**
     * 节点数量(排除了server节点)
     */
    private int agents;

    /**
     * 服务数量
     */
    private int services;

    /**
     * 容错数量
     */
    private int tolerance;

    /**
     * leader信息
     */
    private AgentBo agent;

    /**
     * members列表
     */
    private List<MemberBo> members;

    /**
     * server状态
     */
    private AutopilotStateBo autopilotState;
}
