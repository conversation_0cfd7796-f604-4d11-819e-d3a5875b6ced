package cn.huolala.arch.hermes.mesh.api.bos.registry;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import lombok.Data;

import java.util.Map;

@Data
public class AgentBo extends Bo {

    public ConfigBo config;

    public Map<String,Object> debugConfig;

    public MemberBo member;

    public Map<String, String> meta;

    public Map<String, Object> coord;

    public Map<String, Object> stats;

    @Data
    public static class ConfigBo {

        public String datacenter;

        public String nodeName;

        public String revision;

        public boolean server;

        public String version;
    }

}
