package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import lombok.Data;

import java.util.List;

@Data
public class AiInspectionReportBo {

    /**
     * 组件状态总览
     */
    private List<ComponentOverview> componentOverview;

    /**
     * 异常与应急操作明细
     */
    private List<ExceptionAndActionDetail> exceptionAndActionDetails;

    /**
     * 自我验证结果
     */
    private SelfValidationResult selfValidationResult;


    @Data
    public static class ComponentOverview {

        private String componentName;

        private String status;

        private String description;

    }

    @Data
    public static class ExceptionAndActionDetail {

        private String componentName;

        private String abnormalMetric;

        private String reason;

        private List<String> emergencyActions;
    }


    @Data
    public static class SelfValidationResult {

        private String missingDangerWarningMetrics;

        private String dataContradictionsExist;

        private String emergencyActionsOperable;

        private String finalConclusion;
    }
}
