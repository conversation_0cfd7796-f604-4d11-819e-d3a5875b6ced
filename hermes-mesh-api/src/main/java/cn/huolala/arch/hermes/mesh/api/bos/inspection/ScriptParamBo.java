package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import lombok.Data;

import java.util.Date;

@Data
public class ScriptParamBo extends Bo {

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 组件
     */
    private InspectionComponent component;

    /**
     * 备注
     */
    private String memo;

    /**
     * 创建时间
     */
    protected Date createdAt;

    /**
     * 更新时间
     */
    protected Date updatedAt;
}
