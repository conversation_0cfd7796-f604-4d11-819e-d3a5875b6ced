package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionComponent;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionType;
import cn.hutool.core.lang.TypeReference;
import lombok.Data;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Data
public class MeterReportBo extends ItemReportBo {

    /**
     * 指标id
     */
    private static final String ID_KEY = "id";

    /**
     * 指标描述
     */
    private static final String MEMO_KEY = "memo";

    /**
     * 组件
     */
    private static final String COMPONENT_KEY ="component";

    /**
     * ITEMS 子项检查列表
     */
    private static final String ITEMS = "items";

    /**
     * 字表类型
     */
    private static final String TYPE = "type";

    /**
     * 应急动作
     */
    private static final String ACTION = "action";


    /**
     * 获取指标ID
     */
    public Long getId() {
        return getCast(ID_KEY,Long.class);
    }

    /**
     * 设置指标ID
     */
    public void setId(Long id) {
        put(ID_KEY, id);
    }

    /**
     * 获取指标描述
     */
    public String getMemo() {
        return (String) get(MEMO_KEY);
    }

    /**
     * 设置指标描述
     */
    public void setMemo(String memo) {
        put(MEMO_KEY, memo);
    }

    /**
     * 获取组件
     */
    public InspectionComponent getComponent() {
        return getCast(COMPONENT_KEY,InspectionComponent.class);
    }

    /**
     * 设置组件
     */
    public void setComponent(InspectionComponent component) {
        put(COMPONENT_KEY,component);
    }

    /**
     * 获取子项列表
     */
    public List<ItemReportBo> getItems() {
        return getCast(ITEMS, new TypeReference<>() {});
    }

    /**
     * 设置子项列表
     */
    public void putItems(ItemReportBo itemReportBo) {
        getOrInit(ITEMS).add(itemReportBo);
    }

    /**
     * 获取指标类型
     */
    public InspectionType getType() {
        return getCast(TYPE,InspectionType.class);
    }

    /**
     * 设置指标类型
     */
    public void setType(InspectionType type) {
        put(TYPE,type);
    }

    /**
     * 设置应急动作
     */
    public void setAction(String action) {
        put(ACTION,action);
    }

    /**
     * 获取应急动作
     */
    public String getAction() {
        return getCast(ACTION,String.class);
    }


    public static MeterReportBo of(Map<String,Object> meters) {
        MeterReportBo meterReportBo = new MeterReportBo();
        meterReportBo.putAll(meters);
        return meterReportBo;
    }
}
