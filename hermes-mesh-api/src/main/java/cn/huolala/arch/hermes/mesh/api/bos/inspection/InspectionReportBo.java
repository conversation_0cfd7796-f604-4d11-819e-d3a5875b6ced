package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus;
import cn.huolala.arch.hermes.mesh.common.model.ParamContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class InspectionReportBo extends Bo {

    /**
     * 报告Id
     */
    private Long id;

    /**
     *  状态
     */
    private InspectionMeterStatus status;

    /**
     * 组件列表
     */
    private List<ComponentReportBo> components = new ArrayList<>();

    /**
     * 自检参数
     */
    private ParamContext paramContext;

    /**
     * AI总结
     */
    private AiInspectionReportBo aiInspectionReport;


    /**
     * 报告时间
     */
    private Date createdAt;

}
