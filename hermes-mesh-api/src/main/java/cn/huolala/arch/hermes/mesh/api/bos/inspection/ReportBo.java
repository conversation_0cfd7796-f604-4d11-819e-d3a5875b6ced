package cn.huolala.arch.hermes.mesh.api.bos.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.InspectionMeterStatus;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.lang.TypeReference;
import lombok.Data;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public abstract class ReportBo extends HashMap<String,Object> {

    /**
     * 状态
     */
    private static final String STATUS_KEY = "status";

    /**
     * 详情
     */
    private static final String DETAILS_KEY = "details";

    /**
     * 额外数据
     */
    private static final String EXTRA_KEY = "extra";


    /**
     * 获取指标状态
     */
    public InspectionMeterStatus getStatus() {
        return getCast(STATUS_KEY,InspectionMeterStatus.class);
    }

    /**
     * 设置指标状态
     */
    public void setStatus(InspectionMeterStatus meterStatus) {
        put(STATUS_KEY, meterStatus);
    }

    /**
     * 获取指标详情
     */
    public String getDetails() {
        return (String) get(DETAILS_KEY);
    }

    /**
     * 设置指标详情
     */
    public void setDetails(String details) {
        put(DETAILS_KEY, details);
    }

    /**
     * 获取额外数据
     */
    public Map<String,Object> getExtra() {
        return getCast(EXTRA_KEY, Map.class);
    }

    /**
     * 设置额外数据
     */
    public void setExtra(Map<String,Object> extra){
        put(EXTRA_KEY,extra);
    }

    /**
     * 添加额外数据
     */
    public void putExtra(String key,Object value){
        HashMap<String, Object> extraMap = (HashMap<String, Object>) getExtra();
        if (extraMap == null){
            extraMap = new HashMap<>();
            setExtra(extraMap);
        }
        extraMap.put(key,value);
    }

    /**
     * 获取list 如果不存在则初始化
     */
    protected <T> List<T> getOrInit(String key){
        Object value = get(key);
        if (Objects.isNull(value)){
            value =  new ArrayList<T>();
            put(key,value);
        }
        return (List<T>) value;
    }


    /**
     * 获取并自动转化类型
     */
    protected <T> T getCast(String key,Class<T> clazz){
        return Convert.convert(clazz,get(key));
    }

    protected <T> T getCast(String key,TypeReference<T> reference) {
        return Convert.convert(reference,get(key));
    }

}
