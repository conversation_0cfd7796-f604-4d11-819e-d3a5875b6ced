<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.huolala.arch.hermes</groupId>
		<artifactId>hll-hermes-mesh</artifactId>
		<version>${revision}</version>
	</parent>
	<artifactId>hermes-mesh-api</artifactId>


	<dependencies>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-api</artifactId>
			<version>${hermes.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>hermes-mesh-common</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
			<version>${mybatis-plus-starter}</version>
			<exclusions>
				<exclusion>
					<groupId>p6spy</groupId>
					<artifactId>p6spy</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>cn.huolala.arch</groupId>
			<artifactId>hll-open-config-client</artifactId>
			<version>${open.config.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>cn.huolala.arch.hermes</groupId>
			<artifactId>mesh-component-open-monitor</artifactId>
			<version>${revision}</version>
		</dependency>
	</dependencies>

</project>
