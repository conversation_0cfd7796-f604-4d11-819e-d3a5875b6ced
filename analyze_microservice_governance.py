#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务治理工作成果分析脚本
分析稳定性事件和SOA admin操作记录的关联性
"""

import json
from datetime import datetime, timedelta
import re
from collections import defaultdict

def load_json_data(file_path):
    """加载JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    # 处理嵌套数组的情况
    if isinstance(data, list) and len(data) > 0:
        if isinstance(data[0], list):
            return data[0]  # 稳定性事件的格式
        else:
            return data  # 操作记录的格式
    return data

def parse_time(time_str):
    """解析时间字符串"""
    if isinstance(time_str, (int, float)):
        # Excel时间戳转换
        base_date = datetime(1900, 1, 1)
        return base_date + timedelta(days=time_str - 2)
    
    if isinstance(time_str, str):
        try:
            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        except:
            try:
                return datetime.strptime(time_str, '%Y-%m-%d %H:%M')
            except:
                return None
    return None

def classify_stability_events(events):
    """对稳定性事件进行分类"""
    categories = {
        '微服务问题': [],  # 可以通过SOA admin治理手段影响的问题
        '容器问题': [],
        '网络问题': [],
        '基础设施问题': [],
        '第三方问题': [],
        '配置问题': [],
        '代码问题': [],
        '运营问题': [],
        '其他问题': []
    }

    for event in events:
        event_id = event.get('ID')
        event_desc = event.get('事件描述', '')
        root_cause = event.get('根因类型', '')
        event_cause = event.get('事件原因', '')
        responsible_dept = event.get('责任部门', '')
        app_id = event.get('appid', '')

        # 微服务问题：专注于RPC服务调用相关的问题，可以通过SOA admin治理手段影响
        # 主要包括：服务异常、RPC调用超时、服务RT上涨、服务异常上涨等
        is_microservice_problem = False

        # 1. 明确包含服务名称的事件（-svc, -api结尾的服务）
        if any(keyword in event_desc.lower() for keyword in [
            '-svc', '-api', 'service', 'facade'
        ]):
            is_microservice_problem = True

        # 2. 明确的RPC服务调用问题
        elif any(keyword in event_desc.lower() for keyword in [
            'rpc', '调用', '服务异常', '服务抖动', 'rt上涨', 'rt增长', 'exception',
            '超时', 'timeout', '服务', 'svc异常', 'api异常'
        ]) and not any(exclude in event_desc.lower() for exclude in [
            'http', 'cdn', '地图', '支付宝', '微信', '百度', '阿里云', '腾讯云'
        ]):
            is_microservice_problem = True

        # 3. 有明确的微服务appid
        elif app_id and any(suffix in app_id.lower() for suffix in [
            '-svc', '-api', 'service'
        ]) and 'demo' not in app_id.lower():
            is_microservice_problem = True

        # 4. 明确的微服务相关根因
        elif any(keyword in root_cause.lower() for keyword in [
            '服务', 'rpc', '调用', '接口超时', '服务异常'
        ]):
            is_microservice_problem = True

        if is_microservice_problem:
            categories['微服务问题'].append(event)
        # 容器问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
               for keyword in ['容器', 'container', 'docker', 'k8s', 'kubernetes', 'pod', 'cpu', '内存', '磁盘']):
            categories['容器问题'].append(event)
        # 网络问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['网络', 'network', 'cdn', 'dns', 'cloudflare', '专线', '丢包']):
            categories['网络问题'].append(event)
        # 基础设施问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['基础设施', '云资源', 'rds', 'redis', '机房', '宕机', 'ecs', 'lvs', '故障']):
            categories['基础设施问题'].append(event)
        # 第三方问题
        elif ('第三方' in responsible_dept or
              any(keyword in root_cause.lower() or keyword in event_desc.lower() for keyword in [
                  '第三方', '外部服务', '运营商', '阿里云', '腾讯云', 'aws', '百度', '微信', '支付宝'
              ])):
            categories['第三方问题'].append(event)
        # 配置问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() or keyword in event_cause.lower()
                for keyword in ['配置', 'config', 'apollo', '策略', '误删', '误操作']):
            categories['配置问题'].append(event)
        # 代码问题
        elif any(keyword in root_cause.lower() or keyword in event_desc.lower() for keyword in ['代码', 'bug', 'code', '埋点', '数据量缺失']):
            categories['代码问题'].append(event)
        # 运营问题
        elif any(keyword in root_cause.lower() or keyword in responsible_dept.lower() or keyword in event_desc.lower()
                for keyword in ['运营', '产品', '客诉', '审核', '工单']):
            categories['运营问题'].append(event)
        else:
            categories['其他问题'].append(event)

    return categories

def analyze_microservice_events_with_soa_operations(microservice_events, soa_operations):
    """分析微服务问题事件与SOA admin操作的关联"""
    results = []
    
    for event in microservice_events:
        event_id = event.get('ID')
        event_time = parse_time(event.get('发生时间'))
        event_desc = event.get('事件描述', '')
        app_id = event.get('appid', '')
        
        if not event_time:
            continue
            
        # 查找事件发生前后2小时内的SOA操作
        time_window_start = event_time - timedelta(hours=2)
        time_window_end = event_time + timedelta(hours=2)
        
        related_operations = []
        for op in soa_operations:
            op_time = parse_time(op.get('操作时间'))
            if op_time and time_window_start <= op_time <= time_window_end:
                # 检查是否与同一个服务相关
                op_app_id = op.get('app_id', '')
                if app_id and op_app_id and (app_id == op_app_id or app_id in op_app_id or op_app_id in app_id):
                    related_operations.append(op)
                else:
                    # 即使不是同一服务，也记录时间窗口内的操作
                    related_operations.append(op)
        
        # 分析是否有治理操作
        governance_actions = []
        for op in related_operations:
            op_type = op.get('操作类型', '')
            if op_type in ['DEGRADE', 'TIMEOUT', 'LIMIT']:
                governance_actions.append(op)
        
        # 判断是否使用了控制面进行治理
        used_governance = len(governance_actions) > 0
        
        # 判断是否可以使用但没有操作
        could_use_but_didnt = not used_governance and len(related_operations) == 0
        
        analysis_result = {
            'event_id': event_id,
            'event_desc': event_desc,
            'event_time': event_time.strftime('%Y-%m-%d %H:%M:%S') if event_time else '',
            'app_id': app_id,
            'used_governance': used_governance,
            'could_use_but_didnt': could_use_but_didnt,
            'governance_actions': governance_actions,
            'related_operations': related_operations,
            'analysis': self._generate_analysis(event, governance_actions, related_operations)
        }
        
        results.append(analysis_result)
    
    return results

def _generate_analysis(event, governance_actions, related_operations):
    """生成具体分析情况"""
    analysis = []
    
    event_desc = event.get('事件描述', '')
    event_time = event.get('发生时间', '')
    recovery_time = event.get('恢复时间', '')
    
    analysis.append(f"事件描述: {event_desc}")
    analysis.append(f"发生时间: {event_time}")
    analysis.append(f"恢复时间: {recovery_time}")
    
    if governance_actions:
        analysis.append(f"\n✅ 使用了SOA admin控制面进行服务治理:")
        for action in governance_actions:
            op_type = action.get('操作类型', '')
            op_time = action.get('操作时间', '')
            op_user = action.get('user_id', '')
            op_app = action.get('app_id', '')
            op_detail = action.get('操作详情', '')
            
            analysis.append(f"  - {op_type}操作: {op_time} by {op_user}")
            analysis.append(f"    服务: {op_app}")
            analysis.append(f"    详情: {op_detail}")
    else:
        analysis.append(f"\n❌ 未发现使用SOA admin控制面进行服务治理")
        
        if related_operations:
            analysis.append(f"时间窗口内有其他操作但非治理操作:")
            for op in related_operations[:3]:  # 只显示前3个
                analysis.append(f"  - {op.get('操作类型', '')}: {op.get('操作时间', '')}")
        else:
            analysis.append(f"时间窗口内无任何SOA admin操作记录")
    
    return '\n'.join(analysis)

def main():
    """主函数"""
    print("开始分析微服务治理工作成果...")

    # 加载数据
    print("加载稳定性事件数据...")
    stability_events = load_json_data('test/noc稳定性事件24.07.10-25.07.01.json')

    print("加载货运SOA admin操作记录...")
    freight_operations = load_json_data('test/soa admin货运操作记录.json')

    print("加载小拉SOA admin操作记录...")
    xiaola_operations = load_json_data('test/soa admin小拉操作记录.json')

    # 合并两个环境的操作记录
    all_operations = freight_operations + xiaola_operations

    print(f"稳定性事件数量: {len(stability_events)}")
    print(f"货运环境操作记录数量: {len(freight_operations)}")
    print(f"小拉环境操作记录数量: {len(xiaola_operations)}")
    print(f"总操作记录数量: {len(all_operations)}")

    # 对稳定性事件进行分类
    print("\n对稳定性事件进行分类...")
    event_categories = classify_stability_events(stability_events)

    print("\n=== 稳定性事件分类结果 ===")
    for category, events in event_categories.items():
        print(f"{category}: {len(events)}个事件")
        for event in events:
            print(f"  - ID {event.get('ID')}: {event.get('事件描述', '')}")

    # 分析微服务问题类型事件
    microservice_events = event_categories['微服务问题']
    print(f"\n开始分析{len(microservice_events)}个微服务问题事件...")

    # 分析每个微服务事件
    analysis_results = []
    for event in microservice_events:
        event_id = event.get('ID')
        event_time = parse_time(event.get('发生时间'))
        event_desc = event.get('事件描述', '')
        app_id = event.get('appid', '')

        if not event_time:
            continue

        # 查找事件发生到恢复时间+1小时内的SOA操作（更合理的时间窗口）
        recovery_time = parse_time(event.get('恢复时间'))
        if recovery_time:
            time_window_start = event_time - timedelta(hours=1)  # 事件发生前1小时
            time_window_end = recovery_time + timedelta(hours=1)  # 恢复后1小时
        else:
            time_window_start = event_time - timedelta(hours=1)
            time_window_end = event_time + timedelta(hours=3)  # 如果没有恢复时间，则事件后3小时

        related_operations = []
        for op in all_operations:
            op_time = parse_time(op.get('created_at') or op.get('updated_at'))
            if op_time and time_window_start <= op_time <= time_window_end:
                # 过滤demo类型的服务
                op_app_id = op.get('app_id', '')
                if 'demo' in op_app_id.lower():
                    continue

                # 如果事件有明确的appid，优先匹配相同或相关的服务
                if app_id:
                    # 精确匹配或包含关系
                    if (app_id == op_app_id or
                        app_id in op_app_id or
                        op_app_id in app_id or
                        # 同一服务族的匹配（如bme-trade-xxx系列）
                        (len(app_id.split('-')) >= 2 and len(op_app_id.split('-')) >= 2 and
                         '-'.join(app_id.split('-')[:2]) == '-'.join(op_app_id.split('-')[:2]))):
                        related_operations.append(op)
                else:
                    # 没有明确appid的事件，记录时间窗口内的所有治理操作
                    related_operations.append(op)

        # 分析是否有治理操作
        governance_actions = []
        for op in related_operations:
            op_type = op.get('type', '')
            if op_type in ['DEGRADE', 'TIMEOUT', 'LIMIT']:
                governance_actions.append(op)

        # 生成分析
        analysis = generate_analysis(event, governance_actions, related_operations)

        analysis_results.append({
            'event_id': event_id,
            'event_desc': event_desc,
            'analysis': analysis
        })

    # 输出分析结果
    print("\n=== 微服务问题事件分析结果 ===")
    used_governance_count = 0
    not_used_count = 0

    for result in analysis_results:
        if '✅ 使用了SOA admin控制面进行服务治理' in result['analysis']:
            used_governance_count += 1
        else:
            not_used_count += 1

        print(f"\n事件ID {result['event_id']}:")
        print(result['analysis'])
        print("-" * 80)

    print(f"\n=== 统计汇总 ===")
    print(f"微服务问题事件总数: {len(analysis_results)}")
    print(f"使用了SOA admin治理: {used_governance_count}个 ({used_governance_count/len(analysis_results)*100:.1f}%)")
    print(f"未使用治理: {not_used_count}个 ({not_used_count/len(analysis_results)*100:.1f}%)")

    # 保存详细分析结果到文件
    save_detailed_analysis_to_file(analysis_results, event_categories)

def generate_analysis(event, governance_actions, related_operations):
    """生成具体分析情况"""
    analysis = []

    event_desc = event.get('事件描述', '')
    event_time_str = event.get('发生时间', '')
    recovery_time_str = event.get('恢复时间', '')
    business_line = event.get('影响业务线', '')
    app_id = event.get('appid', '')

    event_time = parse_time(event_time_str)
    recovery_time = parse_time(recovery_time_str)

    analysis.append(f"事件描述: {event_desc}")
    analysis.append(f"发生时间: {event_time_str}")
    analysis.append(f"恢复时间: {recovery_time_str}")
    analysis.append(f"影响业务线: {business_line}")
    analysis.append(f"关联appid: {app_id}")

    if governance_actions:
        # 检查治理操作的时间合理性
        valid_governance_actions = []
        invalid_governance_actions = []

        for action in governance_actions:
            op_time_str = action.get('created_at') or action.get('updated_at', '')
            op_time = parse_time(op_time_str)

            # 检查操作时间是否在合理范围内
            if op_time and event_time and recovery_time:
                # 操作应该在事件发生后且恢复前，或者恢复后很短时间内
                if (op_time >= event_time and op_time <= recovery_time + timedelta(hours=1)):
                    valid_governance_actions.append(action)
                else:
                    invalid_governance_actions.append(action)
            elif op_time and event_time:
                # 如果没有恢复时间，检查是否在事件发生后3小时内
                if op_time >= event_time and op_time <= event_time + timedelta(hours=3):
                    valid_governance_actions.append(action)
                else:
                    invalid_governance_actions.append(action)
            else:
                valid_governance_actions.append(action)  # 无法解析时间的情况

        if valid_governance_actions:
            analysis.append(f"\n✅ 使用了SOA admin控制面进行服务治理:")
            governance_summary = defaultdict(int)
            for action in valid_governance_actions:
                op_type = action.get('type', '')
                op_time = action.get('created_at') or action.get('updated_at', '')
                op_user = action.get('user_id', '')
                op_app = action.get('app_id', '')
                op_data = action.get('data', '')

                governance_summary[op_type] += 1
                analysis.append(f"  - {op_type}操作: {op_time} by {op_user} on {op_app}")
                if op_data:
                    try:
                        data_obj = json.loads(op_data)
                        if 'name' in data_obj:
                            analysis.append(f"    目标: {data_obj['name']}")
                    except:
                        pass

            analysis.append(f"\n治理操作统计:")
            for op_type, count in governance_summary.items():
                analysis.append(f"  - {op_type}: {count}次")

            if invalid_governance_actions:
                analysis.append(f"\n⚠️  注意: 发现{len(invalid_governance_actions)}个时间不合理的操作记录（可能与此事件无关）")
        else:
            analysis.append(f"\n❌ 未发现使用SOA admin控制面进行服务治理")
            if invalid_governance_actions:
                analysis.append(f"时间窗口内有{len(invalid_governance_actions)}个操作，但时间不合理（可能与此事件无关）")
    else:
        if not governance_actions:
            analysis.append(f"\n❌ 未发现使用SOA admin控制面进行服务治理")

            if related_operations:
                analysis.append(f"时间窗口内有{len(related_operations)}个其他操作，但非治理操作")
                # 显示其他操作类型
                other_ops = defaultdict(int)
                for op in related_operations:
                    other_ops[op.get('type', 'UNKNOWN')] += 1
                for op_type, count in other_ops.items():
                    analysis.append(f"  - {op_type}: {count}次")
            else:
                analysis.append(f"时间窗口内无任何SOA admin操作记录")

    # 分析是否可以使用但没有使用治理手段
    if not governance_actions:
        if any(keyword in event_desc.lower() for keyword in ['-svc', '-api', '服务异常', 'rpc', '调用']):
            analysis.append(f"\n⚠️  分析: 该事件涉及微服务RPC调用问题，理论上可以使用控制面治理手段，但实际未使用")
        elif app_id and '-svc' in app_id.lower():
            analysis.append(f"\n⚠️  分析: 该事件有明确的微服务appid({app_id})，理论上可以使用控制面治理手段，但实际未使用")

    # 分析关联性
    if app_id and related_operations:
        matched_ops = [op for op in related_operations if app_id in op.get('app_id', '') or op.get('app_id', '') in app_id]
        if matched_ops:
            analysis.append(f"\n🔗 关联性分析: 找到{len(matched_ops)}个与事件appid({app_id})直接相关的操作")
        else:
            analysis.append(f"\n🔗 关联性分析: 时间窗口内的操作与事件appid({app_id})无直接关联")

    return '\n'.join(analysis)

def save_detailed_analysis_to_file(analysis_results, event_categories):
    """保存详细分析结果到文件"""
    with open('微服务事件全量分析报告.md', 'w', encoding='utf-8') as f:
        f.write("# 微服务事件全量分析报告\n\n")
        f.write("## 分析说明\n")
        f.write("本报告基于2024年7月10日至2025年7月1日期间的稳定性事件数据和SOA admin操作记录，")
        f.write("重新定义微服务问题为：可以通过SOA admin控制面治理手段（TIMEOUT、DEGRADE、LIMIT）影响的事件。\n\n")

        # 事件分类统计
        f.write("## 稳定性事件分类统计\n\n")
        total_events = sum(len(events) for events in event_categories.values())
        for category, events in event_categories.items():
            percentage = len(events) / total_events * 100 if total_events > 0 else 0
            f.write(f"- **{category}**: {len(events)}个事件 ({percentage:.1f}%)\n")
        f.write(f"\n总计: {total_events}个事件\n\n")

        # 微服务问题事件统计
        microservice_count = len(analysis_results)
        used_count = sum(1 for result in analysis_results if '✅ 使用了SOA admin控制面进行服务治理' in result['analysis'])
        not_used_count = microservice_count - used_count

        f.write("## 微服务问题事件治理统计\n\n")
        f.write(f"- **微服务问题事件总数**: {microservice_count}个\n")
        f.write(f"- **使用了SOA admin治理**: {used_count}个 ({used_count/microservice_count*100:.1f}%)\n")
        f.write(f"- **未使用治理**: {not_used_count}个 ({not_used_count/microservice_count*100:.1f}%)\n\n")

        # 详细分析结果
        f.write("## 微服务问题事件详细分析\n\n")
        f.write("### 使用了SOA admin治理的事件\n\n")

        used_events = [result for result in analysis_results if '✅ 使用了SOA admin控制面进行服务治理' in result['analysis']]
        for i, result in enumerate(used_events, 1):
            f.write(f"#### {i}. 事件ID {result['event_id']}: {result['event_desc']}\n\n")
            f.write(f"{result['analysis']}\n\n")
            f.write("---\n\n")

        f.write("### 未使用治理的事件\n\n")
        not_used_events = [result for result in analysis_results if '❌ 未发现使用SOA admin控制面进行服务治理' in result['analysis']]
        for i, result in enumerate(not_used_events, 1):
            f.write(f"#### {i}. 事件ID {result['event_id']}: {result['event_desc']}\n\n")
            f.write(f"{result['analysis']}\n\n")
            f.write("---\n\n")

        f.write("## 总结\n\n")
        f.write("本报告提供了所有微服务问题事件的详细分析，包括治理措施的使用情况和具体效果。")
        f.write("通过重新定义微服务问题的标准，更准确地识别了可以通过控制面治理的事件类型。\n")

if __name__ == "__main__":
    main()
