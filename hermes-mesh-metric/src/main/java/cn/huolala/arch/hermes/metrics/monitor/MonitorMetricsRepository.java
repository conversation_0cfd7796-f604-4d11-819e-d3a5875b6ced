package cn.huolala.arch.hermes.metrics.monitor;

import cn.huolala.arch.hermes.metrics.AbstractMetricsRepository;
import cn.lalaframework.monitor.api.metric.registry.MonitorRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MonitorMetricsRepository extends AbstractMetricsRepository {

    private static final Logger logger = LoggerFactory.getLogger(MonitorMetricsRepository.class);

    @Override
    public void start() throws IllegalStateException {
        try {
            MeterRegistry registry = MonitorRegistry.getMeterRegistry();
            if (registry == null) {
                logger.warn("The MeterRegistry is not set, metrics will be ignored");
                return;
            }
            meterRegistry.set(registry);
        }catch (NoClassDefFoundError e){
            logger.warn("The MeterRegistry is not set, metrics will be ignored,{}",e.getMessage());
        }
    }
}
