<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hll-hermes-mesh</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hermes-mesh-rpc</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.lalaframework.boot</groupId>
            <artifactId>lala-boot-starter-soa</artifactId>
            <version>${hermes.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.lalaframework</groupId>
                    <artifactId>lala-config-apollo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.huolala.arch.hermes</groupId>
                    <artifactId>javassist-shaded</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.orbitz.consul</groupId>
                    <artifactId>consul-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.huolala.arch.hermes</groupId>
                    <artifactId>hermes-mini</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.lalaframework.whitzard</groupId>
                    <artifactId>whitzard-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.14</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-all</artifactId>
            <version>${hermes.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.huolala.tech</groupId>
                    <artifactId>hll-config-client-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.huolala.tech</groupId>
            <artifactId>hll-meta</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>javassist-shaded</artifactId>
            <version>${javassist.shaded.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo.client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.orbitz.consul</groupId>
            <artifactId>consul-client</artifactId>
            <version>${consul.huolala.version}</version>
        </dependency>
    </dependencies>
</project>
