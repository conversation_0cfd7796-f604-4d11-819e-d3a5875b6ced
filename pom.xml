<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.huolala.arch.hermes</groupId>
    <artifactId>hll-hermes-mesh</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>hll-hermes-mesh</name>

    <properties>
        <revision>2.1.0-SNAPSHOT</revision>
        <java.version>17</java.version>
        <lombok.version>1.18.20</lombok.version>
        <spring-boot.version>3.4.6</spring-boot.version>
        <retrofit.version>3.0.2</retrofit.version>
        <hutool.version>5.8.17</hutool.version>
        <nutz.version>1.r.68.v20190516</nutz.version>
        <fastjson.version>2.0.32</fastjson.version>
        <consul.api.version>1.4.5</consul.api.version>
        <guava.verion>29.0-jre</guava.verion>
        <javax.version>1.3.2</javax.version>
        <hermes.version>2.10.2.RC1</hermes.version>
        <meta.version>1.0.8.RELEASE</meta.version>
        <mysql.version>8.0.21</mysql.version>
        <cors.filter.version>2.10</cors.filter.version>
        <mybatis-plus-starter>3.5.12</mybatis-plus-starter>
        <cors.filter.version>2.10</cors.filter.version>
        <consul.client.version>1.5.3</consul.client.version>
        <open.config.version>4.4.1.RELEASE</open.config.version>
        <apollo.client.version>2.1.0</apollo.client.version>
        <vault.version>3.0.2</vault.version>
        <lark.open.api.verion>2.0.17</lark.open.api.verion>
        <apollo.lalaapi.verion>1.6.1.2</apollo.lalaapi.verion>
        <javassist.shaded.version>3.29.2-GA</javassist.shaded.version>
        <consul.huolala.version>1.5.3-huolala.0.3</consul.huolala.version>
        <ldeps.version>1.3.0-SNAPSHOT</ldeps.version>
        <easyexcel.version>3.1.4</easyexcel.version>
        <jsoup.version>1.15.3</jsoup.version>
        <dns-cache.version>1.8.1</dns-cache.version>
        <groovy-all.version>3.0.22</groovy-all.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <prometheus.version>0.16.0</prometheus.version>
        <monitor.version>hermes-2.4.2-SNAPSHOT</monitor.version>
        <shade.bytebuddy.version>hermes-1.0.0-SNAPSHOT</shade.bytebuddy.version>
        <bytebuddy.version>1.11.18</bytebuddy.version>
        <disruptor.version>3.4.4</disruptor.version>
        <spring-ai-openai.version>1.0.0</spring-ai-openai.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <flatten-maven-plugin.version>1.2.7</flatten-maven-plugin.version>
    </properties>

    <modules>
        <module>hermes-mesh-ui</module>
        <module>hermes-mesh-rpc</module>
        <module>hermes-mesh-api</module>
        <module>hermes-mesh-panel</module>
        <module>hermes-mesh-stream</module>
        <module>hermes-mesh-common</module>
        <module>hermes-mesh-monitor</module>
        <module>hermes-mesh-sentinel</module>
        <module>hermes-mesh-component</module>
        <module>hermes-mesh-dao</module>
        <module>hermes-mesh-kms</module>
        <module>hermes-mesh-compatible</module>
        <module>hermes-mesh-metric</module>
        <module>hermes-mesh-autopilot</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-starter}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.tech</groupId>
                <artifactId>hll-meta</artifactId>
                <version>${meta.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.lianjiatech</groupId>
                <artifactId>retrofit-spring-boot-starter</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-starter}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_common</artifactId>
                <version>${prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.lalaframework</groupId>
                <artifactId>lala-jaf-monitor-starter</artifactId>
                <version>${monitor.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.lalaframework.monitor</groupId>
                <artifactId>shade-byte-buddy</artifactId>
                <version>${shade.bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <parameters>true</parameters>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>


    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://maven.huolala.cn/repository/lala-release/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://maven.huolala.cn/repository/lala-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
